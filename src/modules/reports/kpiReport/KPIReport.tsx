import { Form, Formik } from 'formik'
import { SharedDate } from '../../../shared/date/SharedDate'
import {
  KPIButtonContainer,
  KPIDate,
  KPIDateContainer,
  KPIDateLabel,
  KPIReportContainer,
  KPIReportContentContainer,
  KPIReportContentDescription,
  KPIReportContentGap,
  KPIReportContentHeading,
  KPIReportContentSubHeading,
  KPIReportHeading,
  TableContainer,
  TableContent,
  TableContentLabel,
  TableHeading,
  TableMainContainer,
  TableTitle,
} from './style'
import { Fragment, useEffect, useState } from 'react'
import { getKPIReport } from '../../../logic/apis/report'
import { Link, useNavigate, useParams, useLocation } from 'react-router-dom'
import { useSelector } from 'react-redux'
import {
  checkAndReturnValue,
  dayjsFormat,
  formatNumberToCommaS,
  getDataFromLocalStorage,
  getEnumValue,
  isSuccess,
  renderClientName,
  toPascalCase,
  truncateString,
} from '../../../shared/helpers/util'
import Button from '../../../shared/components/button/Button'
import { getCheckpoint, getStages } from '../../../logic/apis/sales'
import { I_Checkpoint } from '../../crmSettings/CrmSettings'
import { I_Stage } from '../../opportunity/components/assessmentForm/AssessmentForm'
import { FlexRow, SectionTitle, SubTableContainer } from '../../../styles/styled'
import * as SharedStyled from '../../../styles/styled'
import { IButton } from '../../newProject/style'
import { StageGroupEnum, StorageKey } from '../../../shared/helpers/constants'

const KPIReport = () => {
  const [isLoading, setIsLoading] = useState(false)

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany } = globalSelector.company
  const [isKpiTableSelected, setIsKpiTableSelected] = useState<string>('')
  const [kpiData, setData] = useState<any>([])
  const [checkpoints, setCheckpoints] = useState<I_Checkpoint[]>([])
  const [stages, setStages] = useState<I_Stage[]>([])
  const [leadsReportToggle, setLeadsReportToggle] = useState<{ [key: string]: boolean }>({})
  const location = useLocation()

  const navigate = useNavigate()

  const [initialValues, setInitialValues] = useState({
    startDate: '',
    endDate: '',
  })

  useEffect(() => {
    fetchCheckpoints()
  }, [])

  useEffect(() => {
    const params = new URLSearchParams(location.search)
    const paramStartDate = params.get('startDate')
    const paramEndDate = params.get('endDate')
    if (paramStartDate && paramEndDate) {
      if (paramStartDate !== '' && paramEndDate !== '') {
        setInitialValues({ startDate: paramStartDate, endDate: paramEndDate })
        handleSubmitForm({ startDate: paramStartDate, endDate: paramEndDate })
      }
    }
  }, [location.search])

  const handleDateSelection = (selectedStartDate: string, selectedEndDate: string) => {
    const params = new URLSearchParams()
    params.append('startDate', selectedStartDate)
    params.append('endDate', selectedEndDate)
    const newURL = `${window.location.pathname}?${params.toString()}`
    window.history.pushState({}, '', newURL)
    // Perform any other actions needed when start and end dates are selected
  }

  const handleSubmitForm = async (values: any) => {
    setIsLoading(true)
    handleDateSelection(values.startDate, values.endDate)
    const paramsData: any = {
      endDate: values.endDate,
      startDate: values.startDate,
    }

    try {
      const kpiData = await getKPIReport(paramsData)
      setData(kpiData)
    } catch (error) {
      console.log('Error reports', error)
    } finally {
      setIsLoading(false)
    }
  }

  const toggleTableData = (type: string) => {
    setLeadsReportToggle((prevState) => ({
      ...prevState,
      [type]: !prevState[type],
    }))
  }

  const fetchCheckpoints = async () => {
    try {
      const response = await getCheckpoint(false, StageGroupEnum.Sales)
      if (isSuccess(response) || isSuccess(response)) {
        let checkpointsArr: I_Checkpoint[] = response.data.data.checkpoint.map((item: any) => {
          // Remove spaces and convert name to lowercase
          const updatedName = item.name.replace(/\s/g, '').toLowerCase()

          // Return the updated object
          return {
            ...item,
            name: updatedName,
          }
        })
        setCheckpoints(checkpointsArr)
      } else {
        throw new Error(response?.data.message ?? 'Something went wrong!')
      }
    } catch (err) {
      console.log('CHECKPOINTS Fetch failed', err)
    }
  }

  useEffect(() => {
    getStagesData()
  }, [])

  const getStagesData = async () => {
    try {
      const stagesRes = await getStages({}, false)
      // const stagesRes = await getStages({ companyId: currentCompany._id }, false, operationsFlag)
      if (isSuccess(stagesRes)) {
        const { stage: stages } = stagesRes.data.data
        const formatStages: I_Stage[] = new Array(stages.length)
        stages.forEach((stage: I_Stage) => {
          formatStages[stage.sequence - 1] = stage
        })
        setStages(stages)
        // setStages(formatStages)
      } else throw new Error(stagesRes?.data?.message)
    } catch (err) {
      console.log('Err stages data', err)
    }
  }

  const conversionRate = (num1: number, num2: number) => {
    if (num2 === 0) {
      return 0
    }
    const num = (num1 / num2) * 100
    return num.toFixed(1)
  }

  const calculateDivisionValues = (obj: any) => {
    const keys = Object.keys(obj)
    const divisionValues: any = {}

    keys.forEach((key, index) => {
      let divisionResults: any = {}
      for (let i = 0; i < index; i++) {
        // if (obj[keys[i]].num !== 0) {
        const result = conversionRate(obj[key].num, obj[keys[i]].num)
        const sourceKey = keys[i]
        divisionResults[sourceKey.toLocaleLowerCase()] = Number(result).toFixed(1)
        // }
      }
      divisionValues[key.toLocaleLowerCase()] = divisionResults
    })

    return divisionValues
  }

  return (
    <KPIReportContainer>
      <SectionTitle>KPI Report</SectionTitle>
      <Formik enableReinitialize={true} initialValues={initialValues} onSubmit={handleSubmitForm}>
        {({ values, setFieldValue, touched, errors }) => (
          <Form>
            <KPIDateContainer>
              <div>
                <KPIDateLabel>Date Start:</KPIDateLabel>
                <KPIDate>
                  <SharedDate
                    value={values.startDate}
                    labelName="From"
                    stateName="startDate"
                    error={touched.startDate && errors.startDate ? true : false}
                    setFieldValue={setFieldValue}
                  />
                </KPIDate>
              </div>
              <div>
                <KPIDateLabel>Date End:</KPIDateLabel>
                <KPIDate>
                  <SharedDate
                    value={values.endDate}
                    labelName="To"
                    stateName="endDate"
                    error={touched.endDate && errors.endDate ? true : false}
                    min={values.startDate}
                    setFieldValue={setFieldValue}
                  />
                </KPIDate>
              </div>
              <KPIButtonContainer>
                <Button disabled={values.endDate === '' || values.startDate === ''} isLoading={isLoading}>
                  Run Report
                </Button>
              </KPIButtonContainer>
            </KPIDateContainer>
          </Form>
        )}
      </Formik>
      <div>
        {kpiData?.length !== 0 && (
          <div>
            <KPIReportContentContainer>
              <div>
                <KPIReportHeading style={{ margin: '12px 0 0 0' }}> Sales </KPIReportHeading>
                {Object.keys(kpiData?.checkpoints)
                  ?.sort((a, b) => kpiData?.checkpoints[a].sequence - kpiData?.checkpoints[b].sequence)
                  ?.map((key: string, indexCheckPoint: number) => {
                    const checkpoint = kpiData?.checkpoints[key]
                    const conversionRate = calculateDivisionValues(
                      Object.fromEntries(
                        Object.entries(kpiData?.checkpoints).sort(
                          ([_keyA, valueA], [_keyB, valueB]) => valueA.sequence - valueB.sequence
                        )
                      )
                      // sortObjectKeysBySequence(checkpoints, kpiData?.checkpoints)
                    )
                    return (
                      <Fragment key={indexCheckPoint}>
                        {checkpoint?.symbol === 'saleDate' ? (
                          <div>
                            <>
                              <KPIReportContentGap>
                                <KPIReportContentHeading onClick={() => setIsKpiTableSelected(key)}>
                                  {key}: {checkpoint?.num} | Volume: ${formatNumberToCommaS(checkpoint?.vol)}
                                </KPIReportContentHeading>
                              </KPIReportContentGap>
                              <KPIReportContentGap>
                                {checkpoint?.types?.map((type: any, indexS: number) => {
                                  return (
                                    <KPIReportContentDescription key={indexS}>
                                      {type?.name}: {type?.num} | Volume: ${formatNumberToCommaS(type?.vol)}
                                      <br />
                                    </KPIReportContentDescription>
                                  )
                                })}
                                {Object.keys(conversionRate[key.toLocaleLowerCase()]).length > 0 && (
                                  <ul>
                                    {Object.keys(conversionRate[key.toLocaleLowerCase()]).map(
                                      (keyC, indexSC: number) => {
                                        const obj = conversionRate[key.toLocaleLowerCase()]
                                        return (
                                          <li key={indexSC}>
                                            {keyC?.toLocaleLowerCase()} {'>'} {key?.toLocaleLowerCase()}: {obj[keyC]}%
                                          </li>
                                        )
                                      }
                                    )}
                                  </ul>
                                )}
                              </KPIReportContentGap>
                            </>
                          </div>
                        ) : (
                          <div>
                            <KPIReportContentGap>
                              <KPIReportContentHeading onClick={() => setIsKpiTableSelected(key)}>
                                {key}: {checkpoint?.num}
                              </KPIReportContentHeading>
                            </KPIReportContentGap>
                            <KPIReportContentGap>
                              {Object.keys(conversionRate[key.toLocaleLowerCase()]).length >= 0 && (
                                <ul>
                                  {Object.keys(conversionRate[key.toLocaleLowerCase()]).map((keyC, indexSC: number) => {
                                    const obj = conversionRate[key.toLocaleLowerCase()]
                                    return (
                                      <li key={indexSC}>
                                        {keyC?.toLocaleLowerCase()} {'>'} {key?.toLocaleLowerCase()}: {obj[keyC]}%
                                      </li>
                                    )
                                  })}
                                </ul>
                              )}
                            </KPIReportContentGap>
                          </div>
                        )}
                      </Fragment>
                    )
                  })}
                <div style={{ margin: '0 0 20px 0' }}>
                  <KPIReportContentGap>
                    <KPIReportContentHeading onClick={() => setIsKpiTableSelected('Leads Report')}>
                      Leads Report
                    </KPIReportContentHeading>
                  </KPIReportContentGap>
                </div>
              </div>

              <div style={{ margin: '0 0 20px 0' }}>
                <KPIReportContentGap>
                  <KPIReportHeading onClick={() => setIsKpiTableSelected('Completed Repairs')}>
                    {' '}
                    Operations{' '}
                  </KPIReportHeading>
                  <FlexRow margin="8px 0">
                    <KPIReportContentSubHeading onClick={() => setIsKpiTableSelected('Completed Repairs')}>
                      Jobs Done: {kpiData?.completed?.num} | Volume: ${formatNumberToCommaS(kpiData?.completed?.vol)}
                    </KPIReportContentSubHeading>
                  </FlexRow>
                  {kpiData?.completed?.types?.map((type: any, index: number) => {
                    return (
                      <KPIReportContentDescription key={index}>
                        {type?.name}: {type?.num} | Volume: ${formatNumberToCommaS(type?.vol)}
                      </KPIReportContentDescription>
                    )
                  })}
                </KPIReportContentGap>
              </div>
            </KPIReportContentContainer>

            {isKpiTableSelected === 'Leads Report' && (
              <>
                <KPIReportHeading>{'Leads Report'}</KPIReportHeading>
                <TableContainer>
                  <TableHeading column="2fr repeat(9,1fr)">
                    <TableTitle>Source</TableTitle>
                    <TableTitle># Leads</TableTitle>
                    <TableTitle>Ttl Cost</TableTitle>
                    <TableTitle># Opps</TableTitle>
                    <TableTitle>Cost/Opp</TableTitle>
                    <TableTitle># Sales</TableTitle>
                    <TableTitle className="right-align">Volume</TableTitle>
                    <TableTitle className="right-align">Real Rev</TableTitle>
                    <TableTitle className="right-align">Cost/Sale</TableTitle>
                    <TableTitle className="right-align">RR/Lead</TableTitle>
                  </TableHeading>
                  {kpiData?.checkpoints?.[`${stages?.[0]?.name}`]?.leadSources?.map((value: any, iP: number) => (
                    <Fragment key={iP}>
                      <tr>
                        <TableContent
                          column="2fr repeat(9,1fr)"
                          pointer="pointer"
                          onClick={() => toggleTableData(value?.id)}
                        >
                          <TableContentLabel> {value?.id}</TableContentLabel>
                          <TableContentLabel>{value?.num}</TableContentLabel>
                          <TableContentLabel>{value?.cost}</TableContentLabel>
                          <TableContentLabel>{value?.opps}</TableContentLabel>
                          <TableContentLabel>{value?.costPerOpp}</TableContentLabel>
                          <TableContentLabel>{value?.sales}</TableContentLabel>
                          <TableContentLabel className="right-align">
                            ${formatNumberToCommaS(value?.volume) || 0}
                          </TableContentLabel>
                          <TableContentLabel className="right-align">
                            ${formatNumberToCommaS(value?.realRev) || 0}
                          </TableContentLabel>
                          <TableContentLabel className="right-align">
                            ${formatNumberToCommaS(value?.costPerSale) || 0}
                          </TableContentLabel>
                          <TableContentLabel className="right-align">
                            ${formatNumberToCommaS(value?.rrPerLead) || 0}
                          </TableContentLabel>
                        </TableContent>
                      </tr>
                      {leadsReportToggle[value?.id] && (
                        <SubTableContainer margin="0 0 10px auto" width="95%">
                          <KPIDateLabel>{value?.id} Leads Breakdown</KPIDateLabel>
                          <TableContainer>
                            <TableHeading column="2fr repeat(8,1fr)">
                              <TableTitle>Name</TableTitle>
                              <TableTitle>PO#</TableTitle>
                              <TableTitle>City</TableTitle>
                              <TableTitle>
                                <SharedStyled.TooltipContainer
                                  width="250px"
                                  positionLeft="0"
                                  positionBottom="0"
                                  positionLeftDecs="0px"
                                  positionBottomDecs="20px"
                                >
                                  <span className="tooltip-content">{'New Lead'}</span>
                                  <div>{'New L'}</div>
                                </SharedStyled.TooltipContainer>
                              </TableTitle>
                              <TableTitle>
                                <SharedStyled.TooltipContainer
                                  width="250px"
                                  positionLeft="0"
                                  positionBottom="0"
                                  positionLeftDecs="0px"
                                  positionBottomDecs="20px"
                                >
                                  <span className="tooltip-content">Needs Assessment</span>
                                  <div>{'Needs A'}</div>
                                </SharedStyled.TooltipContainer>
                              </TableTitle>
                              <TableTitle>
                                <SharedStyled.TooltipContainer
                                  width="250px"
                                  positionLeft="0"
                                  positionBottom="0"
                                  positionLeftDecs="0px"
                                  positionBottomDecs="20px"
                                >
                                  <span className="tooltip-content">Presentation</span>
                                  <div>{'Prese'}</div>
                                </SharedStyled.TooltipContainer>
                              </TableTitle>
                              <TableTitle>Sale</TableTitle>
                              <TableTitle className="right-align">Volume</TableTitle>
                              <TableTitle className="right-align">Real Rev</TableTitle>
                            </TableHeading>
                            {value?.oppData?.map((opp: any, i: number) => {
                              const matchedObject: any = stages.find((item) => item._id === opp?.stage)
                              return (
                                <Fragment key={i}>
                                  <tr>
                                    <TableContent
                                      as={Link}
                                      to={`/${getEnumValue(matchedObject?.stageGroup)}/opportunity/${opp?._id}`}
                                      key={opp?._id}
                                      column="2fr repeat(8,1fr)"
                                      pointer="pointer"
                                      // onClick={() => getpageById(opp?.stage, opp?._id)}
                                    >
                                      <TableContentLabel>
                                        {renderClientName(
                                          opp?.contactId?.isBusiness,
                                          opp?.contactId?.fullName,
                                          opp?.contactId?.businessName
                                        )}
                                      </TableContentLabel>
                                      <TableContentLabel>
                                        {opp?.PO}-{opp?.num}
                                      </TableContentLabel>
                                      <TableContentLabel>{opp?.city}</TableContentLabel>
                                      <TableContentLabel>
                                        {dayjsFormat(opp?.oppDate, 'M/D/YY') || '--'}
                                      </TableContentLabel>
                                      <TableContentLabel>
                                        {dayjsFormat(opp?.needsAssessmentDate, 'M/D/YY') || '--'}
                                      </TableContentLabel>
                                      <TableContentLabel>
                                        {dayjsFormat(opp?.presentationDate, 'M/D/YY') || '--'}
                                      </TableContentLabel>
                                      <TableContentLabel>
                                        {dayjsFormat(opp?.saleDate, 'M/D/YY') || '--'}
                                      </TableContentLabel>
                                      <TableContentLabel className="right-align">
                                        ${formatNumberToCommaS(opp?.soldValue) || 0}
                                      </TableContentLabel>
                                      <TableContentLabel className="right-align">
                                        ${formatNumberToCommaS(opp?.realRevValue) || 0}
                                      </TableContentLabel>
                                    </TableContent>
                                  </tr>
                                  <div></div>
                                </Fragment>
                              )
                            })}
                          </TableContainer>
                        </SubTableContainer>
                      )}
                    </Fragment>
                  ))}
                </TableContainer>
              </>
            )}

            {isKpiTableSelected === 'Completed Repairs' && (
              <>
                {kpiData?.completed?.types?.map((type: any, indexComplete: number) => {
                  return type?.typeReplacement ? (
                    <Fragment key={indexComplete}>
                      <KPIReportHeading>Completed {type?.name}</KPIReportHeading>
                      <TableContainer>
                        <TableHeading column="repeat(8,2fr) 1fr repeat(2,2fr)">
                          <TableTitle>Name</TableTitle>
                          <TableTitle>PO#</TableTitle>
                          <TableTitle>City</TableTitle>
                          <TableTitle>Sub</TableTitle>
                          <TableTitle>Source</TableTitle>
                          <TableTitle>Completed</TableTitle>
                          <TableTitle className="right-align">Volume</TableTitle>
                          <TableTitle className="right-align">RealRev</TableTitle>
                          <TableTitle className="right-align">SQ</TableTitle>
                          <TableTitle className="right-align">vol/SQ</TableTitle>
                          <TableTitle className="right-align">RR/SQ</TableTitle>
                        </TableHeading>
                        {type?.opps
                          ?.sort((a: any, b: any) => new Date(a?.jobCompletedDate) - new Date(b?.jobCompletedDate))
                          ?.map((value: any, i: number) => {
                            const matchedObject: any = stages.find((item) => item._id === value?.stage)
                            return (
                              <tr>
                                <TableContent
                                  as={Link}
                                  to={`/${getEnumValue(matchedObject?.stageGroup)}/opportunity/${value?._id}`}
                                  key={i}
                                  column="repeat(8,2fr) 1fr repeat(2,2fr)"
                                  pointer="pointer"
                                  // onClick={() => getpageById(value?.stage, value?._id)}
                                >
                                  <TableContentLabel>
                                    {renderClientName(
                                      value?.contactId?.isBusiness,
                                      value?.contactId?.fullName,
                                      value?.contactId?.businessName
                                    )}
                                  </TableContentLabel>
                                  <TableContentLabel>
                                    {value?.PO}-{value?.num}
                                  </TableContentLabel>
                                  <TableContentLabel>{value?.city}</TableContentLabel>
                                  <TableContentLabel>
                                    {/* {value?.['Ready To Start']?.checklists?.['sub-crew-assigned-result']?.value} */}
                                    {value?.workingCrew?.name}
                                  </TableContentLabel>
                                  <TableContentLabel>{value?.leadSource}</TableContentLabel>
                                  <TableContentLabel>
                                    {dayjsFormat(value?.jobCompletedDate, 'M/D/YY')}
                                  </TableContentLabel>
                                  <TableContentLabel className="right-align">
                                    ${formatNumberToCommaS(value?.soldValue) || 0}
                                  </TableContentLabel>
                                  <TableContentLabel className="right-align">
                                    ${formatNumberToCommaS(value?.realRevValue) || 0}
                                  </TableContentLabel>
                                  <TableContentLabel className="right-align">
                                    {value?.squares?.toFixed(1)}
                                  </TableContentLabel>
                                  <TableContentLabel className="right-align">
                                    ${formatNumberToCommaS(value?.volSQ) || 0}
                                  </TableContentLabel>
                                  <TableContentLabel className="right-align">
                                    ${formatNumberToCommaS(value?.RRSQ) || 0}
                                  </TableContentLabel>
                                </TableContent>
                              </tr>
                            )
                          })}
                      </TableContainer>
                    </Fragment>
                  ) : (
                    <Fragment key={indexComplete}>
                      <KPIReportHeading>Completed {type?.name}</KPIReportHeading>

                      <TableContainer>
                        <TableHeading column="repeat(8,1fr)">
                          <TableTitle>Name</TableTitle>
                          <TableTitle>PO#</TableTitle>
                          <TableTitle>City</TableTitle>
                          <TableTitle>Sub</TableTitle>
                          <TableTitle>Source</TableTitle>
                          <TableTitle>Completed</TableTitle>
                          <TableTitle className="right-align">Volume</TableTitle>
                          <TableTitle className="right-align">RealRev</TableTitle>
                        </TableHeading>
                        {type?.opps
                          ?.sort((a: any, b: any) => new Date(a?.jobCompletedDate) - new Date(b?.jobCompletedDate))
                          ?.map((value: any, i: number) => {
                            const matchedObject: any = stages.find((item) => item._id === value?.stage)
                            return (
                              <tr>
                                <TableContent
                                  as={Link}
                                  to={`/${getEnumValue(matchedObject?.stageGroup)}/opportunity/${value?._id}`}
                                  key={value?._id}
                                  column="repeat(8,1fr)"
                                  pointer="pointer"
                                  key={i}
                                  // onClick={() => getpageById(value?.stage, value?._id)}
                                >
                                  <TableContentLabel>
                                    {renderClientName(
                                      value?.contactId?.isBusiness,
                                      value?.contactId?.fullName,
                                      value?.contactId?.businessName
                                    )}
                                    {/* {value?.firstName}, {value?.lastName} */}
                                  </TableContentLabel>
                                  <TableContentLabel>
                                    {value?.PO}-{value?.num}
                                  </TableContentLabel>
                                  <TableContentLabel>{value?.city}</TableContentLabel>
                                  <TableContentLabel>{value?.workingCrew?.name}</TableContentLabel>
                                  <TableContentLabel>{value?.leadSource}</TableContentLabel>
                                  <TableContentLabel>
                                    {dayjsFormat(value?.jobCompletedDate, 'M/D/YY')}
                                  </TableContentLabel>
                                  <TableContentLabel className="right-align">
                                    ${formatNumberToCommaS(value?.soldValue) || 0}
                                  </TableContentLabel>
                                  <TableContentLabel className="right-align">
                                    ${formatNumberToCommaS(value?.realRevValue) || 0}
                                  </TableContentLabel>
                                </TableContent>
                              </tr>
                            )
                          })}
                      </TableContainer>
                    </Fragment>
                  )
                })}
              </>
            )}

            {Object.keys(kpiData?.checkpoints)
              ?.sort((a, b) => kpiData?.checkpoints[a].sequence - kpiData?.checkpoints[b].sequence)
              .map((key: string, index: number) => {
                const checkpoint = kpiData?.checkpoints[key]
                return (
                  <Fragment key={index}>
                    {isKpiTableSelected.toLocaleLowerCase() === 'sale' ? (
                      <>
                        {checkpoint?.types?.map((type: any, i: number) => {
                          return (
                            <Fragment key={i}>
                              <KPIReportHeading>
                                {key} {type?.name}
                              </KPIReportHeading>
                              <TableContainer>
                                <TableHeading column="repeat(7,1fr)">
                                  <TableTitle>Name</TableTitle>
                                  <TableTitle>PO#</TableTitle>
                                  <TableTitle>City</TableTitle>
                                  <TableTitle>Signed</TableTitle>
                                  <TableTitle>{key}</TableTitle>
                                  <TableTitle className="right-align">Volume</TableTitle>
                                  <TableTitle className="center-align">Sold By</TableTitle>
                                </TableHeading>
                                {type?.opps
                                  ?.sort((a: any, b: any) => new Date(a.saleDate) - new Date(b.saleDate))
                                  ?.map((value: any, i: number) => {
                                    const matchedObject: any = stages.find((item) => item._id === value?.stage)

                                    return (
                                      <tr>
                                        <TableContent
                                          as={Link}
                                          to={`/${getEnumValue(matchedObject?.stageGroup)}/opportunity/${value?._id}`}
                                          key={value?._id}
                                          pointer="pointer"
                                          column="repeat(7,1fr)"
                                          // onClick={() => getpageById(value?.stage, value?._id)}
                                        >
                                          <TableContentLabel>
                                            {renderClientName(
                                              value?.contactId?.isBusiness,
                                              value?.contactId?.fullName,
                                              value?.contactId?.businessName
                                            )}
                                            {/* {value?.firstName}, {value?.lastName} */}
                                          </TableContentLabel>
                                          <TableContentLabel>
                                            {value?.PO}-{value?.num}
                                          </TableContentLabel>
                                          <TableContentLabel>{value?.city}</TableContentLabel>
                                          <TableContentLabel>{value?.leadSource}</TableContentLabel>
                                          <TableContentLabel>
                                            {dayjsFormat(value?.[checkpoint?.symbol], 'M/D/YY')}
                                          </TableContentLabel>
                                          <TableContentLabel className="right-align">
                                            ${formatNumberToCommaS(value?.soldValue) ?? 0}
                                          </TableContentLabel>
                                          <TableContentLabel className="center-align">
                                            {value?.assignedTo}
                                          </TableContentLabel>
                                        </TableContent>
                                      </tr>
                                    )
                                  })}
                              </TableContainer>
                            </Fragment>
                          )
                        })}
                      </>
                    ) : (
                      isKpiTableSelected.toLocaleLowerCase() === key.toLocaleLowerCase() && (
                        <>
                          <KPIReportHeading>{key}</KPIReportHeading>
                          <TableContainer>
                            <TableHeading>
                              <TableTitle>Name</TableTitle>
                              <TableTitle>PO#</TableTitle>
                              <TableTitle>City</TableTitle>
                              <TableTitle>Source</TableTitle>
                              <TableTitle>{key}</TableTitle>
                            </TableHeading>
                            {checkpoint?.opps?.map((value: any, i: number) => {
                              const matchedObject: any = stages.find((item) => item._id === value?.stage)

                              return (
                                <tr>
                                  <TableContent
                                    as={Link}
                                    to={`/${getEnumValue(matchedObject?.stageGroup)}/opportunity/${value?._id}`}
                                    key={value?._id}
                                    pointer="pointer"
                                    // onClick={() => getpageById(value?.stage, value?._id)}
                                  >
                                    <TableContentLabel>
                                      {renderClientName(
                                        value?.contactId?.isBusiness,
                                        value?.contactId?.fullName,
                                        value?.contactId?.businessName
                                      )}
                                      {/* {value?.firstName}, {value?.lastName} */}
                                    </TableContentLabel>
                                    <TableContentLabel>
                                      {value?.PO}-{value?.num}
                                    </TableContentLabel>
                                    <TableContentLabel>{value?.city}</TableContentLabel>
                                    <TableContentLabel>{value?.leadSource}</TableContentLabel>
                                    <TableContentLabel>
                                      {dayjsFormat(value?.[checkpoint?.symbol], 'M/D/YY')}
                                    </TableContentLabel>
                                  </TableContent>
                                </tr>
                              )
                            })}
                          </TableContainer>
                        </>
                      )
                    )}
                  </Fragment>
                )
              })}
          </div>
        )}
      </div>
    </KPIReportContainer>
  )
}

export default KPIReport
