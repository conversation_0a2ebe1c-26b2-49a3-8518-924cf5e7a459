import { Link, useNavigate, useLocation } from 'react-router-dom'
import { useEffect, useState, Fragment } from 'react'
import { Formik } from 'formik'
import { useSelector } from 'react-redux'
import * as Yup from 'yup'

import { getCustomCSRReport } from '../../../logic/apis/report'
import * as SharedStyled from '../../../styles/styled'
import * as Styled from './style'
import {
  dayjsFormat,
  formatNumberToCommaS,
  getEnumValue,
  getValueByKeyAndMatch,
  hasValues,
  isSuccess,
  notify,
  renderClientName,
  truncateParagraph,
} from '../../../shared/helpers/util'
import { getCheckpoint, getPositionMembersById, getStages } from '../../../logic/apis/sales'
import { SharedDate } from '../../../shared/date/SharedDate'
import { I_Stage } from '../../opportunity/components/assessmentForm/AssessmentForm'
import { I_Checkpoint } from '../../crmSettings/CrmSettings'
import Button from '../../../shared/components/button/Button'
import { ButtonWrapper } from '../projectReport/style'
import DropdownWithCheckboxes from '../../../shared/dropdownWithCheckboxes/DropdownWithCheckboxes'
import { StageGroupEnum } from '../../../shared/helpers/constants'
import { SLoader } from '../../../shared/components/loader/Loader'
import { getDepartments } from '../../../logic/apis/department'
import { I_Position, I_SalesPerson } from '../../leads/AddNewLead'

const filterSchema = Yup.object().shape({
  date: Yup.string().required('Required'),
  time: Yup.string().required('Required'),
  scheduledBy: Yup.string(),
})

const CustomCSRReport = () => {
  const navigate = useNavigate()

  const [salesPersonDrop, setSalesPersonDrop] = useState<I_SalesPerson[]>([])

  const [officeId, setOfficeId] = useState('')
  const [stages, setStages] = useState<I_Stage[]>([])
  const [checkpointsLowerCase, setCheckpointsLowerCase] = useState<I_Checkpoint[]>([])
  const [filterData, setFilterData] = useState({
    salesPerson: '',
    startDate: '',
    endDate: '',
    selectedOptions: [],
  })
  const [toggleConvCount, setToggleConvCount] = useState<{ [key: string]: boolean }>({})
  const [toggleHeading, setToggleHeading] = useState<{ [key: string]: boolean }>({})
  const [changeOrderSection, setChangeOrderSection] = useState<{ [key: string]: boolean }>({})
  const [optionLoader, setOptionLoader] = useState(false)
  const [officeDrop, setOfficeDrop] = useState<any[]>([])

  const toggleCount = (type: string) => {
    setToggleConvCount((prevState) => ({
      ...prevState,
      [type]: !prevState[type],
    }))
  }
  const [isLoading, setIsLoading] = useState(false)

  const location = useLocation()
  const params = new URLSearchParams(location.search)
  const paramSalesPerson = params.get('salesPerson')
  const paramStartDate = params.get('startDate')
  const paramEndDate = params.get('endDate')
  const selectedParamOptions = salesPersonDrop
    .filter((item) => paramSalesPerson?.includes(item._id))
    ?.map(({ name, _id }: { name: string; _id: string }) => ({ name: name, _id: _id }))

  const [initValues, setInitialValues] = useState({
    salesPerson: '',
    startDate: '',
    endDate: '',
    selectedOptions: selectedParamOptions || [],
  })

  const [data, setData] = useState<any>({})
  const [actionToggle, setActionToggle] = useState(false)

  const globalSelector = useSelector((state: any) => state)
  const { positionDetails, currentMember } = globalSelector.company

  useEffect(() => {
    if (positionDetails?.symbol) {
      fetchCheckpoints()
    }
  }, [positionDetails, currentMember])

  useEffect(() => {
    // const selectedParamOptions = salesPersonDrop
    //   .filter((item) => paramSalesPerson?.includes(item._id))
    //   ?.map(({ name, _id }: { name: string; _id: string }) => ({ name: name, _id: _id }))

    if (paramStartDate && paramEndDate && paramSalesPerson) {
      if (paramStartDate !== '' && paramEndDate !== '' && paramSalesPerson !== '') {
        setInitialValues((prev) => ({
          ...prev,
          startDate: paramStartDate,
          endDate: paramEndDate,
        }))
        if (salesPersonDrop.length > 0) {
          handleFilterData({
            selectedOptions: selectedParamOptions || [],
            startDate: paramStartDate,
            endDate: paramEndDate,
          })
        }
      }
    }
  }, [location.search])

  useEffect(() => {
    if (salesPersonDrop.length > 0) {
      setInitialValues((prev) => ({ ...prev, selectedOptions: selectedParamOptions || [] }))
    }
  }, [salesPersonDrop])

  const handleDateSelection = (salesPersonId: string, selectedStartDate: string, selectedEndDate: string) => {
    const params = new URLSearchParams()
    params.append('salesPerson', salesPersonId)
    params.append('startDate', selectedStartDate)
    params.append('endDate', selectedEndDate)
    const newURL = `${window.location.pathname}?${params.toString()}`
    window.history.pushState({}, '', newURL)
    // Perform any other actions needed when start and end dates are selected
  }

  const fetchCheckpoints = async () => {
    try {
      const response = await getCheckpoint(false, StageGroupEnum.Sales)
      if (isSuccess(response) || isSuccess(response)) {
        const checkpointsArr: I_Checkpoint[] = response.data.data.checkpoint
        // const checkPointsObj = checkpointsArr.reduce((prev, cur) => {
        //   return { ...prev, [cur.name]: cur }
        // }, {})

        const localCheckptsArr = new Array(checkpointsArr.length)
        checkpointsArr.forEach((checkpnt) => {
          localCheckptsArr[checkpnt.sequence - 1] = checkpnt
        })
        let checkpointsArrLowerCase: I_Checkpoint[] = response.data.data.checkpoint.map((item: any) => {
          // Remove spaces and convert name to lowercase
          const updatedName = item.name.replace(/\s/g, '').toLowerCase()

          // Return the updated object
          return {
            ...item,
            name: updatedName,
          }
        })

        setCheckpointsLowerCase(checkpointsArrLowerCase)
      } else {
        throw new Error(response?.data.message ?? 'Something went wrong!')
      }
    } catch (err) {
      console.log('CHECKPOINTS Fetch failed', err)
    }
  }

  const getPositionsOffice = async () => {
    try {
      const response = await getDepartments({ deleted: false }, false)
      if (isSuccess(response)) {
        console.log({ response })
        const departments: I_Position[] = response?.data?.data?.department

        let officePersonIdx: string[] = []
        departments.forEach((department: any, idx) => {
          if (department.name === 'Office') {
            officePersonIdx.push(department?._id)
            return
          }
        })

        setOfficeId(officePersonIdx?.join())
        getPositionForOfficeMembers(officePersonIdx?.join())
      } else {
        notify(response?.data?.message, 'error')
      }
    } catch (err) {
      // notify('Something went wrong!', 'error')
      console.log('GET POSITION FAILED', err)
    }
  }

  const getPositionForOfficeMembers = async (departmentId: string) => {
    try {
      const response = await getPositionMembersById({ departmentId }, false)
      if (isSuccess(response)) {
        setOfficeDrop(response?.data?.data?.memberData)
      } else notify(response?.data?.message, 'error')
    } catch (err) {
      console.log('GET POSITION MEMBERS FAILED', err)
    }
  }

  const getPositions = async (startDate: string, endDate: string) => {
    try {
      const response = await getPositionMembersById(
        {
          departmentId: officeId || undefined,
          startDate,
          endDate,
        },
        false
      )
      if (isSuccess(response)) {
        setSalesPersonDrop(response?.data?.data?.memberData)
      } else {
        notify(response?.data?.message, 'error')
      }
    } catch (err) {
      setOptionLoader(false)
      // notify('Something went wrong!', 'error')
      console.log('GET POSITION FAILED', err)
    } finally {
      setOptionLoader(false)
    }
  }

  useEffect(() => {
    getStagesData()
    getPositionsOffice()
  }, [])

  const getStagesData = async () => {
    try {
      const stagesRes = await getStages({}, false)

      if (isSuccess(stagesRes)) {
        const { stage: stages } = stagesRes.data.data
        const formatStages: I_Stage[] = new Array(stages.length)
        stages.forEach((stage: I_Stage) => {
          formatStages[stage.sequence - 1] = stage
        })
        setStages(stages)
        // setStages(formatStages)
      } else throw new Error(stagesRes?.data?.message)
    } catch (err) {
      console.log('Err stages data', err)
    }
  }

  const sortObjectKeysBySequence = (response1: any, response2: any) => {
    try {
      const keysArray = Object.keys(response2)

      // Sort keysArray based on the sequence order from response1
      keysArray.sort((a, b) => {
        const sequenceA = response1?.find((item: any) => item.name.toLowerCase() === a.toLowerCase())?.sequence || 0
        const sequenceB = response1?.find((item: any) => item.name.toLowerCase() === b.toLowerCase())?.sequence || 0
        return sequenceA - sequenceB
      })

      // Create a new object with sorted keys
      const sortedResponse2: any = {}
      keysArray?.forEach((key) => {
        sortedResponse2[key] = response2[key.toLocaleLowerCase()]
      })
      const sortedData: any = {}
      keysArray.forEach((key) => {
        if (response2[key]) {
          sortedData[key] = response2[key]
          const matchingResponse1 = response1.find((item: any) => item.name.toLowerCase() === key.toLowerCase())
          if (matchingResponse1) {
            // Add symbol from response1 to response2
            sortedData[key].symbol = matchingResponse1.symbol
          }
        }
      })

      return sortedData
    } catch (error) {
      console.log(error)
    }
  }

  const handleFilterData = async (values: typeof initValues) => {
    try {
      setIsLoading(true)
      // const salesPersonId: string = values.selectedOptions?.map((v) => v._id).join(',')
      const salesPersonId: string = values.selectedOptions
        ?.filter((v) => salesPersonDrop?.map((v) => v._id)?.includes(v._id))
        ?.map((v) => v._id)
        .join(',')

      handleDateSelection(salesPersonId, values.startDate, values.endDate)

      const response = await getCustomCSRReport({
        salesPersonId,
        endDate: values.endDate,
        startDate: values.startDate,
      })
      setFilterData(values)
      if (isSuccess(response)) {
        setData(response?.data?.data)

        if (response?.data?.data?.noOrderOpp?.length) {
          response?.data?.data?.noOrderOpp?.forEach((v: any) => notify(v, 'error'))
        }
      } else throw new Error(response?.data?.message)
    } catch (err) {
      console.log('handleFilterData err', err)
    } finally {
      setIsLoading(false)
    }
  }
  const toggleCountHeading = (type: string) => {
    setToggleHeading((prevState) => ({
      ...prevState,
      [type]: !prevState[type],
    }))
  }

  return (
    <>
      <SharedStyled.Content
        maxWidth="1270px"
        width="100%"
        disableBoxShadow={true}
        noPadding={true}
        alignItems="center"
        overflow=""
        gap="10px"
      >
        <SharedStyled.FlexBox flexDirection="column" width="100%">
          <SharedStyled.SectionTitle textAlign="center">Custom CSR Report</SharedStyled.SectionTitle>
          <SharedStyled.HorizontalDivider />
        </SharedStyled.FlexBox>
        <SharedStyled.FlexBox justifyContent="center" alignItems="center" flexDirection="column">
          <div>
            <Styled.SubHeading>
              {filterData.startDate
                ? `${dayjsFormat(filterData.startDate, 'M/D/YY')} to ${dayjsFormat(filterData.endDate, 'M/D/YY')}`
                : ''}
            </Styled.SubHeading>
          </div>

          <div style={{ width: '100%', padding: '0 30px' }}>
            <Formik
              initialValues={initValues}
              onSubmit={handleFilterData}
              validationSchema={filterSchema}
              validateOnChange={true}
              validateOnBlur={false}
              enableReinitialize={true}
            >
              {(formik) => {
                const { handleSubmit, errors, values, setFieldValue, touched, resetForm } = formik

                useEffect(() => {
                  if (
                    currentMember?._id &&
                    positionDetails?.symbol &&
                    values.startDate !== '' &&
                    values.endDate !== ''
                  ) {
                    setInitialValues((prev) => ({ ...prev, endDate: values.endDate, startDate: values.startDate }))
                    setOptionLoader(true)
                    getPositions(values.startDate, values.endDate)
                  }
                }, [positionDetails, currentMember, values.startDate, values.endDate])

                return (
                  <SharedStyled.FlexBox
                    width="100%"
                    gap="10px"
                    alignItems="center"
                    column="column"
                    justifyContent="center"
                  >
                    <SharedDate
                      value={values.startDate}
                      labelName="From"
                      stateName="startDate"
                      error={touched.startDate && errors.startDate ? true : false}
                      setFieldValue={setFieldValue}
                    />
                    <SharedDate
                      value={values.endDate}
                      labelName="To"
                      stateName="endDate"
                      error={touched.endDate && errors.endDate ? true : false}
                      min={values.startDate}
                      setFieldValue={setFieldValue}
                    />
                    {optionLoader ? (
                      <div>
                        <SLoader margin="10px 0 0 0" height={52} width={200} />
                      </div>
                    ) : (
                      <DropdownWithCheckboxes
                        options={salesPersonDrop?.map((v) => ({ name: v.name, _id: v._id })) || []}
                        formik={formik}
                        // selectedOptions={values?.selectedSalesPerson}
                      />
                    )}
                    <Styled.KPIButtonContainer>
                      <Button
                        width="max-content"
                        height="52px"
                        style={{ alignSelf: 'flex-end' }}
                        onClick={() => {
                          handleFilterData(values)
                          // resetForm()
                        }}
                        disabled={
                          values.selectedOptions?.length === 0 || values.startDate === '' || values.endDate === ''
                        }
                        isLoading={isLoading}
                      >
                        Run Report
                      </Button>
                    </Styled.KPIButtonContainer>
                  </SharedStyled.FlexBox>
                )
              }}
            </Formik>
          </div>
        </SharedStyled.FlexBox>
      </SharedStyled.Content>
      {Object.keys(data).length ? (
        <Styled.ReportMainContainer>
          <SharedStyled.FlexBox justifyContent="flex-start">
            <Styled.ReportWrapper>
              <div style={{ margin: '2px 0' }}>
                {data?.totalHours ? (
                  <Styled.ReportLgSubHeading style={{ width: 'max-content', fontWeight: 'normal' }}>
                    Hours Worked:&nbsp;
                    {data?.totalHours?.toFixed(2)}
                  </Styled.ReportLgSubHeading>
                ) : null}
              </div>

              {hasValues(data?.conversion)
                ? Object.entries(data?.conversion).map(([label, value]: any) => (
                    <Styled.ReportLgSubHeading>
                      <span>{label}</span>
                      <span>{value}%</span>
                    </Styled.ReportLgSubHeading>
                  ))
                : null}
            </Styled.ReportWrapper>
          </SharedStyled.FlexBox>

          <Styled.TableContainerMain>
            <thead>
              <tr>
                <th scope="col"></th>
                <th scope="col"></th>
                {/* <th scope="col">This month</th> */}
              </tr>
            </thead>
            <tbody>
              <tr>
                <th>Actions Completed:</th>
                <td style={{ cursor: 'pointer' }} onClick={() => setActionToggle((prevToggle) => !prevToggle)}>
                  {data?.actions.thisWeekNum}
                </td>

                {/* <td></td> */}
              </tr>
              {/*  <tr>
                <th>Opps w/ Actions:</th>
                <td style={{ cursor: 'pointer' }} onClick={() => setActionToggle((prevToggle) => !prevToggle)}>
                  {data?.actions.thisWeekOANum}
                </td>
                /~ <td></td>  ~/
              </tr>*/}
              {Object.keys(data?.checkpoints).map((key: any) => {
                const checkpoint = data?.checkpoints[key]
                return (
                  <tr>
                    <th>{key}:</th>
                    <td>
                      {checkpoint?.num}{' '}
                      <span style={{ color: '#2fac2f' }}>
                        {checkpoint?.selfGenCount > 0 && `(${checkpoint?.selfGenCount})`}
                      </span>
                    </td>
                    {/* <td></td> */}
                  </tr>
                )
              })}
            </tbody>
          </Styled.TableContainerMain>

          {actionToggle && (
            <>
              <Styled.ReportHeading className="title" fontSize="21px" textAlign="center">
                Actions
              </Styled.ReportHeading>
              <Styled.ReportGridBox>
                <div>
                  <Styled.ReportHeading className="margin">
                    Actions Completed: {data?.actions?.thisWeekNum}
                  </Styled.ReportHeading>
                  <Styled.TableContainer>
                    <Styled.TableHeading column={'repeat(4,1fr)'}>
                      <Styled.TableTitle>Type</Styled.TableTitle>
                      <Styled.TableTitle>Action Taken</Styled.TableTitle>
                      <Styled.TableTitle>Done</Styled.TableTitle>
                      <Styled.TableTitle>Opportunity</Styled.TableTitle>
                    </Styled.TableHeading>
                    {data?.actions?.thisWeek
                      ?.sort(
                        (a: any, b: any) => new Date(a?.completedAt)?.getTime() - new Date(b?.completedAt)?.getTime()
                      )
                      ?.map((val: any) => {
                        return (
                          <>
                            <Styled.TableContent column={'repeat(4,1fr)'}>
                              <Styled.CrewReportTableContentLabel>{val?.type}</Styled.CrewReportTableContentLabel>
                              <Styled.CrewReportTableContentLabel>{val?.body}</Styled.CrewReportTableContentLabel>
                              <Styled.CrewReportTableContentLabel>
                                {dayjsFormat(val?.completedAt, 'M/D/YY')}
                              </Styled.CrewReportTableContentLabel>
                              <Styled.CrewReportTableContentLabel>{val?.name}</Styled.CrewReportTableContentLabel>
                            </Styled.TableContent>
                          </>
                        )
                      })}
                  </Styled.TableContainer>
                </div>

                {/* <div>
                  <Styled.ReportHeading>Opps w/ Actions: {data?.actions?.thisWeekOANum}</Styled.ReportHeading>
                  <Styled.TableContainer>
                    <Styled.TableHeading>
                      <Styled.TableTitle>Client name</Styled.TableTitle>
                      <Styled.TableTitle>Date</Styled.TableTitle>
                      <Styled.TableTitle>City</Styled.TableTitle>
                      <Styled.TableTitle>Source</Styled.TableTitle>
                    </Styled.TableHeading>
                    {data?.actions?.thisWeekOA
                      ?.sort(
                        (a: any, b: any) => {
                          const dateA = a?.oppDate ? new Date(a?.oppDate)?.toISOString()?.split('T')[0] : ''
                          const dateB = b?.oppDate ? new Date(b?.oppDate)?.toISOString()?.split('T')[0] : ''

                          if (dateA < dateB) return -1
                          if (dateA > dateB) return 1

                          // If the dates are the same or missing, sort by last name
                          return (a?.clientId?.lastName || '').localeCompare(b?.clientId?.lastName || '')
                        }
                        // new Date(a?.oppDate) - new Date(b?.oppDate) || a.lastName.localeCompare(b.lastName)
                      )
                      ?.map((val: any) => {
                        const matchedObject: any = stages.find((item) => item._id === val?.stage)
                        return (
                          <>
                            <Styled.TableContent
                              as={Link}
                              to={`/${getEnumValue(matchedObject?.stageGroup)}/opportunity/${val?._id}`}
                              key={val?._id}
                              pointer="pointer"
                              selfGen={val?.selfGen}
                              // onClick={() => getpageById(val?.stage, val?._id)}
                            >
                              <Styled.CrewReportTableContentLabel>
                                {renderClientName(
                                  val?.clientId?.isBusiness,
                                  val?.clientId?.firstName,
                                  val?.clientId?.lastName
                                )}
                              </Styled.CrewReportTableContentLabel>
                              <Styled.CrewReportTableContentLabel>
                                {dayjsFormat(val?.oppDate, 'M/D/YY')}
                              </Styled.CrewReportTableContentLabel>
                              <Styled.CrewReportTableContentLabel>{val?.city}</Styled.CrewReportTableContentLabel>
                              <Styled.CrewReportTableContentLabel>{val?.leadSource}</Styled.CrewReportTableContentLabel>
                            </Styled.TableContent>
                          </>
                        )
                      })}
                  </Styled.TableContainer>
                </div> */}
              </Styled.ReportGridBox>
            </>
          )}

          {Object.keys(sortObjectKeysBySequence(checkpointsLowerCase, data?.checkpoints)).map((key: string) => {
            const checkpoint = data?.checkpoints[key]
            const types = checkpoint?.types
            return checkpoint.symbol === 'saleDate' ? (
              <>
                <Styled.ReportWrapper>
                  <Styled.ReportHeading className="title" fontSize="21px" textAlign="center">
                    Total {key}: {checkpoint?.num || 0}{' '}
                    <span style={{ color: '#2fac2f' }}>
                      {checkpoint?.selfGenCount > 0 && `(${checkpoint?.selfGenCount})`}
                    </span>
                    <br />
                  </Styled.ReportHeading>
                  {/* <Styled.ReportHeading fontSize={'16px'} textAlign="center" className="margin">
                            Total Volume:
                            {Number(checkpoint?.SaleVol) ? ` $${formatNumberToCommaS(Number(checkpoint?.SaleVol))} ` : ` $--`}
                          </Styled.ReportHeading> */}

                  <Styled.ReportGridBox>
                    {types?.map((type: any, index: number) => {
                      return (
                        <>
                          <div style={{ width: '100%' }}>
                            <Styled.ReportHeading fontSize={'16px'} className="margin">
                              <span
                                onClick={() => {
                                  toggleCountHeading(`${type?.name}-${key}`)
                                }}
                                className="cursor"
                              >
                                {!toggleHeading[`${type?.name}-${key}`] ? <>&#9654;</> : <>&#9660;</>}
                                {type?.name} {key}: {type?.num}{' '}
                                <span style={{ color: '#2fac2f' }}>
                                  {type?.selfGenCount > 0 && `(${type?.selfGenCount})`}
                                </span>{' '}
                                | Total Volume: ${Number(type?.vol) ? formatNumberToCommaS(Number(type?.vol)) : '--'}
                              </span>
                              <br />
                            </Styled.ReportHeading>
                            {toggleHeading[`${type?.name}-${key}`] ? (
                              <Styled.TableContainer>
                                {/* hardcoded code */}
                                <Styled.TableHeading
                                  column={`2fr repeat(2, 1fr) 2fr ${
                                    type.id === 'c4948c95-fc97-4780-8029-27573ea9bd96' ? '' : '1fr'
                                  } 2fr`}
                                >
                                  <Styled.TableTitle>Client Name</Styled.TableTitle>
                                  <Styled.TableTitle>Date</Styled.TableTitle>
                                  <Styled.TableTitle>CSR</Styled.TableTitle>
                                  {/* <Styled.TableTitle>Source</Styled.TableTitle> */}
                                  <Styled.TableTitle className="right-align no-wrap">Ttl Volume</Styled.TableTitle>
                                  {/* hardcoded code */}
                                  {type.id === 'c4948c95-fc97-4780-8029-27573ea9bd96' ? (
                                    <></>
                                  ) : (
                                    <Styled.TableTitle className="right-align">Score</Styled.TableTitle>
                                  )}
                                  <Styled.TableTitle className="right-align">Discounts</Styled.TableTitle>
                                </Styled.TableHeading>
                                {type?.opps
                                  ?.sort(
                                    (a: any, b: any) => {
                                      const dateA = a?.saleDate
                                        ? new Date(a?.saleDate)?.toISOString()?.split('T')[0]
                                        : ''
                                      const dateB = b?.saleDate
                                        ? new Date(b?.saleDate)?.toISOString()?.split('T')[0]
                                        : ''

                                      if (dateA < dateB) return -1
                                      if (dateA > dateB) return 1

                                      // If the dates are the same or missing, sort by last name
                                      return (a?.fullName || '').localeCompare(b?.fullName || '')
                                    }
                                    // new Date(a?.saleDate) - new Date(b?.saleDate) ||
                                    // a.lastName.localeCompare(b.lastName)
                                  )
                                  ?.map((opp: any) => {
                                    const matchedObject: any = stages.find((item) => item._id === opp?.stage)

                                    return (
                                      <>
                                        <tr>
                                          {/* hardcoded code */}
                                          <Styled.TableContent
                                            // as={Link}
                                            // to={`/${getEnumValue(matchedObject?.stageGroup)}/opportunity/${opp?._id}`}
                                            key={opp?._id}
                                            pointer="pointer"
                                            column={`2fr repeat(2, 1fr) 2fr ${
                                              type.id === 'c4948c95-fc97-4780-8029-27573ea9bd96' ? '' : '1fr'
                                            } 2fr`}
                                            selfGen={opp?.selfGen}
                                            className={
                                              opp?.changeOrders?.filter(
                                                (itm: any) => itm?.signedBySales && !itm?.deleted
                                              )?.length
                                                ? 'change-orders'
                                                : ''
                                            }
                                            onClick={() => {
                                              setChangeOrderSection((prev) => ({
                                                ...prev,
                                                [opp?._id]: !prev[opp?._id],
                                              }))
                                            }}
                                            // onClick={() => getpageById(opp?.stage, opp?._id)}
                                          >
                                            <Link
                                              to={
                                                opp?.PO
                                                  ? `/${getEnumValue(matchedObject?.stageGroup)}/opportunity/${
                                                      opp?._id
                                                    }`
                                                  : `/contact/profile/${opp.contactId}`
                                              }
                                            >
                                              <Styled.CrewReportTableContentLabel>
                                                {opp?.fullName}
                                              </Styled.CrewReportTableContentLabel>
                                            </Link>
                                            <Styled.CrewReportTableContentLabel>
                                              {dayjsFormat(opp?.saleDate, 'M/D/YY')}
                                            </Styled.CrewReportTableContentLabel>
                                            <Styled.CrewReportTableContentLabel>
                                              {getValueByKeyAndMatch('name', opp?.csrId, '_id', officeDrop) || '--'}
                                            </Styled.CrewReportTableContentLabel>
                                            {/* <Styled.CrewReportTableContentLabel>
                                                      {opp?.leadSource}
                                                    </Styled.CrewReportTableContentLabel> */}
                                            <Styled.CrewReportTableContentLabel className="right-align">
                                              ${formatNumberToCommaS(Number(opp?.soldValue))}
                                            </Styled.CrewReportTableContentLabel>
                                            {/* hardcoded code */}
                                            {type.id === 'c4948c95-fc97-4780-8029-27573ea9bd96' ? (
                                              <></>
                                            ) : (
                                              <Styled.CrewReportTableContentLabel className="right-align">
                                                {formatNumberToCommaS(opp?.score)}
                                              </Styled.CrewReportTableContentLabel>
                                            )}
                                            <Styled.CrewReportTableContentLabel className="right-align">
                                              $
                                              {Number(opp?.discount)
                                                ? formatNumberToCommaS(Number(opp?.discount))
                                                : '--'}{' '}
                                              ({formatNumberToCommaS(opp?.disPer)}%)
                                            </Styled.CrewReportTableContentLabel>
                                          </Styled.TableContent>
                                        </tr>

                                        {changeOrderSection[opp?._id] ? (
                                          <>
                                            {opp?.changeOrders?.map((itm: any) => (
                                              <>
                                                {itm?.signedBySales && !itm?.deleted ? (
                                                  <>
                                                    <tr>
                                                      <Styled.TableContent
                                                        // as={Link}
                                                        // to={`/${getEnumValue(matchedObject?.stageGroup)}/opportunity/${opp?._id}`}
                                                        key={opp?._id}
                                                        pointer="pointer"
                                                        column={`2fr repeat(3, 1fr) 2fr 1fr 2fr`}
                                                        selfGen={opp?.selfGen}
                                                        className={'nested'}
                                                        // onClick={() => getpageById(opp?.stage, opp?._id)}
                                                      >
                                                        <Styled.CrewReportTableContentLabel
                                                          as={Link}
                                                          to={
                                                            opp?.PO
                                                              ? `/${getEnumValue(
                                                                  matchedObject?.stageGroup
                                                                )}/opportunity/${opp?._id}`
                                                              : `/contact/profile/${opp.contactId}`
                                                          }
                                                          // to={`/${getEnumValue(
                                                          //   matchedObject?.stageGroup
                                                          // )}/opportunity/${opp?._id}`}
                                                        >
                                                          {opp?.fullName}
                                                        </Styled.CrewReportTableContentLabel>
                                                        <Styled.CrewReportTableContentLabel>
                                                          {itm?.date ? dayjsFormat(itm?.date, 'M/D/YY') : '--'}
                                                        </Styled.CrewReportTableContentLabel>
                                                        <Styled.CrewReportTableContentLabel>
                                                          {opp?.city}
                                                        </Styled.CrewReportTableContentLabel>
                                                        <Styled.CrewReportTableContentLabel>
                                                          Modification
                                                        </Styled.CrewReportTableContentLabel>
                                                        <Styled.CrewReportTableContentLabel className="right-align">
                                                          ${formatNumberToCommaS(itm?.jobCost)}
                                                        </Styled.CrewReportTableContentLabel>
                                                        <Styled.CrewReportTableContentLabel className="right-align">
                                                          --
                                                        </Styled.CrewReportTableContentLabel>
                                                        <Styled.CrewReportTableContentLabel className="right-align">
                                                          --
                                                        </Styled.CrewReportTableContentLabel>
                                                      </Styled.TableContent>
                                                    </tr>
                                                  </>
                                                ) : null}
                                              </>
                                            ))}
                                          </>
                                        ) : null}
                                      </>
                                    )
                                  })}
                              </Styled.TableContainer>
                            ) : null}
                          </div>
                        </>
                      )
                    })}
                  </Styled.ReportGridBox>
                </Styled.ReportWrapper>
              </>
            ) : checkpoint.symbol === 'oppDate' ? (
              <>
                <Styled.ReportWrapper>
                  <Styled.ReportHeading className="title" fontSize="21px" textAlign="center">
                    Total {key}: {checkpoint?.num || 0}{' '}
                    <span style={{ color: '#2fac2f' }}>
                      {checkpoint?.selfGenCount > 0 && `(${checkpoint?.selfGenCount})`}
                    </span>
                  </Styled.ReportHeading>
                  <Styled.ReportGridBox>
                    {types
                      ?.filter((v: any) => v.name !== 'All')
                      ?.map((type: any, index: number) => {
                        return (
                          <>
                            <div style={{ width: '100%' }}>
                              <Styled.ReportHeading fontSize={'16px'} className="margin">
                                <span
                                  onClick={() => {
                                    toggleCountHeading(`${type?.name}-${key}`)
                                  }}
                                  className="cursor"
                                >
                                  {!toggleHeading[`${type?.name}-${key}`] ? <>&#9654;</> : <>&#9660;</>}
                                  {type?.name} {key}: {type?.num || 0}{' '}
                                  <span style={{ color: '#2fac2f' }}>
                                    {type?.selfGenCount > 0 && `(${type?.selfGenCount})`}
                                  </span>
                                </span>
                              </Styled.ReportHeading>

                              {toggleHeading[`${type?.name}-${key}`] ? (
                                <>
                                  <Styled.TableContainer>
                                    <Styled.TableHeading column={`2fr repeat(4, 1fr)`}>
                                      <Styled.TableTitle>Client Name</Styled.TableTitle>
                                      <Styled.TableTitle>Date</Styled.TableTitle>
                                      <Styled.TableTitle>City</Styled.TableTitle>
                                      <Styled.TableTitle>Source</Styled.TableTitle>
                                      <Styled.TableTitle className="right-align">Cost</Styled.TableTitle>
                                    </Styled.TableHeading>
                                    {type?.opps
                                      ?.sort(
                                        (a: any, b: any) => {
                                          const dateA = a?.oppDate
                                            ? new Date(a?.oppDate)?.toISOString()?.split('T')[0]
                                            : ''
                                          const dateB = b?.oppDate
                                            ? new Date(b?.oppDate)?.toISOString()?.split('T')[0]
                                            : ''

                                          if (dateA < dateB) return -1
                                          if (dateA > dateB) return 1

                                          // If the dates are the same or missing, sort by last name
                                          return (a?.fullName || '').localeCompare(b?.fullName || '')
                                        }
                                        // new Date(a?.oppDate) - new Date(b?.oppDate) ||
                                        // a.lastName.localeCompare(b.lastName)
                                      )
                                      ?.map((opp: any) => {
                                        const matchedObject: any = stages.find((item) => item._id === opp?.stage)
                                        return (
                                          <>
                                            <tr>
                                              <Styled.TableContent
                                                as={Link}
                                                to={
                                                  opp?.PO
                                                    ? `/${getEnumValue(matchedObject?.stageGroup)}/opportunity/${
                                                        opp?._id
                                                      }`
                                                    : `/contact/profile/${opp.contactId}`
                                                }
                                                key={opp?._id}
                                                column={`2fr repeat(4, 1fr)`}
                                                selfGen={opp?.selfGen}
                                                pointer="pointer"
                                                // onClick={() => getpageById(opp?.stage, opp?._id)}
                                              >
                                                <Styled.CrewReportTableContentLabel>
                                                  {opp?.fullName}
                                                </Styled.CrewReportTableContentLabel>
                                                <Styled.CrewReportTableContentLabel>
                                                  {dayjsFormat(opp?.oppDate, 'M/D/YY')}
                                                </Styled.CrewReportTableContentLabel>
                                                <Styled.CrewReportTableContentLabel>
                                                  {opp?.city}
                                                </Styled.CrewReportTableContentLabel>
                                                <Styled.CrewReportTableContentLabel>
                                                  {opp?.leadSource}

                                                  {opp?.campaignName ? (
                                                    <>
                                                      <br />
                                                      <i>({opp?.campaignName})</i>
                                                    </>
                                                  ) : null}
                                                </Styled.CrewReportTableContentLabel>
                                                <Styled.CrewReportTableContentLabel className="right-align">
                                                  ${formatNumberToCommaS(opp?.leadCost)}
                                                </Styled.CrewReportTableContentLabel>
                                              </Styled.TableContent>
                                            </tr>
                                          </>
                                        )
                                      })}
                                  </Styled.TableContainer>
                                </>
                              ) : null}
                            </div>
                          </>
                        )
                      })}
                  </Styled.ReportGridBox>
                </Styled.ReportWrapper>
              </>
            ) : (
              key !== 'Lost' && (
                <Styled.ReportWrapper>
                  <Styled.ReportHeading className="title" fontSize="21px" textAlign="center">
                    Total {key}: {checkpoint?.num || 0}{' '}
                    <span style={{ color: '#2fac2f' }}>
                      {checkpoint?.selfGenCount > 0 && `(${checkpoint?.selfGenCount})`}
                    </span>
                  </Styled.ReportHeading>
                  <Styled.ReportGridBox>
                    {types?.map((type: any, index: number) => {
                      return (
                        <>
                          <div style={{ width: '100%' }}>
                            <Styled.ReportHeading fontSize={'16px'} className="margin">
                              <span
                                onClick={() => {
                                  toggleCountHeading(`${type?.name}-${key}`)
                                }}
                                className="cursor"
                              >
                                {!toggleHeading[`${type?.name}-${key}`] ? <>&#9654;</> : <>&#9660;</>}
                                {type?.name} {key}: {type?.num || 0}{' '}
                                <span style={{ color: '#2fac2f' }}>
                                  {type?.selfGenCount > 0 && `(${type?.selfGenCount})`}
                                </span>
                              </span>
                            </Styled.ReportHeading>
                            {toggleHeading[`${type?.name}-${key}`] ? (
                              <Styled.TableContainer>
                                <Styled.TableHeading>
                                  <Styled.TableTitle>Client Name</Styled.TableTitle>
                                  <Styled.TableTitle>Date</Styled.TableTitle>
                                  <Styled.TableTitle>City</Styled.TableTitle>
                                  <Styled.TableTitle>Source</Styled.TableTitle>
                                  {/* <Styled.TableTitle>Cost</Styled.TableTitle> */}
                                </Styled.TableHeading>
                                {type?.opps
                                  ?.sort((a: any, b: any) => {
                                    const dateA = a[checkpoint?.symbol]
                                      ? new Date(a[checkpoint?.symbol]).toISOString().split('T')[0]
                                      : ''
                                    const dateB = b[checkpoint?.symbol]
                                      ? new Date(b[checkpoint?.symbol]).toISOString().split('T')[0]
                                      : ''

                                    if (dateA < dateB) return -1
                                    if (dateA > dateB) return 1

                                    // If the dates are the same or missing, sort by last name
                                    return (a?.fullName || '')?.localeCompare(b?.fullName || '')
                                  })
                                  ?.map((opp: any) => {
                                    const matchedObject: any = stages.find((item) => item._id === opp?.stage)
                                    return (
                                      <>
                                        <tr>
                                          <Styled.TableContent
                                            as={Link}
                                            to={
                                              opp?.PO
                                                ? `/${getEnumValue(matchedObject?.stageGroup)}/opportunity/${opp?._id}`
                                                : `/contact/profile/${opp.contactId}`
                                            }
                                            key={opp?._id}
                                            selfGen={opp?.selfGen}
                                            pointer="pointer"
                                            // onClick={() => getpageById(opp?.stage, opp?._id)}
                                          >
                                            <Styled.CrewReportTableContentLabel>
                                              {opp?.fullName}
                                            </Styled.CrewReportTableContentLabel>
                                            <Styled.CrewReportTableContentLabel>
                                              {dayjsFormat(opp[checkpoint.symbol], 'M/D/YY')}
                                            </Styled.CrewReportTableContentLabel>
                                            <Styled.CrewReportTableContentLabel>
                                              {opp?.city}
                                            </Styled.CrewReportTableContentLabel>
                                            <Styled.CrewReportTableContentLabel>
                                              {opp?.leadSource}
                                              {opp?.campaignName ? (
                                                <>
                                                  <br />
                                                  <i>({opp?.campaignName})</i>
                                                </>
                                              ) : null}
                                            </Styled.CrewReportTableContentLabel>
                                            {/* <Styled.CrewReportTableContentLabel>
                                            {opp?.leadCost}
                                          </Styled.CrewReportTableContentLabel> */}
                                          </Styled.TableContent>
                                        </tr>
                                      </>
                                    )
                                  })}
                              </Styled.TableContainer>
                            ) : null}
                          </div>
                        </>
                      )
                    })}
                  </Styled.ReportGridBox>
                </Styled.ReportWrapper>
              )
            )
          })}

          {data?.checkpoints['Lost'] && (
            <>
              <Styled.ReportWrapper>
                <Styled.ReportHeading className="title" fontSize="21px" textAlign="center">
                  Lost Opportunities: {data?.checkpoints['Lost']?.num || 0}{' '}
                  <span style={{ color: '#2fac2f' }}>
                    {data?.checkpoints['Lost']?.selfGenCount > 0 && `(${data?.checkpoints['Lost']?.selfGenCount})`}
                  </span>
                </Styled.ReportHeading>

                <div style={{ width: '100%' }}>
                  <Styled.TableContainer>
                    <Styled.TableHeading column={`1fr 1fr`}>
                      <Styled.TableTitle>Stage</Styled.TableTitle>
                      <Styled.TableTitle>#</Styled.TableTitle>
                    </Styled.TableHeading>
                    {data?.checkpoints['Lost']?.stages
                      ?.filter((v: any) => v?.stageGroup !== StageGroupEnum.Operations)
                      ?.sort((v: any) => v?.sequence)
                      ?.map((value: any, index: number) => {
                        return (
                          <tr key={index}>
                            <Styled.TableContent column={`1fr 1fr`} onClick={() => toggleCount(index + value?.name)}>
                              <Styled.CrewReportTableContentLabel>{value?.name}</Styled.CrewReportTableContentLabel>
                              <Styled.CrewReportTableContentLabel>
                                {value?.totalNum || 0}
                              </Styled.CrewReportTableContentLabel>
                            </Styled.TableContent>

                            {toggleConvCount[index + value?.name] && value?.totalNum > 0 ? (
                              value?.opps?.map((v: any) => {
                                const matchedObject: any = stages?.find((item) => item?._id === v?.stage)

                                return (
                                  <Fragment>
                                    <Styled.TableContent
                                      as={Link}
                                      to={
                                        v?.PO
                                          ? `/${getEnumValue(matchedObject?.stageGroup)}/opportunity/${v?._id}`
                                          : `/contact/profile/${v.contactId}`
                                      }
                                      key={v?._id}
                                      pointer="pointer"
                                      column={`repeat(6,1fr)`}
                                    >
                                      <Styled.CrewReportTableContentLabel colors="grey">
                                        {v?.PO}-{v?.num}
                                      </Styled.CrewReportTableContentLabel>
                                      <Styled.CrewReportTableContentLabel colors="grey">
                                        {v?.fullName}
                                      </Styled.CrewReportTableContentLabel>
                                      <Styled.CrewReportTableContentLabel colors="grey">
                                        {/* {getValueByKeyAndMatch('name', v?.salesPerson, '_id', salesPersonDrop)} */}

                                        {v?.csrAssigned?.name}
                                      </Styled.CrewReportTableContentLabel>
                                      <Styled.CrewReportTableContentLabel colors="grey">
                                        {v?.leadSource}
                                        {v?.campaignName ? (
                                          <>
                                            <br />
                                            <i>({v?.campaignName})</i>
                                          </>
                                        ) : null}
                                      </Styled.CrewReportTableContentLabel>
                                      <Styled.CrewReportTableContentLabel colors="grey">
                                        {dayjsFormat(v?.lostDate, 'M/D/YY')}
                                      </Styled.CrewReportTableContentLabel>
                                      <Styled.CrewReportTableContentLabel colors="grey">
                                        <SharedStyled.TooltipContainer
                                          positionLeft="0"
                                          positionBottom="0"
                                          positionLeftDecs="40px"
                                          positionBottomDecs="25px"
                                        >
                                          <span className="tooltip-content">{v?.lostReason}</span>
                                          {truncateParagraph(v?.lostReason, 15)}
                                        </SharedStyled.TooltipContainer>
                                      </Styled.CrewReportTableContentLabel>
                                    </Styled.TableContent>
                                  </Fragment>
                                )
                              })
                            ) : (
                              <></>
                            )}
                          </tr>
                        )
                      })}
                  </Styled.TableContainer>
                </div>

                <Styled.ReportGridBox column="1fr">
                  {data?.checkpoints['Lost']?.types?.map((type: any, index: number) => {
                    return (
                      <>
                        <div style={{ width: '100%' }}>
                          <Styled.ReportHeading fontSize={'16px'} className="margin">
                            <span
                              onClick={() => {
                                toggleCountHeading(`${type?.name}-Lost`)
                              }}
                              className="cursor"
                            >
                              {!toggleHeading[`${type?.name}-Lost`] ? <>&#9654;</> : <>&#9660;</>}
                              {type?.name} Lost: {type?.num || 0}{' '}
                              <span style={{ color: '#2fac2f' }}>
                                {type?.selfGenCount > 0 && `(${type?.selfGenCount})`}
                              </span>
                            </span>
                          </Styled.ReportHeading>
                          {toggleHeading[`${type?.name}-Lost`] ? (
                            <Styled.TableContainer>
                              <Styled.TableHeading column={`2fr repeat(7, 1fr)`}>
                                <Styled.TableTitle>Lost Opportunities</Styled.TableTitle>
                                <Styled.TableTitle>Source</Styled.TableTitle>
                                <Styled.TableTitle className="right-align">Cost</Styled.TableTitle>
                                <Styled.TableTitle></Styled.TableTitle>
                                <Styled.TableTitle>Date lost</Styled.TableTitle>
                                <Styled.TableTitle>Reason lost</Styled.TableTitle>
                                <Styled.TableTitle>Cred req</Styled.TableTitle>
                                <Styled.TableTitle>Cred rec</Styled.TableTitle>
                              </Styled.TableHeading>
                              {type?.opps
                                ?.sort((a: any, b: any) => {
                                  const dateA = a?.lostDate ? new Date(a.lostDate).toISOString().split('T')[0] : ''
                                  const dateB = b?.lostDate ? new Date(b.lostDate).toISOString().split('T')[0] : ''

                                  if (dateA < dateB) return -1
                                  if (dateA > dateB) return 1

                                  // If the dates are the same or missing, sort by last name
                                  return (a?.fullName || '').localeCompare(b?.fullName || '')
                                })
                                ?.map((opp: any) => {
                                  const matchedObject: any = stages.find((item) => item._id === opp?.stage)
                                  return (
                                    <>
                                      <tr>
                                        <Styled.TableContent
                                          as={Link}
                                          to={
                                            opp?.PO
                                              ? `/${getEnumValue(matchedObject?.stageGroup)}/opportunity/${opp?._id}`
                                              : `/contact/profile/${opp.contactId}`
                                          }
                                          key={opp?._id}
                                          column={`2fr repeat(7, 1fr)`}
                                          selfGen={opp?.selfGen}
                                          pointer="pointer"
                                          // onClick={() => getpageById(opp?.stage, opp?._id)}
                                        >
                                          <Styled.CrewReportTableContentLabel>
                                            {opp?.fullName}
                                          </Styled.CrewReportTableContentLabel>
                                          <Styled.CrewReportTableContentLabel>
                                            {opp?.leadSource}
                                            {opp?.campaignName ? (
                                              <>
                                                <br />
                                                <i>({opp?.campaignName})</i>
                                              </>
                                            ) : null}
                                          </Styled.CrewReportTableContentLabel>
                                          <Styled.CrewReportTableContentLabel className="right-align">
                                            ${formatNumberToCommaS(Number(opp?.leadCost))}
                                          </Styled.CrewReportTableContentLabel>
                                          <Styled.CrewReportTableContentLabel></Styled.CrewReportTableContentLabel>
                                          <Styled.CrewReportTableContentLabel>
                                            {dayjsFormat(opp?.lostDate, 'M/D/YY')}
                                          </Styled.CrewReportTableContentLabel>
                                          <Styled.CrewReportTableContentLabel>
                                            <SharedStyled.TooltipContainer
                                              positionLeft="0"
                                              positionBottom="0"
                                              positionLeftDecs="40px"
                                              positionBottomDecs="25px"
                                            >
                                              <span className="tooltip-content">{opp?.lostReason}</span>
                                              {truncateParagraph(opp?.lostReason, 15)}
                                            </SharedStyled.TooltipContainer>
                                          </Styled.CrewReportTableContentLabel>
                                          <Styled.CrewReportTableContentLabel>
                                            {opp?.creditRequested}
                                          </Styled.CrewReportTableContentLabel>
                                          <Styled.CrewReportTableContentLabel>
                                            {opp?.creditReceived}
                                          </Styled.CrewReportTableContentLabel>
                                        </Styled.TableContent>
                                      </tr>
                                    </>
                                  )
                                })}
                            </Styled.TableContainer>
                          ) : null}
                        </div>
                      </>
                    )
                  })}
                </Styled.ReportGridBox>
              </Styled.ReportWrapper>
            </>
          )}
        </Styled.ReportMainContainer>
      ) : (
        <SharedStyled.ContentHeader textAlign="center" fontWeight="500" margin="40px 0 0 0">
          Please choose office person and date range!
        </SharedStyled.ContentHeader>
      )}
      <ButtonWrapper>
        <Button width="max-content" onClick={() => navigate(`/reports`)}>
          Back To Reports
        </Button>
      </ButtonWrapper>
    </>
  )
}

export default CustomCSRReport
