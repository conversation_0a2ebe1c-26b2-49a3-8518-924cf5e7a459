import { Form, Formik } from 'formik'
import React, { Fragment, useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { useParams } from 'react-router-dom'
import * as SharedStyled from '../../../styles/styled'

import { getWeeklyProductionReport } from '../../../logic/apis/report'
import CustomSelect from '../../../shared/customSelect/CustomSelect'
import {
  dayjsFormat,
  extractDatesFromString,
  formattedDateForRange,
  formatNumberToCommaS,
  getWeeksInRange,
  isSuccess,
  notify,
  getDataFromLocalStorage,
} from '../../../shared/helpers/util'
import WeeklyProductionReportCard from '../weeklyProductionReportCard/WeeklyProductionReportCard'
import * as Styled from './style'
import Button from '../../../shared/components/button/Button'
import { CustomModal } from '../../../shared/customModal/CustomModal'
import DailyLog from '../../timeCard/components/dailyLog/DailyLog'
import { StorageKey } from '../../../shared/helpers/constants'
import dayjs from 'dayjs'

const WeeklyProductionReport = () => {
  const [isLoading, setIsLoading] = useState(false)
  const globalSelector = useSelector((state: any) => state)
  const { currentCompany } = globalSelector.company
  const [weeksOptions, setWeeksOptions] = useState<string[]>([])
  const [logId, setLogId] = useState('')
  const [reportD, setReport] = useState<string>('')
  const [weekReport, setWeekReport] = useState('')
  const [isEditDailyLog, setIsEditDailyLog] = useState(false)
  const [buttonCall, setbuttonCall] = useState(false)
  const [showCrewDailyLog, setShowCrewDailyLog] = useState(false)
  const [selectedCrew, setSelectedCrew] = useState('')
  const [selectedCrewName, setSelectedCrewName] = useState('')
  const [data, setData] = useState<any>([])
  const [poList, setPoList] = useState([])

  const tableHeading = [
    {
      label: 'Date',
      price: false,
    },
    {
      label: 'PO#',
      price: false,
    },
    {
      label: 'Budget',
      price: true,
    },
    {
      label: 'Actual',
      price: true,
    },
    {
      label: 'RR',
      price: true,
    },
    {
      label: 'Volume',
      price: true,
    },
    {
      label: '',
      price: false,
    },
    {
      label: 'Tear off',
      price: false,
    },
    {
      label: 'Roofing',
      price: false,
    },
  ]

  const initialValues = {
    dateRange: '',
    data: [],
  }

  // useEffect(() => {
  //   if (weeksOptions.length) {
  //     setWeekReport(weeksOptions[1])
  //   }
  // }, [weeksOptions])

  const handleDateSelection = (weekReport: string) => {
    const params = new URLSearchParams()
    params.append('weekReport', weekReport)
    const newURL = `${window.location.pathname}?${params.toString()}`
    window.history.pushState({}, '', newURL)
    // Perform any other actions needed when start and end dates are selected
  }

  useEffect(() => {
    // const now = new Date()
    // const twentyWeeksAgo = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 203)
    // const weeksObj = getWeeksInRange(twentyWeeksAgo, now)
    // const weeksOptionsArr = weeksObj
    //   .map((weekData) => `W${weekData.week}: ${weekData.startDate} to ${weekData.endDate}`)
    //   .reverse()
    const weeksOptionsArr = formattedDateForRange(true)
    setWeeksOptions(weeksOptionsArr)
    setWeekReport(weeksOptionsArr[1])

    const params = new URLSearchParams(location.search)
    const paramWeekReport = params.get('weekReport')
    if (paramWeekReport) {
      if (paramWeekReport !== '') {
        setWeekReport(paramWeekReport)
        const [startDate, endDate] = extractDatesFromString(paramWeekReport)
        if (!buttonCall) {
          initFetch(startDate, endDate)
        }
        // handleSubmitForm(paramWeekReport)
      }
    }
    // else {
    //   const [startDate, endDate] = extractDatesFromString(weeksOptionsArr[0])
    //   initFetch(startDate, endDate)
    // }
  }, [location.search])

  const runReportClick = () => {
    handleDateSelection(weekReport)
    const [startDate, endDate] = extractDatesFromString(weekReport)
    initFetch(startDate, endDate)
  }

  const initFetch = async (startDate: Date, endDate: Date) => {
    setIsLoading(true)
    try {
      const response = await getWeeklyProductionReport({
        endDate: endDate ? endDate.toISOString() : new Date().toISOString(),
        startDate: startDate
          ? startDate.toISOString()
          : new Date(new Date().getTime() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      })
      if (isSuccess(response)) {
        setIsLoading(false)
        setData(response?.data?.data)
        if (response?.data?.data?.missingError?.length) {
          response?.data?.data?.missingError?.forEach((v: any) => notify(v, 'error'))
        }
      } else {
        setIsLoading(false)
      }
    } catch (error) {
      console.log(error)
      setIsLoading(false)
    }
  }

  // const handleWeeklyReport = async (startDate: Date, endDate: Date) => {
  //   try {
  //     setIsLoading(true)
  //     if (values?.dateRange) {
  //       const [startDate, endDate] = extractDatesFromString(values.dateRange)
  //       const startYear = startDate.getFullYear()
  //       const startMonth = String(startDate.getMonth() + 1).padStart(2, '0')
  //       const startDay = String(startDate.getDate()).padStart(2, '0')
  //       const endYear = endDate.getFullYear()
  //       const endMonth = String(endDate.getMonth() + 1).padStart(2, '0')
  //       const endDay = String(endDate.getDate()).padStart(2, '0')
  //       // Step 3: Assemble the formatted date string in the desired format
  //       const formattedStartDate = `${startYear}-${startMonth}-${startDay}`
  //       const formattedEndDate = `${endYear}-${endMonth}-${endDay}`
  //       const response = await getWeeklyProductionReport({
  //         companyId: currentCompany._id,
  //         endDate: formattedEndDate,
  //         startDate: formattedStartDate,
  //       })
  //       setFieldValue('data', response)
  //     }
  //   } catch (e) {
  //     console.log(e)
  //   } finally {
  //     setIsLoading(false)
  //   }
  // }

  return (
    <Styled.WeeklyReportMainLayout>
      <Styled.WeeklyReportHeading>Weekly Production Report</Styled.WeeklyReportHeading>
      <SharedStyled.HorizontalDivider />

      <Styled.WeeklyReportSubHeading>{weekReport}</Styled.WeeklyReportSubHeading>
      <Styled.WeeklyReportDropdownContainer>
        <Styled.WeeklyReportDropdown>
          <CustomSelect
            labelName="Choose a week..."
            error={false}
            value={weekReport}
            dropDownData={weeksOptions}
            setValue={setWeekReport}
            margin="10px 0 0 0"
            stateName="dateRange"
          />
        </Styled.WeeklyReportDropdown>
        <Button
          width="max-content"
          isLoading={isLoading}
          style={{ alignSelf: 'flex-end' }}
          onClick={() => {
            runReportClick()
            setbuttonCall(true)
          }}
        >
          Run Report
        </Button>
      </Styled.WeeklyReportDropdownContainer>
      {Object.keys(data)?.length !== 0 && (
        <Styled.WeeklyReportTotalContainer>
          <Styled.WeeklyReportDetailHeading>Week Totals</Styled.WeeklyReportDetailHeading>
          <Styled.WeeklyReportLabel>
            RR produced: ${formatNumberToCommaS(Number(data?.rr)) ?? '---'}
          </Styled.WeeklyReportLabel>
          <Styled.WeeklyReportLabel>
            Vol produced: ${formatNumberToCommaS(Number(data?.vol)) ?? '---'}
          </Styled.WeeklyReportLabel>
          <Styled.WeeklyReportLabel>
            Total budget: ${formatNumberToCommaS(Number(data?.budget)) ?? '---'}
          </Styled.WeeklyReportLabel>
          <Styled.WeeklyReportLabel>
            Total actual: ${formatNumberToCommaS(Number(data?.actual)) ?? '---'}
          </Styled.WeeklyReportLabel>
          <Styled.WeeklyReportLabel>
            RR/$: ${formatNumberToCommaS(Number(data?.rrDollar)) ?? '---'}
          </Styled.WeeklyReportLabel>
        </Styled.WeeklyReportTotalContainer>
      )}

      {data?.crews?.map((crew: any) => (
        <div key={crew?.crewId}>
          <Styled.WeeklyReportTotalContainer>
            <Styled.WeeklyReportDetailHeading fontWeight="20px">{crew?.name}</Styled.WeeklyReportDetailHeading>
            <Styled.WeeklyReportLabel>
              RR produced: ${formatNumberToCommaS(Number(crew?.rr)) ?? '---'}
            </Styled.WeeklyReportLabel>
            <Styled.WeeklyReportLabel>
              Vol produced: ${formatNumberToCommaS(Number(crew?.vol)) ?? '---'}
            </Styled.WeeklyReportLabel>
            <Styled.WeeklyReportLabel>
              Crew Budget: ${formatNumberToCommaS(Number(crew?.budget)) ?? '---'}
            </Styled.WeeklyReportLabel>
            <Styled.WeeklyReportLabel>
              Crew Actual: ${formatNumberToCommaS(Number(crew?.actual)) ?? '---'}
            </Styled.WeeklyReportLabel>
            <Styled.WeeklyReportLabel>
              RR/$: ${formatNumberToCommaS(Number(crew?.rrDollar)) ?? '---'}
            </Styled.WeeklyReportLabel>
          </Styled.WeeklyReportTotalContainer>
          <Styled.TableContainer>
            <Styled.TableHeading>
              {tableHeading.map((heading) => (
                <Styled.TableTitle className={`${heading?.price && 'right-align'}`}>{heading?.label}</Styled.TableTitle>
              ))}
            </Styled.TableHeading>
            {crew?.dayReports
              ?.filter((v: any) => v.log)
              ?.sort(
                (a: { date: string }, b: { date: string }) => new Date(a.date).getTime() - new Date(b?.date).getTime()
              )
              ?.map((report: any) => (
                <Styled.TableContent
                  onClick={() => {
                    const reportDate = dayjs(report?.date)
                    const today = dayjs()
                    const isOlderThan30Days = today.diff(reportDate, 'day') > 30

                    if (isOlderThan30Days) {
                      notify('You cannot edit daily logs older than 30 days.', 'error')
                      return
                    }
                    setLogId(report?.log?._id)
                    setSelectedCrew(crew?.crewId)

                    setSelectedCrewName(crew?.name)
                    setReport(dayjsFormat(report?.date, 'M/D/YY'))
                    setPoList(report?.LogPOList)
                    setShowCrewDailyLog(true)
                  }}
                >
                  <Styled.CrewReportTableContentLabel>
                    {dayjsFormat(report?.date, 'ddd')} {dayjsFormat(report?.date, 'M/D/YY')}
                  </Styled.CrewReportTableContentLabel>
                  <Styled.CrewReportTableContentLabel>{report?.POList?.join(', ')}</Styled.CrewReportTableContentLabel>
                  <Styled.CrewReportTableContentLabel className="right-align">
                    ${formatNumberToCommaS(Number(report?.budget)) ?? '---'}
                  </Styled.CrewReportTableContentLabel>
                  <Styled.CrewReportTableContentLabel className="right-align">
                    ${formatNumberToCommaS(Number(report?.actual)) ?? '---'}
                  </Styled.CrewReportTableContentLabel>
                  <Styled.CrewReportTableContentLabel className="right-align">
                    ${formatNumberToCommaS(Number(report?.rr)) ?? '---'}
                  </Styled.CrewReportTableContentLabel>
                  <Styled.CrewReportTableContentLabel className="right-align">
                    ${formatNumberToCommaS(Number(report?.vol)) ?? '---'}
                  </Styled.CrewReportTableContentLabel>
                  <Styled.CrewReportTableContentLabel></Styled.CrewReportTableContentLabel>
                  <Styled.CrewReportTableContentLabel>
                    {report?.tearOffSq?.toFixed(1)}
                  </Styled.CrewReportTableContentLabel>
                  <Styled.CrewReportTableContentLabel>
                    {report?.roofingSq?.toFixed(1)}
                  </Styled.CrewReportTableContentLabel>
                </Styled.TableContent>
              ))}
          </Styled.TableContainer>

          <Styled.WeeklyReportCardLayout>
            {crew?.members?.map((member: any, index: number) => (
              <Fragment>
                <WeeklyProductionReportCard
                  name={member?.name}
                  member={member}
                  baseWage={member?.baseWage}
                  crewProduction={member?.percent}
                  dayOff={member?.daysOff}
                  ptoUsedNum={member?.ptoUsed}
                  hrs={member?.hours}
                  rr={member?.rr}
                  rrEarned={member?.rrDollar}
                  totalBudget={member?.budget}
                  totalEarned={member?.totalEarned}
                  cards={member?.cards}
                  crewName={crew?.name}
                  memberIndex={index}
                />
              </Fragment>
            ))}
          </Styled.WeeklyReportCardLayout>
        </div>
      ))}
      <CustomModal show={showCrewDailyLog} className="top">
        {showCrewDailyLog && (
          <DailyLog
            isEdit={logId ? true : false}
            onClose={() => {
              setShowCrewDailyLog(false)
              setIsEditDailyLog(false)
              // const currentDateupdated: any = localStorage.getItem('currentDate')
              // getApproveTimeCardsDetail(currentDateupdated)
            }}
            selectedCrewId={selectedCrew}
            selectedCrewName={selectedCrewName}
            logId={logId}
            reportD={reportD}
            isReportDailyLog
            pickPo={poList}
          />
        )}
      </CustomModal>
    </Styled.WeeklyReportMainLayout>
  )
}

export default WeeklyProductionReport
