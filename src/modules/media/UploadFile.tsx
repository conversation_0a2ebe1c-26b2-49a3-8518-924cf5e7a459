import { useRef } from 'react'
import UploadIcon from '../../assets/newIcons/uploadIcon.svg'
import { generateUUID, isSuccess, notify } from '../../shared/helpers/util'
import { createMediaOpportunity, FilePayload, getPresignedUrlMedia } from '../../logic/apis/media'
import { compressImage, extractImageData, getThumbnailUrl, validateFiles } from './mediaUtils'
import { FilePathTypeEnum } from '../../shared/helpers/constants'
import { processFile } from './Media'

const UploadFile = ({
  allowedMIMETypes,
  setUploadLoading,
  onComplete,
  memberId,
  oppId,
  company,
}: {
  allowedMIMETypes: string[]
  onComplete?: () => void
  setUploadLoading: React.Dispatch<React.SetStateAction<boolean>>
  memberId: string
  oppId?: string
  company: any
}) => {
  const uploadRef = useRef(null)

  const handleFileChange = async (e: any) => {
    try {
      setUploadLoading(true)
      const selectedFiles = Array.from(e.target.files)

      const processedFiles = await Promise.all(selectedFiles.map(processFile))

      const newFiles = processedFiles.map((file: any) => ({
        url: URL.createObjectURL(file),
        mimetype: file.type,
        name: file.name,
        file,
      }))

      const validFiles = validateFiles(newFiles, {
        maxImageSizeMB: company?.companySettingForAll?.maxImageSizeMB,
        maxVideoSizeMB: company?.companySettingForAll?.maxVideoSizeMB,
      })

      if (validFiles?.length) {
        const res = await getPresignedUrlMedia(
          FilePathTypeEnum.Project,
          validFiles.map((itm: any) => ({
            fileName: itm.name,
            mimetype: itm.mimetype.includes('video') ? 'video/mp4' : itm.mimetype,
          })),
          memberId!,
          oppId!
        )
        if (!isSuccess(res)) {
          return
        }

        const urlData: Record<string, any> = res?.data?.data?.signedUrls?.reduce((acc: any, itm: any) => {
          acc[itm.fileName] = itm
          return acc
        }, {})

        const uploadPromises = validFiles.map(async (itm: any) => {
          const myHeaders = new Headers()
          myHeaders.append('Content-Type', itm.mimetype.includes('video') ? 'video/mp4' : itm.mimetype)

          const file = itm?.mimeType?.includes('image') ? (await compressImage(itm.file))?.file : itm.file

          const requestOptions = {
            method: 'PUT',
            headers: myHeaders,
            body: file,
          }

          const response = await fetch(urlData[itm.name]?.url, requestOptions)
          if (!response.ok) {
            notify('Failed to upload', 'error')
            throw new Error(`Failed to upload ${itm.name}: ${response.statusText}`)
          }
          return response
        })

        await Promise.all(uploadPromises)

        const payload: FilePayload[] = []

        const onlyImages = validFiles.filter((itm: any) => itm.mimetype.includes('image'))

        const metaData = await extractImageData(onlyImages)

        validFiles?.forEach((itm: any) => {
          if (itm?.mimetype?.includes('image')) {
            payload.push({
              _id: generateUUID()!,
              createdAt: metaData?.[itm?.name]?.createdAt,
              createdBy: memberId,
              mimetype: itm?.mimetype,
              name: itm?.name,
              url: urlData?.[itm?.name]?.url?.split('?')[0],
              thumbnail: getThumbnailUrl(urlData?.[itm?.name]?.url?.split('?')[0]),
              location: metaData?.[itm?.name]?.location || undefined,
              tags: [],
            })
          } else {
            payload.push({
              _id: generateUUID()!,
              createdAt: new Date().toISOString(),
              createdBy: memberId,
              mimetype: itm.mimetype.includes('video') ? 'video/mp4' : itm.mimetype,
              name: itm?.name,
              url: urlData?.[itm?.name]?.url?.split('?')[0],
              thumbnail: getThumbnailUrl(urlData?.[itm?.name]?.url?.split('?')[0]),
              tags: [],
            })
          }
        })

        const uploadRes = await createMediaOpportunity(oppId!, payload)

        if (isSuccess(uploadRes)) {
          notify(uploadRes?.data?.data?.message, 'success')
          setUploadLoading(false)

          onComplete?.()
        }
      }
    } catch (error) {
      console.log('error======>', error)
    } finally {
      setUploadLoading(false)
    }
  }

  return (
    <>
      <input
        type="file"
        ref={uploadRef}
        style={{
          visibility: 'hidden',
          width: '0px',
        }}
        onChange={handleFileChange}
        accept={`${allowedMIMETypes.join(',')}`}
        multiple
      />
      <img
        src={UploadIcon}
        alt="upload icon"
        style={{
          width: '20px',
          cursor: 'pointer',
        }}
        onClick={() => {
          // @ts-ignore
          uploadRef.current.click()
        }}
      />
    </>
  )
}

export default UploadFile
