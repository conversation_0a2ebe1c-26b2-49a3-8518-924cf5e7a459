import { Form, Formik } from 'formik'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useParams } from 'react-router-dom'
import * as Yup from 'yup'

import UnitSvg from '../../../../assets/newIcons/unitModal.svg'
import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import { getPosition } from '../../../../logic/apis/position'
import { createTask } from '../../../../logic/apis/task'
import { getDataFromLocalStorage, isSuccess, notify } from '../../../../shared/helpers/util'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import { Table } from '../../../../shared/table/Table'
import * as SharedStyled from '../../../../styles/styled'
import * as Styled from './style'
import { ModalHeaderInfo } from '../../../units/components/newUnitModal/style'
import Button from '../../../../shared/components/button/Button'
import { SLoader } from '../../../../shared/components/loader/Loader'
import Toggle from '../../../../shared/toggle/Toggle'
import { StorageKey, SubscriptionPlanType } from '../../../../shared/helpers/constants'

/**
 * InitialValues is an interface declared here so that it can be used as a type for the useState hook
 */
interface InitialValues {
  taskName: string
  description: string
  rate: number
  position: Array<any>
}

/**
 * I_CreateTaskPopUp is an interface which defines the props type which we receive from our parent component to this component
 */
interface I_CreateTaskPopUp {
  setShowCreateTaskPopUp: React.Dispatch<React.SetStateAction<boolean>>
  setDetailsUpdate: React.Dispatch<React.SetStateAction<boolean>>
}

export const CreateTaskPopUp = (props: I_CreateTaskPopUp) => {
  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */
  const [initialValues, setInitialValues] = useState<InitialValues>({
    taskName: '',
    description: '',
    rate: 0,
    position: [],
  })

  /**
   * loading will be the loading state when performing the operations
   */
  const [loading, setLoading] = useState<boolean>(false)
  const [positionLoading, setPositionLoading] = useState(false)

  const [isPieceWork, setIsPieceWork] = useState(false)
  const [isTravel, setIsTravel] = useState(false)

  const [isScoreboard, setIsScoreboard] = useState(false)

  const globalSelector: any = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company

  const isProPlusPlan = currentCompany?.planType === SubscriptionPlanType.PROPLUS

  /**
   * Destructuring the values from the props received
   */
  const { setShowCreateTaskPopUp, setDetailsUpdate } = props

  /**
   * TaskCreationFormSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */
  const TaskCreationFormSchema = Yup.object().shape({
    taskName: Yup.string().required('No task name provided.'),
    description: Yup.string().required('No description provided.'),
  })

  const [pageCount, setPageCount] = useState<number>(10)

  const [data, setData] = useState<any>([])
  const fetchIdRef = useRef(0)

  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [inputRef])

  const fetchData = useCallback(async ({ pageSize, pageIndex }: any) => {
    try {
      // This will get called when the table needs new data

      let receivedData: any = []

      // console.log('currentCompany', currentCompany)
      // let currentCompanyData: any = localStorage.getItem('currentCompany')

      // const statusResponse = await getTeamMembers(
      //   { skip: pageIndex, limit: pageSize, deleted: false, companyId: JSON.parse(currentCompanyData)._id },
      //   id
      // )

      // if (isSuccess(statusResponse)) {
      //   let statusRes = statusResponse?.data?.data?.memberData
      //   statusRes.forEach((res: any, index: number) => {
      //     receivedData.push({
      //       name: res.name,
      //       username: res.username,
      //       email: res.email,
      //       id: res.user,
      //       memberId: res._id,
      //       managerId: res?.managerId ? res.managerId : '0',
      //       position: res?.position ? res?.position : '0',
      //     })
      //   })
      //   console.log('receivedData', receivedData)
      // } else {
      //   notify(statusResponse?.data?.message, 'error')
      // }

      // if (initialPermissionData) {
      Array(4)
        .fill(0)
        .map((permission: any, index: number) =>
          receivedData.push({
            permissionName: <Styled.NameText>Test</Styled.NameText>,
            full: (
              <Styled.CheckBox
                width="15px"
                height="20px"
                type="checkbox"
                // value={checkboxState[index]?.full}
                // checked={checkboxState[index]?.full}
                // onChange={(e: any) => {
                //   onPermissionChange('Full', index, e.target.checked)
                // }}
              />
            ),
            managed: (
              <Styled.CheckBox
                width="15px"
                height="20px"
                type="checkbox"
                // value={checkboxState[index]?.managed}
                // checked={checkboxState[index]?.managed}
                // onChange={(e: any) => {
                //   onPermissionChange('Managed', index, e.target.checked)
                // }}
              />
            ),
            self: (
              <Styled.CheckBox
                width="15px"
                height="20px"
                type="checkbox"
                // value={checkboxState[index]?.self}
                // checked={checkboxState[index]?.self}
                // onChange={(e: any) => {
                //   onPermissionChange('Self', index, e.target.checked)
                // }}
              />
            ),
            none: (
              <Styled.CheckBox
                width="15px"
                height="20px"
                type="checkbox"
                // value={checkboxState[index]?.none}
                // checked={checkboxState[index]?.none}
                // onChange={(e: any) => {
                //   onPermissionChange('None', index, e.target.checked)
                // }}
              />
            ),
          })
        )
      // }
      // Give this fetch an ID
      const fetchId = ++fetchIdRef.current

      // Set the loading state
      // setLoading(true)

      // We'll even set a delay to simulate a server here
      setTimeout(() => {
        // Only update the data if this is the latest fetch
        if (fetchId === fetchIdRef.current) {
          const startRow = pageSize * pageIndex
          const endRow = startRow + pageSize
          setData(receivedData.slice(startRow, endRow))

          // Your server could send back total page count.
          // For now we'll just fake it, too
          setPageCount(Math.ceil(receivedData.length / pageSize))
          // setLoading(false)
        }
      }, 1000)
    } catch (error) {
      console.error('TeamTable fetchData error', error)
    }
  }, [])

  const columns: any = useMemo(
    () => [
      {
        Header: 'Permission Name',
        accessor: 'permissionName',
      },
      {
        Header: 'Full',
        accessor: 'full',
      },
      {
        Header: 'Managed',
        accessor: 'managed',
      },
      {
        Header: 'Self',
        accessor: 'self',
      },
      {
        Header: 'None',
        accessor: 'none',
      },
    ],
    []
  )

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async (submittedValues: InitialValues, { resetForm }: any) => {
    try {
      // if (Object.keys(currentCompany).length > 0 && Object.keys(currentMember).length > 0) {
      setLoading(true)
      let posArray: any = []
      submittedValues.position.forEach((pos: any, index: number) => {
        if (pos.selected) {
          posArray.push(pos._id)
        }
      })
      let dataObj = {
        name: submittedValues.taskName,
        description: submittedValues.description,
        rate: submittedValues.rate,
        position: posArray,
        createdBy: currentMember._id,
        pieceWork: isPieceWork,
        showOnScoreboard: isProPlusPlan ? isScoreboard : undefined,
        addTravel: isProPlusPlan ? isTravel : undefined,
      }
      let response = await createTask(dataObj)

      if (isSuccess(response)) {
        notify('Task Created Successfully', 'success')
        resetForm()
        setDetailsUpdate((prev) => !prev)
        setLoading(false)
        setShowCreateTaskPopUp(false)
      } else {
        setLoading(false)
        notify(response?.data?.message, 'error')
      }
      // }
    } catch (error) {
      setLoading(false)
      console.error('Task Creation error', error)
    }
  }

  const getPositionsDetails = async () => {
    try {
      if (Object.keys(currentCompany).length > 0) {
        setPositionLoading(true)
        const response = await getPosition({ deleted: false, limit: 200 }, false)
        if (isSuccess(response)) {
          let statusPosRes = response?.data?.data?.position
          let positionArray: any = []
          statusPosRes.forEach((pos: any, index: number) =>
            positionArray.push({
              name: pos.position,
              _id: pos._id,
              selected: true,
            })
          )
          setInitialValues({ ...initialValues, position: [...positionArray] })
        }
      }
    } catch (error) {
      console.error('getPositionsDetails error', error)
    } finally {
      setPositionLoading(false)
    }
  }

  useEffect(() => {
    getPositionsDetails()
  }, [])

  return (
    <Styled.CreateTaskPopUpContainer>
      {' '}
      <Formik
        initialValues={initialValues}
        onSubmit={handleSubmit}
        validationSchema={TaskCreationFormSchema}
        validateOnChange={true}
        validateOnBlur={false}
        enableReinitialize={true}
      >
        {/* <Styled.ResetpasswordContainer> */}
        {({ touched, errors, resetForm, values }) => {
          return (
            <>
              <Styled.ModalHeaderContainer>
                <SharedStyled.FlexRow>
                  <img src={UnitSvg} alt="modal icon" />
                  <SharedStyled.FlexCol>
                    <Styled.ModalHeader>Create Task</Styled.ModalHeader>
                  </SharedStyled.FlexCol>
                </SharedStyled.FlexRow>
                <Styled.CrossContainer
                  onClick={() => {
                    setShowCreateTaskPopUp(false)
                    setLoading(false)
                    resetForm()
                  }}
                >
                  <CrossIcon />
                </Styled.CrossContainer>
              </Styled.ModalHeaderContainer>
              <SharedStyled.SettingModalContentContainer>
                <Form className="form">
                  <SharedStyled.Content maxWidth="706px" width="100%" disableBoxShadow={true} noPadding={true}>
                    <InputWithValidation
                      labelName="Task Name"
                      stateName="taskName"
                      error={touched.taskName && errors.taskName ? true : false}
                      passRef={inputRef}
                    />
                    <InputWithValidation
                      labelName="Description"
                      stateName="description"
                      error={touched.description && errors.description ? true : false}
                    />
                    <InputWithValidation
                      labelName="Rate"
                      stateName="rate"
                      forceType="number"
                      error={touched.rate && errors.rate ? true : false}
                    />
                    <Styled.CheckDivContainer className="piecework">
                      <Toggle
                        title="Piece Work"
                        isToggled={isPieceWork}
                        onToggle={() => {
                          setIsPieceWork((prev) => !prev)
                        }}
                      />
                    </Styled.CheckDivContainer>
                    {isProPlusPlan ? (
                      <Styled.CheckDivContainer className="piecework">
                        <Toggle
                          title="Show On Scoreboard"
                          isToggled={isScoreboard}
                          onToggle={() => {
                            setIsScoreboard((prev: boolean) => !prev)
                          }}
                        />
                      </Styled.CheckDivContainer>
                    ) : null}
                    {isProPlusPlan ? (
                      <Styled.CheckDivContainer className="piecework">
                        <Toggle
                          title="Add travel"
                          isToggled={isTravel}
                          onToggle={() => {
                            setIsTravel((prev: boolean) => !prev)
                          }}
                        />
                      </Styled.CheckDivContainer>
                    ) : null}
                    <SharedStyled.FlexRow margin="12px 0">
                      <Styled.FieldHeader>Select Positions:</Styled.FieldHeader>
                    </SharedStyled.FlexRow>
                    <Styled.CheckContainer>
                      {positionLoading ? (
                        <>
                          <SharedStyled.FlexCol gap="10px">
                            <SLoader width={150} height={18} />
                            <SLoader width={150} height={18} />
                            <SLoader width={150} height={18} />
                            <SLoader width={150} height={18} />
                          </SharedStyled.FlexCol>
                        </>
                      ) : (
                        values?.position?.map((pos: any, index: number) => (
                          <Styled.CheckDivContainer key={index}>
                            <Styled.NameText>{pos?.name}</Styled.NameText>
                            <SharedStyled.CheckboxZoneLabel>
                              <Styled.CheckBox
                                name={`position.${index}.selected`}
                                type="checkbox"
                                width="15px"
                                height="20px"
                              />
                            </SharedStyled.CheckboxZoneLabel>
                          </Styled.CheckDivContainer>
                        ))
                      )}
                    </Styled.CheckContainer>

                    {/* <Table
                      columns={columns}
                      data={data}
                      loading={loading}
                      pageCount={pageCount}
                      fetchData={fetchData}
                      noLink={true}
                      noPagination={true}
                      noSearch={true}
                    /> */}
                    <SharedStyled.ButtonContainer marginTop="20px">
                      <Button type="submit" isLoading={loading}>
                        Create Task
                      </Button>
                    </SharedStyled.ButtonContainer>
                  </SharedStyled.Content>
                </Form>
              </SharedStyled.SettingModalContentContainer>
            </>
          )
        }}
      </Formik>
    </Styled.CreateTaskPopUpContainer>
  )
}
