import { Form, Formik } from 'formik'
import { useEffect, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useParams } from 'react-router-dom'
import * as Yup from 'yup'

import UnitSvg from '../../../../assets/newIcons/unitModal.svg'
import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import { updateDepartment } from '../../../../logic/apis/department'
import { getPosition } from '../../../../logic/apis/position'
import { updateTask } from '../../../../logic/apis/task'
import { getDataFromLocalStorage, isSuccess, notify } from '../../../../shared/helpers/util'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import * as SharedStyled from '../../../../styles/styled'
import { ModalHeaderInfo } from '../../../units/components/newUnitModal/style'
import * as Styled from './style'
import Button from '../../../../shared/components/button/Button'
import Toggle from '../../../../shared/toggle/Toggle'
import { StorageKey, SubscriptionPlanType } from '../../../../shared/helpers/constants'

/**
 * InitialValues is an interface declared here so that it can be used as a type for the useState hook
 */
interface InitialValues {
  taskName: string
  description: string
  rate: number
  position: Array<any>
}

/**
 * I_CreateDepartmentPopUp is an interface which defines the props type which we receive from our parent component to this component
 */
interface I_EditTaskPopUp {
  setShowEditTaskPopUp: React.Dispatch<React.SetStateAction<boolean>>
  setDetailsUpdate: React.Dispatch<React.SetStateAction<boolean>>
  taskData: any
}

export const EditTaskPopUp = (props: I_EditTaskPopUp) => {
  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */
  const [initialValues, setInitialValues] = useState<InitialValues>({
    taskName: '',
    description: '',
    rate: 0,
    position: [],
  })

  const { setShowEditTaskPopUp, setDetailsUpdate, taskData } = props

  /**
   * loading will be the loading state when performing the operations
   */
  const [loading, setLoading] = useState<boolean>(false)
  const [shimmerLoading, setShimmerLoading] = useState<boolean>(true)
  const [isScoreboard, setIsScoreboard] = useState(taskData?.showOnScoreboard ?? false)
  const [isPieceWork, setIsPieceWork] = useState(taskData?.pieceWork ?? false)
  const [isTravel, setIsTravel] = useState(taskData?.addTravel ?? false)

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company

  const isProPlusPlan = currentCompany?.planType === SubscriptionPlanType.PROPLUS

  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [inputRef])
  /**
   * Destructuring the values from the props received
   */

  /**
   * TaskCreationFormSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */
  const TaskCreationFormSchema = Yup.object().shape({
    taskName: Yup.string().required('No task name provided.'),
    description: Yup.string(),
  })

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async (submittedValues: InitialValues, { resetForm }: any) => {
    try {
      // if (Object.keys(currentCompany).length > 0 && Object.keys(currentMember).length > 0) {
      setLoading(true)
      let posArray: any = []
      submittedValues.position.forEach((pos: any, index: number) => {
        if (pos.selected) {
          posArray.push(pos._id)
        }
      })
      let dataObj = {
        workTaskId: taskData.id,
        name: submittedValues.taskName,
        description: submittedValues.description,
        rate: submittedValues.rate,
        createdBy: currentMember._id,
        position: posArray,
        pieceWork: isPieceWork,
        showOnScoreboard: isProPlusPlan ? isScoreboard : undefined,
        addTravel: isProPlusPlan ? isTravel : undefined,
      }

      let response = await updateTask(dataObj)
      if (isSuccess(response)) {
        notify('Task Edited Successfully', 'success')
        resetForm()
        setDetailsUpdate((prev) => !prev)
        setLoading(false)
        setShowEditTaskPopUp(false)
      } else {
        setLoading(false)
        notify(response?.data?.message, 'error')
      }
      // }
    } catch (error) {
      setLoading(false)

      console.error('Task Creation error', error)
    }
  }

  // const setInitialValuesFunc = () => {
  //   try {
  //     setInitialValues({
  //       ...initialValues,
  //       taskName: taskData.taskName,
  //       description: taskData.description,
  //     })
  //   } catch (error) {
  //     console.error('Edit task setInitialValues error', error)
  //   }
  // }

  const getPositionsDetails = async () => {
    try {
      if (taskData?.position?.length > 0) {
        setShimmerLoading(true)
        const response = await getPosition({ deleted: false, limit: '200' }, false)
        if (isSuccess(response)) {
          let statusPosRes = response?.data?.data?.position
          let positionArray: any = []
          statusPosRes.forEach((pos: any, index: number) => {
            if (taskData.position.includes(pos._id)) {
              positionArray.push({
                name: pos.position,
                _id: pos._id,
                selected: true,
              })
            } else {
              positionArray.push({
                name: pos.position,
                _id: pos._id,
                selected: false,
              })
            }
          })
          setInitialValues({
            ...initialValues,
            position: [...positionArray],
            taskName: taskData.taskName,
            description: taskData.description,
            rate: taskData.rate,
          })
          setShimmerLoading(false)
        }
      }
    } catch (error) {
      console.error('getPositionsDetails error', error)
      setShimmerLoading(false)
    }
  }

  useEffect(() => {
    getPositionsDetails()
  }, [taskData])

  // useEffect(() => {
  //   setInitialValuesFunc()
  // }, [taskData])

  return (
    <Styled.EditTaskPopUpContainer>
      {' '}
      <Formik
        initialValues={initialValues}
        onSubmit={handleSubmit}
        validationSchema={TaskCreationFormSchema}
        validateOnChange={true}
        validateOnBlur={false}
        enableReinitialize={true}
      >
        {/* <Styled.ResetpasswordContainer> */}
        {({ touched, errors, resetForm, values }) => (
          <>
            <Styled.ModalHeaderContainer>
              <SharedStyled.FlexRow>
                <img src={UnitSvg} alt="modal icon" />
                <SharedStyled.FlexCol>
                  <Styled.ModalHeader>Edit Work Type</Styled.ModalHeader>
                </SharedStyled.FlexCol>
              </SharedStyled.FlexRow>
              <Styled.CrossContainer
                onClick={() => {
                  resetForm()
                  setLoading(false)
                  setShowEditTaskPopUp(false)
                }}
              >
                <CrossIcon />
              </Styled.CrossContainer>
            </Styled.ModalHeaderContainer>
            <SharedStyled.SettingModalContentContainer>
              {shimmerLoading ? (
                <SharedStyled.FlexBox width="100%" flexDirection="column" gap="5px">
                  <SharedStyled.Skeleton custWidth="100%" custHeight={'51px'} />
                  <SharedStyled.Skeleton custWidth="100%" custHeight="51px" custMarginTop="10px" />
                  <SharedStyled.Skeleton custWidth="100%" custHeight="51px" custMarginTop="10px" />
                </SharedStyled.FlexBox>
              ) : (
                <Form className="form">
                  <SharedStyled.Content maxWidth="706px" width="100%" disableBoxShadow={true} noPadding={true}>
                    <InputWithValidation
                      labelName="Task Name"
                      stateName="taskName"
                      error={touched.taskName && errors.taskName ? true : false}
                      passRef={inputRef}
                    />
                    <InputWithValidation
                      labelName="Description"
                      stateName="description"
                      error={touched.description && errors.description ? true : false}
                    />
                    <InputWithValidation
                      labelName="Rate"
                      stateName="rate"
                      forceType="number"
                      error={touched.rate && errors.rate ? true : false}
                    />
                    <Styled.CheckDivContainer className="piecework">
                      <Toggle
                        title="Piece Work"
                        isToggled={isPieceWork}
                        onToggle={() => {
                          setIsPieceWork((prev: any) => !prev)
                        }}
                      />
                    </Styled.CheckDivContainer>
                    {isProPlusPlan ? (
                      <Styled.CheckDivContainer className="piecework">
                        <Toggle
                          title="Show On Scoreboard"
                          isToggled={isScoreboard}
                          onToggle={() => {
                            setIsScoreboard((prev: boolean) => !prev)
                          }}
                        />
                      </Styled.CheckDivContainer>
                    ) : null}
                    {isProPlusPlan ? (
                      <Styled.CheckDivContainer className="piecework">
                        <Toggle
                          title="Add travel"
                          isToggled={isTravel}
                          onToggle={() => {
                            setIsTravel((prev: boolean) => !prev)
                          }}
                        />
                      </Styled.CheckDivContainer>
                    ) : null}

                    <SharedStyled.FlexRow margin="12px 0">
                      <Styled.FieldHeader>Select Positions:</Styled.FieldHeader>
                    </SharedStyled.FlexRow>
                    <Styled.CheckContainer>
                      {values?.position?.map((pos: any, index: number) => (
                        <Styled.CheckDivContainer key={index}>
                          <Styled.NameText>{pos?.name}</Styled.NameText>
                          <SharedStyled.CheckboxZoneLabel>
                            <Styled.CheckBox
                              name={`position.${index}.selected`}
                              type="checkbox"
                              width="15px"
                              height="20px"
                            />
                          </SharedStyled.CheckboxZoneLabel>
                        </Styled.CheckDivContainer>
                      ))}
                    </Styled.CheckContainer>
                    <SharedStyled.ButtonContainer marginTop="20px">
                      <Button type="submit" isLoading={loading}>
                        Save Changes
                      </Button>
                    </SharedStyled.ButtonContainer>
                  </SharedStyled.Content>
                </Form>
              )}
            </SharedStyled.SettingModalContentContainer>
          </>
        )}
      </Formik>
    </Styled.EditTaskPopUpContainer>
  )
}
