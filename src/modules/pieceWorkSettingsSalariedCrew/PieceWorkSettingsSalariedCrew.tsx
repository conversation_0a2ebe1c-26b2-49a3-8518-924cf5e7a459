import { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { useParams } from 'react-router-dom'

import { AddIconNoCircleIcon } from '../../assets/icons/AddIconNoCircleIcon'
import { DeleteIcon } from '../../assets/icons/DeleteIcon'
import { getPieceWorkSetting, upsertPieceWorkSetting } from '../../logic/apis/pieceWork'
import { CustomModal } from '../../shared/customModal/CustomModal'
import { getDataFromLocalStorage, isSuccess, notify } from '../../shared/helpers/util'
import * as SharedStyled from '../../styles/styled'
import { AddCustomRow } from './components/AddCustomRow/AddCustomRow'
import { AddCustomSettings } from './components/AddCustomSettings/AddCustomSettings'
import { RemoveCustomRow } from './components/RemoveCustomRow/RemoveCustomRow'
import * as Styled from './style'
import { ButtonCont, SettingsCont } from '../units/style'
import Button from '../../shared/components/button/Button'
import AddNewPieceWork from './components/AddNewPieceWork/AddNewPieceWork'
import { StorageKey } from '../../shared/helpers/constants'

const PieceWorkSettingsSalariedCrew = () => {
  const [showAddCustomSettingsPopup, setShowAddCustomSettingsPopup] = useState(false)
  const [showAddCustomRowPopup, setShowAddCustomRowPopup] = useState(false)
  const [showRemoveCustomRowPopup, setShowRemoveCustomRowPopup] = useState(false)
  const [saveData, setSaveData] = useState<any>([])
  const [loading, setLoading] = useState<boolean>(false)
  const [detailsUpdate, setDetailsUpdate] = useState(false)
  const [tearOffDetails, setTearOffDetails] = useState<any>([])
  const [tearOffDetailsNone, setTearOffDetailsNone] = useState<any>([])
  const [newSubType, setNewSubType] = useState<number>(0)
  const [newRowDetail, setNewRowDetail] = useState<any>({})
  const [toDeleteRow, setToDeleteRow] = useState<any>({})
  const [roofingDetails, setRoofingDetails] = useState<any>([])
  const [roofingDetailsNone, setRoofingDetailsNone] = useState<any>([])
  const [customDetails, setCustomDetails] = useState<any>([])
  const [pitchDetails, setPitchDetails] = useState<any>([])
  const [shimmerLoading, setShimmerLoading] = useState<boolean>(true)
  const [showNewPieceWork, setShowNewPieceWork] = useState(false)

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company

  const [selectedPiecework, setSelectedPiecework] = useState({})

  const getPieceWorkSettingsDetail = async () => {
    try {
      // if (Object.keys(currentCompany).length > 0) {
      let response = await getPieceWorkSetting({ deleted: false })

      if (isSuccess(response)) {
        let resData = response?.data?.data?.pieceWorkSetting
        let dataObj: any = {}
        let tear: any = []
        let tearNone: any = []
        let roof: any = []
        let roofNone: any = []
        let cust: any = []
        let pitch: any = []
        let subTypeValue = 0
        resData.forEach((data: any) => {
          let name = data?._id?.type.replace('_', ' ')
          let workerType = data?._id?.workerType
          let subType = data?._id?.subType
          let displayData = [...data.data]
          data?.data?.forEach((dat: any, index: number) => {
            if (dat?.unit === '%') {
              let obj = { ...dat, amount: dat?.amount * 100 }
              displayData.splice(index, 1, obj)
            }
          })
          if (name === 'Tear Off' && workerType === 'salaried') {
            const modifiedData = displayData.map((item) => ({
              ...item,
              workerType: 'salaried',
            }))
            tear.push(modifiedData)
          } else if (name === 'Tear Off' && workerType === 'default') {
            const modifiedData = displayData.map((item) => ({
              ...item,
              workerType: 'default',
            }))
            tearNone.push(modifiedData)
          } else if (name === 'Roofing' && workerType === 'salaried') {
            const modifiedData = displayData.map((item) => ({
              ...item,
              workerType: 'salaried',
            }))
            roof.push(modifiedData)
          } else if (name === 'Roofing' && workerType === 'default') {
            const modifiedData = displayData.map((item) => ({
              ...item,
              workerType: 'default',
            }))
            roofNone.push(modifiedData)
          } else if (name === 'Pitch Modifier') {
            pitch.push(displayData)
          } else {
            cust.push(displayData)
          }
          if (subType > subTypeValue) {
            subTypeValue = subType
          }
        })
        setNewSubType(subTypeValue + 1)
        setTearOffDetails(tear)
        setTearOffDetailsNone(tearNone)
        setRoofingDetails(roof)
        setRoofingDetailsNone(roofNone)
        setPitchDetails(pitch)
        setCustomDetails(cust)
        setShimmerLoading(false)
      } else {
        notify(response?.data?.message, 'error')
        setShimmerLoading(false)
      }
      // }
    } catch (error) {
      console.error('getPieceWorkSettingsDetail error', error)
      setShimmerLoading(false)
    }
  }
  const onSave = async () => {
    try {
      // if (Object.keys(currentCompany).length > 0) {
      if (saveData.length > 0) {
        setLoading(true)
        let dummySaveData = [...saveData]
        saveData.forEach((dat: any, index: number) => {
          if (dat?.unit === '%') {
            let obj = { ...dat, amount: Number(dat?.amount) / 100 }
            dummySaveData.splice(index, 1, obj)
          }
        })
        let response = await upsertPieceWorkSetting(dummySaveData)
        if (isSuccess(response)) {
          notify('Saved Data Successfully', 'success')
          getPieceWorkSettingsDetail()
          setSaveData([])
          setLoading(false)
        } else {
          notify(response?.data?.message, 'error')
          setLoading(false)
        }
      } else {
        notify('No Fields Edited Yet to Save', 'info')
      }
      // }
    } catch (error) {
      console.error('onSave error', error)
      setLoading(false)
    }
  }

  const onChangeTearOffValue = (indexOuter: number, indexInner: number) => (e: any) => {
    try {
      // if (Object.keys(currentCompany).length > 0) {
      let dataArray = [...tearOffDetails]
      let dataObj = { ...dataArray[indexOuter][indexInner], amount: e.target.value }
      setSaveData([...saveData, { ...dataObj, createdBy: currentMember._id }])
      dataArray[indexOuter][indexInner] = { ...dataObj }
      setTearOffDetails(dataArray)
      // }
    } catch (error) {
      console.error('onChangeTearOffValue error', error)
    }
  }

  const onChangeRoofingValue = (indexOuter: number, indexInner: number) => (e: any) => {
    try {
      // if (Object.keys(currentCompany).length > 0) {
      let dataArray = [...roofingDetails]
      let dataObj = { ...dataArray[indexOuter][indexInner], amount: e.target.value }
      setSaveData([...saveData, { ...dataObj, createdBy: currentMember._id }])
      dataArray[indexOuter][indexInner] = { ...dataObj }
      setRoofingDetails(dataArray)
      // }
    } catch (error) {
      console.error('onChangeRoofingValue error', error)
    }
  }

  const onChangeCustomValue = (indexOuter: number, indexInner: number) => (e: any) => {
    try {
      // if (Object.keys(currentCompany).length > 0) {
      let dataArray = [...customDetails]
      let dataObj = { ...dataArray[indexOuter][indexInner], amount: e.target.value }
      setSaveData([...saveData, { ...dataObj, createdBy: currentMember._id }])
      dataArray[indexOuter][indexInner] = { ...dataObj }
      setCustomDetails(dataArray)
      // }
    } catch (error) {
      console.error('onChangeCustomValue error', error)
    }
  }

  const onChangePitchModifierValue = (indexOuter: number, indexInner: number) => (e: any) => {
    try {
      // if (Object.keys(currentCompany).length > 0) {
      let dataArray = [...pitchDetails]
      let dataObj = { ...dataArray[indexOuter][indexInner], amount: e.target.value }
      setSaveData([...saveData, { ...dataObj, createdBy: currentMember._id }])
      dataArray[indexOuter][indexInner] = { ...dataObj }
      setPitchDetails(dataArray)
      // }
    } catch (error) {
      console.error('onChangePitchModifierValue error', error)
    }
  }

  useEffect(() => {
    getPieceWorkSettingsDetail()
  }, [detailsUpdate])

  return (
    <>
      {shimmerLoading ? (
        <>
          <SharedStyled.Skeleton custWidth="100%" custHeight={'51px'} />
          <SharedStyled.Skeleton custWidth="100%" custHeight="50%" custMarginTop="10px" />
          <SharedStyled.Skeleton custWidth="100%" custHeight="50%" custMarginTop="10px" />
        </>
      ) : (
        <>
          {/* <Styled.PieceWorkSettingsSalariedCrewContainer> */}
          <SettingsCont gap="24px">
            <SharedStyled.FlexRow justifyContent="space-between" flexWrap="wrap">
              <SharedStyled.SectionTitle>Piece Work (Salaried Crew)</SharedStyled.SectionTitle>
              <ButtonCont>
                <Button
                  onClick={() => {
                    setShowAddCustomSettingsPopup(true)
                  }}
                >
                  Add Column
                </Button>
              </ButtonCont>
            </SharedStyled.FlexRow>

            {/* <SharedStyled.ContentHeader textAlign="left">Piece Work(Salaried Crew)</SharedStyled.ContentHeader>
            <SharedStyled.HorizontalDivider /> */}
            <div style={{ width: '100%' }}>
              <SharedStyled.FlexRow>
                {tearOffDetails.length > 0 && <Styled.HeadingDiv>Tear Off</Styled.HeadingDiv>}
                <Button width="max-content" padding="8px 14px" onClick={() => setShowNewPieceWork(true)}>
                  Add new piece work
                </Button>
              </SharedStyled.FlexRow>

              <Styled.SingleWorkSettingsGridContainer marginTop="15px">
                {tearOffDetails?.map((detail: any, index1: number) => (
                  <Styled.SingleColumnDiv key={detail._id}>
                    <Styled.DescriptionDiv>{detail[0].description}</Styled.DescriptionDiv>
                    {detail
                      .sort((a: any, b: any) => a.order - b.order)
                      .map((det: any, index: number) => (
                        <Styled.NameValueUnitContainer
                          key={index}
                          onClick={() => {
                            setSelectedPiecework(det)
                            setShowNewPieceWork(true)
                          }}
                        >
                          <Styled.NameDiv>{det.name}:</Styled.NameDiv>
                          <Styled.ValueInput
                            type="number"
                            value={det.amount}
                            onClick={(e: any) => e.stopPropagation()}
                            onChange={onChangeTearOffValue(index1, index)}
                          />
                          <Styled.UnitDiv>{det.unit}</Styled.UnitDiv>
                        </Styled.NameValueUnitContainer>
                      ))}
                  </Styled.SingleColumnDiv>
                ))}
              </Styled.SingleWorkSettingsGridContainer>
            </div>

            <div style={{ width: '100%' }}>
              {roofingDetails.length > 0 && <Styled.HeadingDiv marginTop="10px">Roofing</Styled.HeadingDiv>}
              <Styled.SingleWorkSettingsGridContainer marginTop="15px">
                {roofingDetails?.map((detail: any, index1: number) => (
                  <Styled.SingleColumnDiv key={detail._id}>
                    <Styled.DescriptionDiv>{detail[0].description}</Styled.DescriptionDiv>
                    {detail
                      .sort((a: any, b: any) => a.order - b.order)
                      .map((det: any, index: number) => (
                        <Styled.NameValueUnitContainer key={index}>
                          <Styled.NameDiv>{det.name}:</Styled.NameDiv>
                          <Styled.ValueInput
                            type="number"
                            value={det.amount}
                            onChange={onChangeRoofingValue(index1, index)}
                          />
                          <Styled.UnitDiv>{det.unit}</Styled.UnitDiv>
                        </Styled.NameValueUnitContainer>
                      ))}
                  </Styled.SingleColumnDiv>
                ))}
              </Styled.SingleWorkSettingsGridContainer>
            </div>

            <SharedStyled.SectionTitle>Piece Work(Full Piece Work Crew)</SharedStyled.SectionTitle>
            <div style={{ width: '100%' }}>
              {tearOffDetailsNone.length > 0 && <Styled.HeadingDiv marginTop="10px">Tear Off</Styled.HeadingDiv>}
              <Styled.SingleWorkSettingsGridContainer marginTop="15px">
                {tearOffDetailsNone?.map((detail: any, index1: number) => (
                  <Styled.SingleColumnDiv key={detail._id}>
                    <Styled.DescriptionDiv>{detail[0].description}</Styled.DescriptionDiv>
                    {detail
                      .sort((a: any, b: any) => a.order - b.order)
                      .map((det: any, index: number) => (
                        <Styled.NameValueUnitContainer key={index}>
                          <Styled.NameDiv>{det.name}:</Styled.NameDiv>
                          <Styled.ValueInput
                            type="number"
                            value={det.amount}
                            onChange={onChangeTearOffValue(index1, index)}
                          />
                          <Styled.UnitDiv>{det.unit}</Styled.UnitDiv>
                        </Styled.NameValueUnitContainer>
                      ))}
                  </Styled.SingleColumnDiv>
                ))}
              </Styled.SingleWorkSettingsGridContainer>
            </div>

            <div style={{ width: '100%' }}>
              {roofingDetailsNone.length > 0 && <Styled.HeadingDiv marginTop="10px">Roofing</Styled.HeadingDiv>}
              <Styled.SingleWorkSettingsGridContainer marginTop="15px">
                {roofingDetailsNone?.map((detail: any, index1: number) => (
                  <Styled.SingleColumnDiv key={detail._id}>
                    <Styled.DescriptionDiv>{detail[0].description}</Styled.DescriptionDiv>
                    {detail
                      .sort((a: any, b: any) => a.order - b.order)
                      .map((det: any, index: number) => (
                        <Styled.NameValueUnitContainer key={index}>
                          <Styled.NameDiv>{det.name}:</Styled.NameDiv>
                          <Styled.ValueInput
                            type="number"
                            value={det.amount}
                            onChange={onChangeRoofingValue(index1, index)}
                          />
                          <Styled.UnitDiv>{det.unit}</Styled.UnitDiv>
                        </Styled.NameValueUnitContainer>
                      ))}
                  </Styled.SingleColumnDiv>
                ))}
              </Styled.SingleWorkSettingsGridContainer>
            </div>
            <div style={{ width: '100%' }}>
              {/* {pitchDetails.length > 0 && (
                <>
                  <SharedStyled.SectionTitle>Pitch Modifier</SharedStyled.SectionTitle>
                </>
              )}
              <Styled.SingleWorkSettingsGridContainer marginTop="15px" className="pitch">
                {pitchDetails?.map((detail: any, index1: number) => (
                  <Styled.SingleColumnDiv key={detail._id}>
                    <Styled.DescriptionDiv>{detail[0].description}</Styled.DescriptionDiv>
                    {detail
                      .sort((a: any, b: any) => a.order - b.order)
                      .map((det: any, index: number) => (
                        <Styled.NameValueUnitContainer key={index}>
                          <Styled.NameDiv>{det.name}:</Styled.NameDiv>
                          <Styled.ValueInput
                            type="number"
                            value={det.amount}
                            onChange={onChangePitchModifierValue(index1, index)}
                          />
                          <Styled.UnitDiv>{det.unit}</Styled.UnitDiv>
                        </Styled.NameValueUnitContainer>
                      ))}
                  </Styled.SingleColumnDiv>
                ))}
              </Styled.SingleWorkSettingsGridContainer> */}

              {customDetails.length > 0 && (
                <>
                  <SharedStyled.ContentHeader margin="20px 0 0 0" textAlign="left">
                    Custom
                  </SharedStyled.ContentHeader>
                  <SharedStyled.HorizontalDivider />
                </>
              )}

              <Styled.SingleWorkSettingsGridContainer1 marginTop="15px">
                {customDetails?.map((detail: any, index1: number) => (
                  <Styled.SingleColumnDiv key={detail._id}>
                    <Styled.DescriptionDiv>{detail[0].description}</Styled.DescriptionDiv>
                    {detail.map((det: any, index: number) => (
                      <Styled.ContentAddButtonDiv key={index}>
                        <Styled.NameValueUnitContainer minWidth="fit-content">
                          <Styled.NameDiv>{det.name}:</Styled.NameDiv>
                          <Styled.ValueInput
                            type="number"
                            value={det.amount}
                            onChange={onChangeCustomValue(index1, index)}
                            width="108px"
                          />
                          <Styled.UnitDiv>{det.unit}</Styled.UnitDiv>
                        </Styled.NameValueUnitContainer>
                        {detail.length === index + 1 && (
                          <Styled.MiniAddButton
                            onClick={() => {
                              setShowAddCustomRowPopup(true)
                              setNewRowDetail({ order: det.order, subType: det.subType })
                            }}
                          >
                            <AddIconNoCircleIcon />
                          </Styled.MiniAddButton>
                        )}
                        <Styled.DeleteButton
                          onClick={() => {
                            setShowRemoveCustomRowPopup(true)
                            setToDeleteRow(det)
                          }}
                        >
                          <DeleteIcon />
                        </Styled.DeleteButton>
                      </Styled.ContentAddButtonDiv>
                    ))}
                  </Styled.SingleColumnDiv>
                ))}
              </Styled.SingleWorkSettingsGridContainer1>
              <SharedStyled.FlexBox width="100%" alignItems="center" gap="20px" wrap="wrap" margin="0 0 70px 0">
                <Button type="submit" isLoading={loading} maxWidth="200px" onClick={() => onSave()}>
                  Save
                </Button>
              </SharedStyled.FlexBox>
            </div>
          </SettingsCont>
          {/* </Styled.PieceWorkSettingsSalariedCrewContainer> */}
          <CustomModal show={showAddCustomSettingsPopup}>
            <AddCustomSettings
              setShowAddCustomSettingsPopup={setShowAddCustomSettingsPopup}
              setDetailsUpdate={setDetailsUpdate}
              newSubType={newSubType}
            />
          </CustomModal>

          <CustomModal show={showNewPieceWork}>
            <AddNewPieceWork
              isEdit={!!Object?.keys(selectedPiecework)?.length}
              onClose={() => {
                setSelectedPiecework({})
                setShowNewPieceWork(false)
              }}
              editValues={selectedPiecework}
            />
          </CustomModal>
          <CustomModal show={showAddCustomRowPopup}>
            <AddCustomRow
              setShowAddCustomRowPopup={setShowAddCustomRowPopup}
              setDetailsUpdate={setDetailsUpdate}
              newRowDetail={newRowDetail}
            />
          </CustomModal>
          <CustomModal show={showRemoveCustomRowPopup}>
            <RemoveCustomRow
              setShowRemoveCustomRowPopup={setShowRemoveCustomRowPopup}
              setDetailsUpdate={setDetailsUpdate}
              toDeleteRow={toDeleteRow}
            />
          </CustomModal>
        </>
      )}
    </>
  )
}

export default PieceWorkSettingsSalariedCrew
