import { Fragment, useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { useParams } from 'react-router-dom'

import { AddIconNoCircleIcon } from '../../assets/icons/AddIconNoCircleIcon'
import { DeleteIcon } from '../../assets/icons/DeleteIcon'
import EditSvg from '../../assets/newIcons/edit.svg'
import {
  getCompanyPayData,
  getNewPieceworkValues,
  getPieceWorkSetting,
  updateNewPieceworkValues,
  updatePieceWorkSettingAmounts,
  updatePwSequence,
  upsertPieceWorkSetting,
} from '../../logic/apis/pieceWork'
import { CustomModal } from '../../shared/customModal/CustomModal'
import { getDataFromLocalStorage, handleWheel, isSuccess, notify } from '../../shared/helpers/util'
import * as SharedStyled from '../../styles/styled'
import { AddCustomRow } from './components/AddCustomRow/AddCustomRow'
import { AddCustomSettings } from './components/AddCustomSettings/AddCustomSettings'
import { RemoveCustomRow } from './components/RemoveCustomRow/RemoveCustomRow'
import * as Styled from './style'
import Button from '../../shared/components/button/Button'
import AddNewPieceWork from './components/AddNewPieceWork/AddNewPieceWork'
import { getTasks } from '../../logic/apis/task'
import { mergeArrays } from '../clockInOut/components/pieceWorkModal/PieceWorkModal'
import { getUnitsApi } from '../../logic/apis/projects'
import Toggle from '../../shared/toggle/Toggle'
import { SLoader } from '../../shared/components/loader/Loader'
import { IButton } from '../newProject/style'
import DraggableDiv from '../../shared/draggableDiv/DraggableDiv'
import CustomSelect from '../../shared/customSelect/CustomSelect'
import Dropdown from '../../shared/components/dropdown/Dropdown'
import AddPieceworkVersion, { IVersion } from './components/AddPieceworkVersion/AddPieceworkVersion'
import AddPositionVersion from './components/AddPosition/AddPosition'
import { getPosition } from '../../logic/apis/position'
import TabBar from '../../shared/components/tabBar/TabBar'
import DeletedPiecework from './components/DeletedPiecework/DeletedPiecework'
import { StorageKey } from '../../shared/helpers/constants'

function filterObjectsByPitch(data: any[]) {
  const pitchArray: any[] = []
  const noPitchArray: any[] = []

  data?.forEach((item: any) => {
    if (item.usesPitch) {
      pitchArray.push(item)
    } else {
      noPitchArray.push(item)
    }
  })
  pitchArray.sort((a: any, b: any) => a.sequence - b.sequence)
  noPitchArray.sort((a: any, b: any) => a.sequence - b.sequence)

  return { pitchArray, noPitchArray }
}

export const labourDropdown = ['Fixed ($)', 'Percent (%)']
const replaceNoPitchArray = (render: any[], newVal: any[], index: number, isUsesPitch?: boolean) => {
  if (index < 0 || index >= render.length) {
    console.error('Index out of bounds')
  }
  if (!render[index].pieceWorkSettings) {
    console.error('No pieceWorkSettings found at the specified index')
  }

  // Replacing the noPitchArray with newVal
  if (isUsesPitch) {
    render[index].pieceWorkSettings.pitchArray = newVal
  } else {
    render[index].pieceWorkSettings.noPitchArray = newVal
  }

  return render
}

const NewPieceWorkSettingsSalariedCrew = () => {
  const [showAddCustomSettingsPopup, setShowAddCustomSettingsPopup] = useState(false)
  const [showAddCustomRowPopup, setShowAddCustomRowPopup] = useState(false)
  const [showRemoveCustomRowPopup, setShowRemoveCustomRowPopup] = useState(false)
  const [saveData, setSaveData] = useState<any>([])
  const [loading, setLoading] = useState<boolean>(false)
  const [detailsUpdate, setDetailsUpdate] = useState(false)
  const [tearOffDetails, setTearOffDetails] = useState<any>([])
  const [tearOffDetailsNone, setTearOffDetailsNone] = useState<any>([])
  const [newSubType, setNewSubType] = useState<number>(0)
  const [newRowDetail, setNewRowDetail] = useState<any>({})
  const [toDeleteRow, setToDeleteRow] = useState<any>({})
  const [roofingDetails, setRoofingDetails] = useState<any>([])
  const [roofingDetailsNone, setRoofingDetailsNone] = useState<any>([])
  const [customDetails, setCustomDetails] = useState<any>([])
  const [pitchDetails, setPitchDetails] = useState<any>([])
  const [shimmerLoading, setShimmerLoading] = useState<boolean>(true)
  const [showNewPieceWork, setShowNewPieceWork] = useState(false)
  const [newPieceworkValues, setNewPieceworkValues] = useState<any[]>([])
  const [units, setUnits] = useState([])
  const [unitsDrop, setUnitsDrop] = useState<any>({})
  const [chevronIndex, setChevronIndex] = useState<any[]>([])
  const [showAddPieceworkVersion, setShowAddPieceworkVersion] = useState(false)
  const [selectedPosition, setSelectedPosition] = useState<{ position?: string }>({})
  const [isFullEdit, setIsFullEdit] = useState(false)
  const [showall, setShowall] = useState(false)
  const [selectedVersion, setSelectedVersion] = useState<IVersion>()
  const [bool, setBool] = useState(false)
  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company

  const [versionLoading, setVersionLoading] = useState(true)
  const [currentValue, setCurrentValue] = useState('')
  const [pieceworkLoading, setPieceworkLoading] = useState(false)
  const [sequenceUpdate, setSequenceUpdate] = useState(false)
  const [inputLoading, setInputLoading] = useState(false)

  const [pwVersions, setPwVersions] = useState<IVersion[]>([])
  const [pwPositions, SetPwPositions] = useState([])

  const [layerValue, setLayerValue] = useState('')
  const pieceworkDropdown = newPieceworkValues?.map((itm) => ({ label: itm?.workTask?.name, value: itm?._id }))
  const [selectedPieceworkView, setSelectedPieceworkView] = useState(() => pieceworkDropdown[0])
  const [selectedLabor, setSelectedLabor] = useState('')

  useEffect(() => {
    if (pieceworkDropdown?.length) {
      setSelectedPieceworkView(pieceworkDropdown[0])
    }
  }, [pieceworkDropdown?.length])
  useEffect(() => {
    if (units && units.length) {
      let localUnits: any = {}
      units?.forEach((unit: any) => {
        localUnits[`${unit.symbol} (${unit.name})`] = unit
      })
      if (Object.keys(localUnits)) setUnitsDrop(localUnits)
    }
  }, [units])

  const [selectedPiecework, setSelectedPiecework] = useState({})
  const [isEdit, setIsEdit] = useState(false)
  const [selectedVersionId, setSelectedVersionId] = useState('')
  const [showPositionModal, setShowPositionModal] = useState(false)
  const [allPositions, setAllPositions] = useState([])

  const getPieceWorkSettingsDetail = async () => {
    try {
      if (Object.keys(currentCompany).length > 0) {
        let response = await getPieceWorkSetting({ deleted: false })

        if (isSuccess(response)) {
          let resData = response?.data?.data?.pieceWorkSetting
          let dataObj: any = {}
          let tear: any = []
          let tearNone: any = []
          let roof: any = []
          let roofNone: any = []
          let cust: any = []
          let pitch: any = []
          let subTypeValue = 0
          resData.forEach((data: any) => {
            let name = data?._id?.type?.replace('_', ' ')
            let workerType = data?._id?.workerType
            let subType = data?._id?.subType
            let displayData = [...data.data]
            data?.data?.forEach((dat: any, index: number) => {
              if (dat?.unit === '%') {
                let obj = { ...dat, amount: dat?.amount * 100 }
                displayData?.splice(index, 1, obj)
              }
            })
            if (name === 'Tear Off' && workerType === 'salaried') {
              const modifiedData = displayData.map((item) => ({
                ...item,
                workerType: 'salaried',
              }))
              tear.push(modifiedData)
            } else if (name === 'Tear Off' && workerType === 'default') {
              const modifiedData = displayData.map((item) => ({
                ...item,
                workerType: 'default',
              }))
              tearNone.push(modifiedData)
            } else if (name === 'Roofing' && workerType === 'salaried') {
              const modifiedData = displayData.map((item) => ({
                ...item,
                workerType: 'salaried',
              }))
              roof.push(modifiedData)
            } else if (name === 'Roofing' && workerType === 'default') {
              const modifiedData = displayData.map((item) => ({
                ...item,
                workerType: 'default',
              }))
              roofNone.push(modifiedData)
            } else if (name === 'Pitch Modifier') {
              pitch.push(displayData)
            } else {
              cust.push(displayData)
            }
            if (subType > subTypeValue) {
              subTypeValue = subType
            }
          })
          setNewSubType(subTypeValue + 1)
          setTearOffDetails(tear)
          setTearOffDetailsNone(tearNone)
          setRoofingDetails(roof)
          setRoofingDetailsNone(roofNone)
          setPitchDetails(pitch)
          setCustomDetails(cust)
          setShimmerLoading(false)
        } else {
          notify(response?.data?.message, 'error')
          setShimmerLoading(false)
        }
      }
    } catch (error) {
      console.error('getPieceWorkSettingsDetail error', error)
      setShimmerLoading(false)
    }
  }

  const getNewPiecework = async ({ version }: { version: string }) => {
    try {
      setVersionLoading(true)
      const res = await getNewPieceworkValues({
        version,
      })

      const responseData = res?.data?.data?.pieceWorkSettings

      const taskRes = await getTasks({ limit: '100' }, false)
      const taskResponse = taskRes?.data?.data?.workTask

      const customResponse = responseData?.map((item: any) => {
        const pitchObject = filterObjectsByPitch(item?.pieceWorkSettings)
        return {
          ...item,
          pieceWorkSettings: pitchObject,
        }
      })

      const customTaskRes = taskResponse
        ?.filter((a: any) => a.pieceWork)
        ?.map((t: any) => ({
          _id: t._id,
          workTask: t,
        }))

      const merged = mergeArrays(customResponse, customTaskRes, '_id')

      setNewPieceworkValues(merged)
    } catch (error) {
      console.error('API Error=====>', error)
    } finally {
      setVersionLoading(false)
    }
  }

  // const onSave = async () => {
  //   try {
  //     if (Object.keys(currentCompany).length > 0) {
  //       if (saveData.length > 0) {
  //         setLoading(true)
  //         let dummySaveData = [...saveData]
  //         saveData.forEach((dat: any, index: number) => {
  //           if (dat?.unit === '%') {
  //             let obj = { ...dat, amount: Number(dat?.amount) / 100 }
  //             dummySaveData.splice(index, 1, obj)
  //           }
  //         })
  //         let response = await upsertPieceWorkSetting(dummySaveData, id)
  //         if (isSuccess(response)) {
  //           notify('Saved Data Successfully', 'success')
  //           getPieceWorkSettingsDetail()
  //           setSaveData([])
  //           setLoading(false)
  //         } else {
  //           notify(response?.data?.message, 'error')
  //           setLoading(false)
  //         }
  //       } else {
  //         notify('No Fields Edited Yet to Save', 'info')
  //       }
  //     }
  //   } catch (error) {
  //     console.error('onSave error', error)
  //     setLoading(false)
  //   }
  // }

  // const onChangeTearOffValue = (indexOuter: number, indexInner: number) => (e: any) => {
  //   try {
  //     if (Object.keys(currentCompany).length > 0) {
  //       let dataArray = [...tearOffDetails]
  //       let dataObj = { ...dataArray[indexOuter][indexInner], amount: e.target.value }
  //       setSaveData([...saveData, { ...dataObj, companyId: currentCompany._id, createdBy: currentMember._id }])
  //       dataArray[indexOuter][indexInner] = { ...dataObj }
  //       setTearOffDetails(dataArray)
  //     }
  //   } catch (error) {
  //     console.error('onChangeTearOffValue error', error)
  //   }
  // }

  // const onChangeRoofingValue = (indexOuter: number, indexInner: number) => (e: any) => {
  //   try {
  //     if (Object.keys(currentCompany).length > 0) {
  //       let dataArray = [...roofingDetails]
  //       let dataObj = { ...dataArray[indexOuter][indexInner], amount: e.target.value }
  //       setSaveData([...saveData, { ...dataObj, companyId: currentCompany._id, createdBy: currentMember._id }])
  //       dataArray[indexOuter][indexInner] = { ...dataObj }
  //       setRoofingDetails(dataArray)
  //     }
  //   } catch (error) {
  //     console.error('onChangeRoofingValue error', error)
  //   }
  // }

  // const onChangeCustomValue = (indexOuter: number, indexInner: number) => (e: any) => {
  //   try {
  //     if (Object.keys(currentCompany).length > 0) {
  //       let dataArray = [...customDetails]
  //       let dataObj = { ...dataArray[indexOuter][indexInner], amount: e.target.value }
  //       setSaveData([...saveData, { ...dataObj, companyId: currentCompany._id, createdBy: currentMember._id }])
  //       dataArray[indexOuter][indexInner] = { ...dataObj }
  //       setCustomDetails(dataArray)
  //     }
  //   } catch (error) {
  //     console.error('onChangeCustomValue error', error)
  //   }
  // }

  // const onChangePitchModifierValue = (indexOuter: number, indexInner: number) => (e: any) => {
  //   try {
  //     if (Object.keys(currentCompany).length > 0) {
  //       let dataArray = [...pitchDetails]
  //       let dataObj = { ...dataArray[indexOuter][indexInner], amount: e.target.value }
  //       setSaveData([...saveData, { ...dataObj, companyId: currentCompany._id, createdBy: currentMember._id }])
  //       dataArray[indexOuter][indexInner] = { ...dataObj }
  //       setPitchDetails(dataArray)
  //     }
  //   } catch (error) {
  //     console.error('onChangePitchModifierValue error', error)
  //   }
  // }

  const fetchUnitsData = async () => {
    try {
      const res = await getUnitsApi({ deleted: false })
      if (isSuccess(res)) {
        const { unit } = res.data.data
        setUnits(unit)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('Failed init fetch', err)
    }
  }

  const fetchCompanyPayData = async () => {
    try {
      setLoading(true)
      const res = await getCompanyPayData()

      if (isSuccess(res)) {
        const { versions, pieceWorkPositions } = res.data.data

        setPwVersions(versions?.sort((a: { name: string }, b: { name: string }) => a?.name?.localeCompare(b?.name)))
        SetPwPositions(pieceWorkPositions)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('Failed init fetch', err)
    } finally {
      setLoading(false)
    }
  }
  const fetchPositions = async () => {
    try {
      const res = await getPosition({ deleted: false }, false)

      if (isSuccess(res)) {
        const { position } = res.data.data
        setAllPositions(position)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('Failed init fetch', err)
    }
  }

  useEffect(() => {
    getPieceWorkSettingsDetail()

    fetchUnitsData()
    fetchPositions()
  }, [detailsUpdate, selectedVersion?._id])

  useEffect(() => {
    selectedVersion?._id! && getNewPiecework({ version: selectedVersion?._id! })
    fetchCompanyPayData()
  }, [detailsUpdate, selectedVersion?._id, sequenceUpdate])

  // ========================================= Drag and drop =========================================

  function updateSequence<T extends { sequence: number }>(
    states: T[],
    setState: any,
    api: any,
    selectedIndex: number,
    isUsesPitch?: boolean
  ) {
    let localState: T[] = []

    return (cb: any) => {
      // get new keys sequence
      const newStateKeys: Array<keyof T> = cb(states)
      const sortedValues = replaceNoPitchArray(newPieceworkValues, newStateKeys, selectedIndex, isUsesPitch)

      setNewPieceworkValues(sortedValues)
      // update sequence in keys arrangement
      newStateKeys.forEach((key: { name: string }, newSequence) => {
        let [item] = states.filter((state: any) => state['name'] === key['name'])
        item.sequence = newSequence + 1
        localState.push(item)
      })

      // update original state in same arrangement
      setState((prev) => !prev)

      // update in db
      api(localState)
    }
  }

  const updateStepsSequence = async (steps: any[], onlyChild?: boolean) => {
    try {
      const data = steps.map((step) => {
        return { _id: step?._id, sequence: step.sequence }
      })

      const response = await updatePwSequence({ data })

      // CALL API
      if (isSuccess(response)) {
        notify(response?.data?.data?.message, 'success')
        setSequenceUpdate((p) => !p)
      } else throw new Error(response?.data?.message ?? 'Something went wrong!')
    } catch (err) {
      notify('Unable to update sequence', 'error')
      console.log(err, 'sequence updation')
    }
  }

  const handleSequenceUpdate = async (newSequence: any[]) => {
    // const allStepsLocal = allSteps
    // allStepsLocal.forEach((step, id) => {
    //   newSequence.forEach((newStep, newId) => {
    //     if (newStep?.name === step?.name) {
    //       allStepsLocal[id].sequence = newId
    //     }
    //   })
    // })
    updateStepsSequence(newSequence)
  }

  // ========================================= Drag and drop =========================================
  const handleBlur = async () => {
    try {
      // setPieceworkLoading(true)
      setInputLoading(true)
      const pitchIndex = selectedPiecework?.pitch?.findIndex(
        (p) => p?.pitchOrder === selectedPiecework?.selectedPitchOrder
      ) as number

      const prevValue = selectedPiecework?.usesPitch
        ? selectedPiecework?.pitch[pitchIndex]?.amount
        : selectedPiecework?.amount

      if (currentValue && prevValue !== Number(currentValue)) {
        selectedPiecework?.usesPitch &&
          !isFullEdit &&
          selectedPiecework?.pitch?.splice(pitchIndex, 1, {
            pitchOrder: selectedPiecework.selectedPitchOrder!,
            amount: Number(currentValue),
          })

        const dataObj = {
          ...selectedPiecework,
        }
        const editObj = { ...dataObj, amount: selectedPiecework?.usesPitch ? undefined : Number(currentValue) }

        const res = await updateNewPieceworkValues(editObj)
        notify(res?.data?.data?.message, 'success')
        // setDetailsUpdate((p) => !p)
        setIsEdit(false)
        setSelectedPiecework({})
        setCurrentValue('')
      }
    } catch (error) {
      console.error('handleBlur error=====>', error)
    } finally {
      setInputLoading(false)
    }
  }

  const newHandleBlur = async (data: {
    id: string
    amount: number
    prevAmount: number
    isPitch: boolean
    pitchOrder?: number
    pitch?: any[]
  }) => {
    try {
      if (data?.isPitch) {
        const pitchIndex = data?.pitch?.findIndex((p) => p?.pitchOrder === data?.pitchOrder) as number

        if (data?.amount && data?.prevAmount !== Number(data?.amount)) {
          data?.pitch?.splice(pitchIndex, 1, {
            pitchOrder: data.pitchOrder!,
            amount: Number(data?.amount),
          })

          const res = await updatePieceWorkSettingAmounts({
            updateInputAmounts: [
              {
                id: data.id,
                pitch: data?.pitch,
                amount: data?.prevAmount,
              },
            ],
          })
          notify(res?.data?.data?.message, 'success')
        }
      } else {
        if (data?.amount && data?.prevAmount !== Number(data?.amount)) {
          const res = await updatePieceWorkSettingAmounts({
            updateInputAmounts: [
              {
                id: data.id,
                amount: data?.amount,
              },
            ],
          })
          notify(res?.data?.data?.message, 'success')
        }
      }
    } catch (error) {
      console.error('newHandleBlur error=====>', error)
    }
  }

  useEffect(() => {
    if (selectedLabor === labourDropdown[0]) {
      setSelectedPiecework({
        ...selectedPiecework,
        layer: {
          ...selectedPiecework?.layer,
          fixed: { isActive: true, value: Number(layerValue) },
          percent: { isActive: false },
        },
      })
      // setFieldValue('layer.fixed.isActive', true)
      // setFieldValue('layer.percent.isActive', false)
      // !isEdit && setFieldValue('layer.percent.value', '')
    } else {
      setSelectedPiecework({
        ...selectedPiecework,
        layer: {
          ...selectedPiecework?.layer,
          fixed: { isActive: false },
          percent: { isActive: true, value: Number(layerValue) },
        },
      })

      // setFieldValue('layer.percent.isActive', true)
      // setFieldValue('layer.fixed.isActive', false)
      // !isEdit && setFieldValue('layer.fixed.value', '')
    }
  }, [selectedLabor, layerValue])

  useEffect(() => {
    if (pwVersions?.length && !selectedVersion?._id) {
      setSelectedVersion(pwVersions[0])
    }
  }, [pwVersions])

  const currentPositionList = allPositions?.filter((pos: { _id: string }) => pwPositions?.includes(pos?._id))
  const positionsAvailableList = allPositions?.filter((pos: { _id: string }) => !pwPositions?.includes(pos?._id))

  return (
    <>
      <SharedStyled.SectionTitle>Piece Work Settings</SharedStyled.SectionTitle>
      <SharedStyled.HorizontalDivider height="0.5px" margin="10px 0" />
      <SharedStyled.FlexRow alignItems="flex-start">
        <SharedStyled.FlexCol gap="24px">
          <TabBar
            setDetailsUpdate={setDetailsUpdate}
            tabs={[
              {
                title: 'Active',
                render: () => (
                  <>
                    <Styled.NewPieceworkCont gap="24px">
                      <SharedStyled.FlexCol style={{ width: '100%' }}>
                        <SharedStyled.FlexCol gap="6px">
                          {versionLoading ? (
                            <SLoader width={200} height={20} />
                          ) : (
                            <SharedStyled.FlexRow>
                              <b>
                                <p>Position Paid By Piece Work:</p>
                              </b>
                              <Button
                                width="max-content"
                                onClick={() => {
                                  setShowPositionModal(true)
                                }}
                                maxWidth="30px"
                                padding="2px 8px"
                                style={{ borderRadius: '0px' }}
                              >
                                +
                              </Button>
                            </SharedStyled.FlexRow>
                          )}
                          {versionLoading ? (
                            <SLoader width={200} height={20} />
                          ) : (
                            <SharedStyled.FlexRow gap="0px">
                              {currentPositionList?.map((pos: { position: string }, idx) => (
                                <p
                                  key={idx}
                                  style={{ cursor: 'pointer' }}
                                  onClick={() => {
                                    setSelectedPosition(pos)
                                    setShowPositionModal(true)
                                  }}
                                >
                                  {pos?.position} {idx === currentPositionList?.length - 1 ? null : <>, &nbsp;</>}
                                </p>
                              ))}
                            </SharedStyled.FlexRow>
                          )}
                        </SharedStyled.FlexCol>

                        <SharedStyled.FlexRow gap="0" margin="20px 0">
                          {versionLoading ? (
                            <SLoader width={200} height={40} />
                          ) : (
                            <>
                              {pwVersions?.map((vers, idx) => (
                                <Styled.TabButton
                                  $isActive={!!(selectedVersion?.name === vers?.name)}
                                  onClick={() => {
                                    setSelectedVersion(vers)
                                  }}
                                  key={idx}
                                >
                                  <img
                                    src={EditSvg}
                                    onClick={(e) => {
                                      e.stopPropagation()
                                      setSelectedVersionId(vers?._id)
                                      setShowAddPieceworkVersion(true)
                                    }}
                                    alt="edit icon"
                                    className="icon"
                                  />
                                  <span>{vers?.name}</span>
                                </Styled.TabButton>
                              ))}

                              <Styled.TabButton
                                onClick={() => {
                                  setShowAddPieceworkVersion(true)
                                }}
                              >
                                +
                              </Styled.TabButton>
                            </>
                          )}
                        </SharedStyled.FlexRow>

                        {versionLoading || pieceworkLoading ? (
                          <SharedStyled.FlexCol gap="20px">
                            <SLoader height={100} />
                            <SLoader height={100} />
                            <SLoader height={100} />
                            <SLoader height={100} />
                            <SLoader height={100} />
                          </SharedStyled.FlexCol>
                        ) : (
                          <>
                            {newPieceworkValues?.map((item: any, idxRoot: number) => (
                              <Fragment key={`${item?.id}-${idxRoot}`}>
                                <SharedStyled.FlexRow>
                                  {selectedPieceworkView?.value === item?._id && (
                                    <SharedStyled.FlexRow margin="10px 0" gap="40px">
                                      {/* {tearOffDetails.length && selectedPieceworkView?.value === item?._id && (
                                      <Styled.HeadingDiv className="title">{item?.workTask?.name}</Styled.HeadingDiv>
                                    )}
          
                                    <Styled.Chevron
                                      onClick={() => {
                                        setSelectedPieceworkView(selectedPieceworkView === idxRoot ? null : idxRoot)
                                      }}
                                    >
                                      ▼
                                    </Styled.Chevron> */}

                                      <>
                                        {/* <CustomSelect
                                        dropDownData={pieceworkDropdown}
                                        value={selectedPieceworkView?.label}
                                        setValue={setSelectedPieceworkView}
                                        stateName=""
                                        className="dropdown pieceworkDropdown"
                                        maxWidth="300px"
                                      /> */}

                                        <Dropdown
                                          onSelectValue={(val: string) => setSelectedPieceworkView(val)}
                                          selectedValue={{ label: selectedPieceworkView?.label }}
                                          options={pieceworkDropdown}
                                          className="pieceworkDropdown"
                                          isChevron
                                        >
                                          <p>{selectedPieceworkView?.label}</p>
                                        </Dropdown>

                                        <Button
                                          width="max-content"
                                          padding="8px 14px"
                                          onClick={() => {
                                            setSelectedPiecework({ workTask: item?._id })
                                            setIsEdit(false)
                                            setIsFullEdit(true)
                                            setShowall(true)
                                            setShowNewPieceWork(true)
                                          }}
                                        >
                                          Add new
                                        </Button>
                                      </>
                                    </SharedStyled.FlexRow>
                                  )}
                                </SharedStyled.FlexRow>

                                {selectedPieceworkView?.value === item?._id ? (
                                  <>
                                    {item?.pieceWorkSettings?.pitchArray?.length ? (
                                      <SharedStyled.FlexRow margin="10px 0">
                                        <Styled.HeadingDiv>Pitch settings</Styled.HeadingDiv>
                                      </SharedStyled.FlexRow>
                                    ) : null}

                                    {/* ================== Pitches ================== */}

                                    <Styled.PieceworkWrap>
                                      <div className="border"></div>

                                      <DraggableDiv
                                        items={item?.pieceWorkSettings?.pitchArray}
                                        isObject
                                        horizontal
                                        setItems={updateSequence(
                                          item?.pieceWorkSettings?.pitchArray,
                                          setBool,
                                          handleSequenceUpdate,
                                          idxRoot,
                                          true
                                        )}
                                        renderChild={(detail: any, index1: number) => (
                                          <Styled.SingleColumnDiv
                                            key={detail?._id}
                                            className={index1 === 0 ? 'first-drag new' : 'new'}
                                          >
                                            <SharedStyled.FlexRow>
                                              <span>
                                                <svg
                                                  width="24"
                                                  height="24"
                                                  viewBox="0 0 24 24"
                                                  style={{ rotate: '90deg', marginTop: '5px' }}
                                                >
                                                  <path d="M7 10h2v2H7v-2zm0 4h2v2H7v-2zm4-4h2v2h-2v-2zm0 4h2v2h-2v-2zm4-4h2v2h-2v-2zm0 4h2v2h-2v-2z" />
                                                </svg>{' '}
                                              </span>
                                              <Styled.PieceName
                                                className={index1 === 0 ? 'first' : ''}
                                                onClick={() => {
                                                  setIsEdit(true)
                                                  setSelectedPiecework({ ...detail })
                                                  setShowNewPieceWork(true)
                                                  setShowall(false)
                                                  setIsFullEdit(true)
                                                }}
                                              >
                                                {detail?.name}
                                              </Styled.PieceName>
                                              <SharedStyled.TooltipContainer
                                                positionLeft="0px"
                                                positionBottom="0px"
                                                positionLeftDecs="0px"
                                                positionBottomDecs="0px"
                                                className="piece small"
                                              >
                                                <span className="tooltip-content">{detail?.description}</span>
                                                <IButton>i</IButton>
                                              </SharedStyled.TooltipContainer>
                                            </SharedStyled.FlexRow>
                                            {detail?.pitch?.map((p: any, idx: number) => (
                                              <SharedStyled.FlexRow alignItems="flex-start" key={idx}>
                                                {index1 === 0 && idx === 0 ? (
                                                  <Styled.Chevron
                                                    onClick={() => {
                                                      setChevronIndex(() => {
                                                        const arr: any[] = [...chevronIndex]
                                                        arr[idxRoot] = !arr[idxRoot]
                                                        setChevronIndex(arr)
                                                      })
                                                    }}
                                                  >
                                                    {chevronIndex?.[idxRoot] ? '▼' : '▶'}
                                                  </Styled.Chevron>
                                                ) : (
                                                  index1 === 0 &&
                                                  chevronIndex?.[idxRoot] && (
                                                    <Styled.Chevron className="hidden">▼</Styled.Chevron>
                                                  )
                                                )}

                                                <Styled.NameValueUnitContainer
                                                  onClick={() => {
                                                    setIsEdit(true)
                                                    setSelectedPiecework({
                                                      ...detail,
                                                      selectedPitchOrder: p?.pitchOrder,
                                                    })
                                                    // setShowNewPieceWork(true)
                                                    setIsFullEdit(false)
                                                  }}
                                                  className="new width"
                                                  key={idx}
                                                  style={{
                                                    display:
                                                      !chevronIndex?.[idxRoot] && idx > 0 ? 'none' : 'inline-block',
                                                  }}
                                                >
                                                  <SharedStyled.FlexRow
                                                    gap={index1 === 0 ? '24px' : '20px'}
                                                    className="chevron-container"
                                                  >
                                                    {index1 === 0 && (
                                                      <Styled.PitchTitle>{`${p?.pitchOrder}/12`}</Styled.PitchTitle>
                                                    )}
                                                    <SharedStyled.FlexRow
                                                      gap="0"
                                                      width="max-content"
                                                      style={{ height: '32px' }}
                                                    >
                                                      <Styled.ValueInput
                                                        type="number"
                                                        // value={p.amount}
                                                        onWheel={handleWheel}
                                                        className="new"
                                                        // onClick={(e) => e.stopPropagation()}
                                                        onChange={(e) => {
                                                          setCurrentValue(e.target.value)
                                                        }}
                                                        // disabled={inputLoading}
                                                        defaultValue={p?.amount}
                                                        onBlur={(e: any) => {
                                                          newHandleBlur({
                                                            amount: Number(e.target.value),
                                                            id: detail?._id,
                                                            prevAmount: p?.amount,
                                                            pitchOrder: p?.pitchOrder,
                                                            isPitch: true,
                                                            pitch: detail?.pitch,
                                                          })
                                                        }}
                                                      />
                                                      <Styled.UnitDiv>
                                                        {detail.unit?.split(' ')[0]?.trim()}
                                                      </Styled.UnitDiv>
                                                    </SharedStyled.FlexRow>
                                                  </SharedStyled.FlexRow>
                                                </Styled.NameValueUnitContainer>
                                              </SharedStyled.FlexRow>
                                            ))}

                                            {detail?.hasLayer ? (
                                              <SharedStyled.FlexCol
                                                margin="40px 0 0 0"
                                                onClick={() => {
                                                  setIsEdit(true)
                                                  setSelectedPiecework({ ...detail })
                                                  setShowNewPieceWork(true)
                                                  setIsFullEdit(true)
                                                }}
                                                gap="10px"
                                                className={
                                                  index1 === 0 ? 'first-layer layer-container' : 'layer-container'
                                                }
                                              >
                                                <Styled.NoPitchTitle>Additional Layers:</Styled.NoPitchTitle>
                                                <SharedStyled.FlexRow width="max-content" gap="8px">
                                                  {/* <Toggle
                                          title=""
                                          isToggled={item?.workTask?.isLayer}
                                          onToggle={() => {
                                            // setIsUsesPitch((prev) => !prev)
                                          }}
                                          customStyles={{
                                            width: 'max-content',
                                            flexDirection: 'row-reverse',
                                            transform: 'rotate(-90deg)',
                                          }}
                                        /> */}

                                                  <CustomSelect
                                                    dropDownData={labourDropdown}
                                                    value={
                                                      detail?.layer?.fixed?.isActive
                                                        ? labourDropdown[0]
                                                        : labourDropdown[1]
                                                    }
                                                    setValue={() => {}}
                                                    stateName=""
                                                    className="dropdown"
                                                  />

                                                  <SharedStyled.FlexCol gap="10px" width="max-content">
                                                    {!!detail?.layer?.fixed?.isActive && (
                                                      <Styled.LayerCont
                                                        className="input"
                                                        isActive={detail?.layer?.fixed?.isActive}
                                                      >
                                                        {/* <Styled.NoPitchTitle>Fixed ($)</Styled.NoPitchTitle> */}
                                                        <Styled.ValueInput
                                                          type="number"
                                                          onWheel={handleWheel}
                                                          value={detail?.layer?.fixed?.value}
                                                          className="new"
                                                          // onClick={(e) => e.stopPropagation()}
                                                          // onChange={onChangeTearOffValue(index1, index)}
                                                          // onChange={(e) => {
                                                          //   setLayerValue(e.target.value)
                                                          //   setCurrentValue(e.target.value)
                                                          // }}
                                                          // defaultValue={detail?.layer?.fixed?.value}
                                                          // onBlur={handleBlur}
                                                        />
                                                      </Styled.LayerCont>
                                                    )}
                                                    {!!detail?.layer?.percent?.isActive && (
                                                      <Styled.LayerCont
                                                        className="input"
                                                        isActive={detail?.layer?.percent?.isActive}
                                                      >
                                                        {/* <Styled.NoPitchTitle>Percent (%)</Styled.NoPitchTitle> */}
                                                        <Styled.ValueInput
                                                          type="number"
                                                          onWheel={handleWheel}
                                                          value={detail?.layer?.percent?.value}
                                                          className="new"
                                                          // onClick={(e) => e.stopPropagation()}
                                                          // onChange={onChangeTearOffValue(index1, index)}
                                                          // onChange={(e) => {
                                                          //   setLayerValue(e.target.value)
                                                          //   setCurrentValue(e.target.value)
                                                          // }}
                                                          // defaultValue={detail?.layer?.percent?.value}
                                                          // onBlur={handleBlur}
                                                        />
                                                      </Styled.LayerCont>
                                                    )}
                                                  </SharedStyled.FlexCol>
                                                </SharedStyled.FlexRow>
                                              </SharedStyled.FlexCol>
                                            ) : (
                                              <div
                                                className={
                                                  item?.pieceWorkSettings?.pitchArray?.length > 1 ? 'hideLayer' : ''
                                                }
                                              ></div>
                                            )}
                                          </Styled.SingleColumnDiv>
                                        )}
                                      />

                                      {/*   {item?.pieceWorkSettings?.pitchArray?.map((detail: any, index1: number) => (
                                      <Styled.SingleColumnDiv key={detail?._id} className="new">
                                        <SharedStyled.FlexRow>
                                          <Styled.PieceName
                                            className={index1 === 0 ? 'first' : ''}
                                            onClick={() => {
                                              setIsEdit(true)
                                              setSelectedPiecework({ ...detail })
                                              setShowNewPieceWork(true)
                                              setShowall(false)
                                              setIsFullEdit(true)
                                            }}
                                          >
                                            {detail?.name}
                                          </Styled.PieceName>
                                          <SharedStyled.TooltipContainer
                                            positionLeft="0px"
                                            positionBottom="0px"
                                            positionLeftDecs="0px"
                                            positionBottomDecs="0px"
                                            className="piece"
                                          >
                                            <span className="tooltip-content">{detail?.description}</span>
                                            <IButton>i</IButton>
                                          </SharedStyled.TooltipContainer>
                                        </SharedStyled.FlexRow>
                                        {detail?.pitch?.map((p: any, idx: number) => (
                                          <SharedStyled.FlexRow alignItems="flex-start" key={idx}>
                                            {index1 === 0 && idx === 0 ? (
                                              <Styled.Chevron
                                                onClick={() => {
                                                  setChevronIndex(() => {
                                                    const arr: any[] = [...chevronIndex]
                                                    arr[idxRoot] = !arr[idxRoot]
                                                    setChevronIndex(arr)
                                                  })
                                                }}
                                              >
                                                {chevronIndex?.[idxRoot] ? '▼' : '▶'}
                                              </Styled.Chevron>
                                            ) : (
                                              index1 === 0 &&
                                              chevronIndex?.[idxRoot] && <Styled.Chevron className="hidden">▼</Styled.Chevron>
                                            )}
          
                                            <Styled.NameValueUnitContainer
                                              onClick={() => {
                                                setIsEdit(true)
                                                setSelectedPiecework({ ...detail, selectedPitchOrder: p?.pitchOrder })
                                                // setShowNewPieceWork(true)
                                                setIsFullEdit(false)
                                              }}
                                              className="new width"
                                              key={idx}
                                              style={{
                                                display: !chevronIndex?.[idxRoot] && idx > 0 ? 'none' : 'inline-block',
                                              }}
                                            >
                                              <SharedStyled.FlexRow gap="20px" className="chevron-container">
                                                {index1 === 0 && <Styled.PitchTitle>{`${p?.pitchOrder}/12`}</Styled.PitchTitle>}
                                                <SharedStyled.FlexRow gap="0" width="max-content" style={{ height: '32px' }}>
                                                  <Styled.ValueInput
                                                    type="number"
                                                    // value={p.amount}
                                                    className="new"
                                                    // onClick={(e) => e.stopPropagation()}
                                                    onChange={(e) => {
                                                      setCurrentValue(e.target.value)
                                                    }}
                                                    defaultValue={p?.amount}
                                                    onBlur={handleBlur}
                                                  />
                                                  <Styled.UnitDiv>{detail.unit?.split(' ')[0]?.trim()}</Styled.UnitDiv>
                                                </SharedStyled.FlexRow>
                                              </SharedStyled.FlexRow>
                                            </Styled.NameValueUnitContainer>
                                          </SharedStyled.FlexRow>
                                        ))}
          
                                        {detail?.hasLayer ? (
                                          <SharedStyled.FlexCol
                                            margin="40px 0 0 0"
                                            onClick={() => {
                                              setIsEdit(true)
                                              setSelectedPiecework({ ...detail })
                                              setShowNewPieceWork(true)
                                              setIsFullEdit(true)
                                            }}
                                            gap="10px"
                                            className={index1 === 0 ? 'first-layer layer-container' : 'layer-container'}
                                          >
                                            <Styled.NoPitchTitle>Additional Layers:</Styled.NoPitchTitle>
                                            <SharedStyled.FlexRow width="max-content" gap="8px">
                                              /~ <Toggle
                                            title=""
                                            isToggled={item?.workTask?.isLayer}
                                            onToggle={() => {
                                              // setIsUsesPitch((prev) => !prev)
                                            }}
                                            customStyles={{
                                              width: 'max-content',
                                              flexDirection: 'row-reverse',
                                              transform: 'rotate(-90deg)',
                                            }}
                                          /> ~/
          
                                              <CustomSelect
                                                dropDownData={labourDropdown}
                                                value={detail?.layer?.fixed?.isActive ? labourDropdown[0] : labourDropdown[1]}
                                                setValue={() => {}}
                                                stateName=""
                                                className="dropdown"
                                              />
          
                                              <SharedStyled.FlexCol gap="10px" width="max-content">
                                                {!!detail?.layer?.fixed?.isActive && (
                                                  <Styled.LayerCont className="input" isActive={detail?.layer?.fixed?.isActive}>
                                                    /~ <Styled.NoPitchTitle>Fixed ($)</Styled.NoPitchTitle> ~/
                                                    <Styled.ValueInput
                                                      type="number"
                                                      value={detail?.layer?.fixed?.value}
                                                      className="new"
                                                      // onClick={(e) => e.stopPropagation()}
                                                      // onChange={onChangeTearOffValue(index1, index)}
                                                      // onChange={(e) => {
                                                      //   setLayerValue(e.target.value)
                                                      //   setCurrentValue(e.target.value)
                                                      // }}
                                                      // defaultValue={detail?.layer?.fixed?.value}
                                                      // onBlur={handleBlur}
                                                    />
                                                  </Styled.LayerCont>
                                                )}
                                                {!!detail?.layer?.percent?.isActive && (
                                                  <Styled.LayerCont className="input" isActive={detail?.layer?.percent?.isActive}>
                                                    /~ <Styled.NoPitchTitle>Percent (%)</Styled.NoPitchTitle> ~/
                                                    <Styled.ValueInput
                                                      type="number"
                                                      value={detail?.layer?.percent?.value}
                                                      className="new"
                                                      // onClick={(e) => e.stopPropagation()}
                                                      // onChange={onChangeTearOffValue(index1, index)}
                                                      // onChange={(e) => {
                                                      //   setLayerValue(e.target.value)
                                                      //   setCurrentValue(e.target.value)
                                                      // }}
                                                      // defaultValue={detail?.layer?.percent?.value}
                                                      // onBlur={handleBlur}
                                                    />
                                                  </Styled.LayerCont>
                                                )}
                                              </SharedStyled.FlexCol>
                                            </SharedStyled.FlexRow>
                                          </SharedStyled.FlexCol>
                                        ) : (
                                          <div
                                            className={item?.pieceWorkSettings?.pitchArray?.length > 1 ? 'hideLayer' : ''}
                                          ></div>
                                        )}
                                      </Styled.SingleColumnDiv>
                                    ))}*/}
                                    </Styled.PieceworkWrap>

                                    {/* ================== no Pitches ================== */}

                                    <SharedStyled.FlexCol gap="20px" margin="40px 0 0 0">
                                      <>
                                        {/* {item?.pieceWorkSettings?.noPitchArray?.map((det: any, index1: number) => (
                                    <SharedStyled.FlexRow
                                      gap="20px"
                                      key={index1}
                                      onClick={() => {
                                        setIsEdit(true)
                                        setSelectedPiecework({ ...det, selectedPitchOrder: det?.pitchOrder })
                                        setShowNewPieceWork(true)
                                        setIsFullEdit(true)
                                      }}
                                    >
                                      <SharedStyled.FlexRow gap="0" width="max-content">
                                        <Styled.NoPitchTitle>{det?.name}</Styled.NoPitchTitle>
                                        <SharedStyled.TooltipContainer
                                          positionLeft="0px"
                                          positionBottom="0px"
                                          positionLeftDecs="0px"
                                          positionBottomDecs="0px"
                                        >
                                          <span className="tooltip-content">{det?.description}</span>
                                          <IButton>i</IButton>
                                        </SharedStyled.TooltipContainer>
                                      </SharedStyled.FlexRow>
                                      <SharedStyled.FlexRow gap="0" width="max-content">
                                        <Styled.ValueInput
                                          type="number"
                                          value={det.amount}
                                          className="new"
                                          onClick={(e) => {
                                            e.stopPropagation()
                                            setIsFullEdit(false)
          
                                            setIsEdit(true)
                                            setSelectedPiecework({ ...det, selectedPitchOrder: det?.pitchOrder })
                                            setShowNewPieceWork(true)
                                          }}
                                          // onChange={onChangeTearOffValue(index1, index)}
                                        />
                                        <Styled.UnitDiv>{det.unit?.split(' ')[0]?.trim()}</Styled.UnitDiv>
                                      </SharedStyled.FlexRow>
                                    </SharedStyled.FlexRow>
                                  ))} */}

                                        {/* ========================================= Test ========================================= */}
                                        <DraggableDiv
                                          items={item?.pieceWorkSettings?.noPitchArray}
                                          isObject
                                          setItems={updateSequence(
                                            item?.pieceWorkSettings?.noPitchArray,
                                            setBool,
                                            handleSequenceUpdate,
                                            idxRoot
                                          )}
                                          noTab
                                          renderChild={(det, index1) => {
                                            return (
                                              <SharedStyled.FlexRow
                                                gap="20px"
                                                key={index1}
                                                onClick={() => {
                                                  setIsEdit(true)
                                                  setSelectedPiecework({ ...det, selectedPitchOrder: det?.pitchOrder })
                                                  setShowNewPieceWork(true)
                                                  setIsFullEdit(true)
                                                }}
                                                alignItems="flex-end"
                                              >
                                                <SharedStyled.FlexRow gap="0" width="max-content" tabIndex={-1}>
                                                  <span className="dots">
                                                    <svg
                                                      width="24"
                                                      height="24"
                                                      viewBox="0 0 24 24"
                                                      style={{ rotate: '90deg', marginTop: '5px' }}
                                                    >
                                                      <path d="M7 10h2v2H7v-2zm0 4h2v2H7v-2zm4-4h2v2h-2v-2zm0 4h2v2h-2v-2zm4-4h2v2h-2v-2zm0 4h2v2h-2v-2z" />
                                                    </svg>{' '}
                                                  </span>
                                                  <Styled.NoPitchTitle>{det?.name}</Styled.NoPitchTitle>
                                                  <SharedStyled.TooltipContainer
                                                    positionLeft="0px"
                                                    positionBottom="0px"
                                                    positionLeftDecs="0px"
                                                    positionBottomDecs="0px"
                                                    className="small"
                                                  >
                                                    <span className="tooltip-content">{det?.description}</span>
                                                    <IButton>i</IButton>
                                                  </SharedStyled.TooltipContainer>
                                                </SharedStyled.FlexRow>
                                                <SharedStyled.FlexRow
                                                  gap="0"
                                                  width="max-content"
                                                  style={{ height: '32px' }}
                                                >
                                                  <Styled.ValueInput
                                                    type="number"
                                                    onWheel={handleWheel}
                                                    // value={det.amount}
                                                    className="new"
                                                    // disabled={inputLoading}
                                                    onClick={(e) => {
                                                      e.stopPropagation()
                                                      setIsFullEdit(false)

                                                      setIsEdit(true)
                                                      setSelectedPiecework({
                                                        ...det,
                                                        selectedPitchOrder: det?.pitchOrder,
                                                      })
                                                      // setShowNewPieceWork(true)
                                                    }}
                                                    onChange={(e) => {
                                                      setCurrentValue(e.target.value)
                                                    }}
                                                    defaultValue={det?.amount}
                                                    onBlur={(e: any) => {
                                                      newHandleBlur({
                                                        amount: Number(e.target.value),
                                                        prevAmount: det?.amount,
                                                        id: det?._id,
                                                        isPitch: false,
                                                      })
                                                    }}
                                                    // onChange={onChangeTearOffValue(index1, index)}
                                                  />
                                                  <Styled.UnitDiv>{det.unit?.split(' ')[0]?.trim()}</Styled.UnitDiv>
                                                </SharedStyled.FlexRow>

                                                {det?.hasLayer ? (
                                                  <SharedStyled.FlexCol
                                                    onClick={() => {
                                                      setIsEdit(true)
                                                      setSelectedPiecework({ ...det })
                                                      setShowNewPieceWork(true)
                                                      setIsFullEdit(true)
                                                    }}
                                                    gap="10px"
                                                    className="layer-container"
                                                  >
                                                    <Styled.NoPitchTitle>Additional Layers:</Styled.NoPitchTitle>
                                                    <SharedStyled.FlexRow width="max-content" gap="8px">
                                                      {/* <Toggle
                                            title=""
                                            isToggled={item?.workTask?.isLayer}
                                            onToggle={() => {
                                              // setIsUsesPitch((prev) => !prev)
                                            }}
                                            customStyles={{
                                              width: 'max-content',
                                              flexDirection: 'row-reverse',
                                              transform: 'rotate(-90deg)',
                                            }}
                                          /> */}

                                                      <CustomSelect
                                                        dropDownData={labourDropdown}
                                                        value={
                                                          det?.layer?.fixed?.isActive
                                                            ? labourDropdown[0]
                                                            : labourDropdown[1]
                                                        }
                                                        setValue={() => {}}
                                                        stateName=""
                                                        className="dropdown"
                                                      />

                                                      <SharedStyled.FlexCol gap="10px" width="max-content">
                                                        {!!det?.layer?.fixed?.isActive && (
                                                          <Styled.LayerCont
                                                            className="input"
                                                            isActive={det?.layer?.fixed?.isActive}
                                                          >
                                                            {/* <Styled.NoPitchTitle>Fixed ($)</Styled.NoPitchTitle> */}
                                                            <Styled.ValueInput
                                                              type="number"
                                                              onWheel={handleWheel}
                                                              value={det?.layer?.fixed?.value}
                                                              className="new"
                                                              // onClick={(e) => e.stopPropagation()}
                                                              // onChange={onChangeTearOffValue(index1, index)}
                                                            />
                                                          </Styled.LayerCont>
                                                        )}
                                                        {!!det?.layer?.percent?.isActive && (
                                                          <Styled.LayerCont
                                                            className="input"
                                                            isActive={det?.layer?.percent?.isActive}
                                                          >
                                                            {/* <Styled.NoPitchTitle>Percent (%)</Styled.NoPitchTitle> */}
                                                            <Styled.ValueInput
                                                              type="number"
                                                              onWheel={handleWheel}
                                                              value={det?.layer?.percent?.value}
                                                              className="new"
                                                              // onClick={(e) => e.stopPropagation()}
                                                              // onChange={onChangeTearOffValue(index1, index)}
                                                            />
                                                          </Styled.LayerCont>
                                                        )}
                                                      </SharedStyled.FlexCol>
                                                    </SharedStyled.FlexRow>
                                                  </SharedStyled.FlexCol>
                                                ) : null}
                                              </SharedStyled.FlexRow>
                                            )
                                          }}
                                        />
                                        {/* ========================================= Test ========================================= */}
                                      </>
                                    </SharedStyled.FlexCol>
                                  </>
                                ) : null}
                              </Fragment>
                            ))}
                          </>
                        )}
                      </SharedStyled.FlexCol>

                      {/* ========================================= old ========================================= */}
                      <>
                        {/*   <div style={{ width: '100%' }}>
                        <SharedStyled.FlexRow>
                          {roofingDetails.length > 0 && <Styled.HeadingDiv marginTop="10px">Roofing</Styled.HeadingDiv>}
          
                          <Button width="max-content" padding="8px 14px" onClick={() => setShowNewPieceWork(true)}>
                            Add new
                          </Button>
                        </SharedStyled.FlexRow>
                        <Styled.SingleWorkSettingsGridContainer marginTop="15px">
                          {roofingDetails?.map((detail: any, index1: number) => (
                            <Styled.SingleColumnDiv key={detail._id}>
                              <Styled.DescriptionDiv>{detail[0].description}</Styled.DescriptionDiv>
                              {detail
                                .sort((a: any, b: any) => a.order - b.order)
                                .map((det: any, index: number) => (
                                  <Styled.NameValueUnitContainer
                                    key={index}
                                    onClick={() => {
                                      setSelectedPiecework(det)
                                      setShowNewPieceWork(true)
                                    }}
                                  >
                                    <Styled.NameDiv>{det.name}:</Styled.NameDiv>
                                    <Styled.ValueInput
                                      type="number"
                                      value={det.amount}
                                      onChange={onChangeRoofingValue(index1, index)}
                                    />
                                    <Styled.UnitDiv>{det.unit}</Styled.UnitDiv>
                                  </Styled.NameValueUnitContainer>
                                ))}
                            </Styled.SingleColumnDiv>
                          ))}
                        </Styled.SingleWorkSettingsGridContainer>
                      </div>
          
                      <SharedStyled.SectionTitle>Piece Work(Full Piece Work Crew)</SharedStyled.SectionTitle>
                      <div style={{ width: '100%' }}>
                        {tearOffDetailsNone.length > 0 && <Styled.HeadingDiv marginTop="10px">Tear Off</Styled.HeadingDiv>}
                        <Styled.SingleWorkSettingsGridContainer marginTop="15px">
                          {tearOffDetailsNone?.map((detail: any, index1: number) => (
                            <Styled.SingleColumnDiv key={detail._id}>
                              <Styled.DescriptionDiv>{detail[0].description}</Styled.DescriptionDiv>
                              {detail
                                .sort((a: any, b: any) => a.order - b.order)
                                .map((det: any, index: number) => (
                                  <Styled.NameValueUnitContainer key={index}>
                                    <Styled.NameDiv>{det.name}:</Styled.NameDiv>
                                    <Styled.ValueInput
                                      type="number"
                                      value={det.amount}
                                      onChange={onChangeTearOffValue(index1, index)}
                                    />
                                    <Styled.UnitDiv>{det.unit}</Styled.UnitDiv>
                                  </Styled.NameValueUnitContainer>
                                ))}
                            </Styled.SingleColumnDiv>
                          ))}
                        </Styled.SingleWorkSettingsGridContainer>
                      </div>
          
                      <div style={{ width: '100%' }}>
                        {roofingDetailsNone.length > 0 && <Styled.HeadingDiv marginTop="10px">Roofing</Styled.HeadingDiv>}
                        <Styled.SingleWorkSettingsGridContainer marginTop="15px">
                          {roofingDetailsNone?.map((detail: any, index1: number) => (
                            <Styled.SingleColumnDiv key={detail._id}>
                              <Styled.DescriptionDiv>{detail[0].description}</Styled.DescriptionDiv>
                              {detail
                                .sort((a: any, b: any) => a.order - b.order)
                                .map((det: any, index: number) => (
                                  <Styled.NameValueUnitContainer key={index}>
                                    <Styled.NameDiv>{det.name}:</Styled.NameDiv>
                                    <Styled.ValueInput
                                      type="number"
                                      value={det.amount}
                                      onChange={onChangeRoofingValue(index1, index)}
                                    />
                                    <Styled.UnitDiv>{det.unit}</Styled.UnitDiv>
                                  </Styled.NameValueUnitContainer>
                                ))}
                            </Styled.SingleColumnDiv>
                          ))}
                        </Styled.SingleWorkSettingsGridContainer>
                      </div>
                      <div style={{ width: '100%' }}>
                        {pitchDetails.length > 0 && (
                          <>
                            <SharedStyled.SectionTitle>Pitch Modifier</SharedStyled.SectionTitle>
                          </>
                        )}
                        <Styled.SingleWorkSettingsGridContainer marginTop="15px" className="pitch">
                          {pitchDetails?.map((detail: any, index1: number) => (
                            <Styled.SingleColumnDiv key={detail._id}>
                              <Styled.DescriptionDiv>{detail[0].description}</Styled.DescriptionDiv>
                              {detail
                                .sort((a: any, b: any) => a.order - b.order)
                                .map((det: any, index: number) => (
                                  <Styled.NameValueUnitContainer key={index}>
                                    <Styled.NameDiv>{det.name}:</Styled.NameDiv>
                                    <Styled.ValueInput
                                      type="number"
                                      value={det.amount}
                                      onChange={onChangePitchModifierValue(index1, index)}
                                    />
                                    <Styled.UnitDiv>{det.unit}</Styled.UnitDiv>
                                  </Styled.NameValueUnitContainer>
                                ))}
                            </Styled.SingleColumnDiv>
                          ))}
                        </Styled.SingleWorkSettingsGridContainer>
          
                        {customDetails.length > 0 && (
                          <>
                            <SharedStyled.ContentHeader margin="20px 0 0 0" textAlign="left">
                              Custom
                            </SharedStyled.ContentHeader>
                            <SharedStyled.HorizontalDivider />
                          </>
                        )}
          
                        <Styled.SingleWorkSettingsGridContainer1 marginTop="15px">
                          {customDetails?.map((detail: any, index1: number) => (
                            <Styled.SingleColumnDiv key={detail._id}>
                              <Styled.DescriptionDiv>{detail[0].description}</Styled.DescriptionDiv>
                              {detail.map((det: any, index: number) => (
                                <Styled.ContentAddButtonDiv key={index}>
                                  <Styled.NameValueUnitContainer minWidth="fit-content">
                                    <Styled.NameDiv>{det.name}:</Styled.NameDiv>
                                    <Styled.ValueInput
                                      type="number"
                                      value={det.amount}
                                      onChange={onChangeCustomValue(index1, index)}
                                      width="108px"
                                    />
                                    <Styled.UnitDiv>{det.unit}</Styled.UnitDiv>
                                  </Styled.NameValueUnitContainer>
                                  {detail.length === index + 1 && (
                                    <Styled.MiniAddButton
                                      onClick={() => {
                                        setShowAddCustomRowPopup(true)
                                        setNewRowDetail({ order: det.order, subType: det.subType })
                                      }}
                                    >
                                      <AddIconNoCircleIcon />
                                    </Styled.MiniAddButton>
                                  )}
                                  <Styled.DeleteButton
                                    onClick={() => {
                                      setShowRemoveCustomRowPopup(true)
                                      setToDeleteRow(det)
                                    }}
                                  >
                                    <DeleteIcon />
                                  </Styled.DeleteButton>
                                </Styled.ContentAddButtonDiv>
                              ))}
                            </Styled.SingleColumnDiv>
                          ))}
                        </Styled.SingleWorkSettingsGridContainer1>
                        <SharedStyled.FlexBox width="100%" alignItems="center" gap="20px" wrap="wrap" margin="0 0 70px 0">
                          <Button type="submit" isLoading={loading} maxWidth="200px" onClick={() => onSave()}>
                            Save
                          </Button>
                        </SharedStyled.FlexBox>
                      </div>
          */}
                      </>
                      {/* ========================================= old ========================================= */}
                    </Styled.NewPieceworkCont>
                    {/* </Styled.PieceWorkSettingsSalariedCrewContainer> */}
                    <CustomModal show={showAddCustomSettingsPopup}>
                      <AddCustomSettings
                        setShowAddCustomSettingsPopup={setShowAddCustomSettingsPopup}
                        setDetailsUpdate={setDetailsUpdate}
                        newSubType={newSubType}
                      />
                    </CustomModal>

                    <CustomModal show={showNewPieceWork} className="width">
                      <AddNewPieceWork
                        isEdit={isEdit}
                        showall={showall}
                        onClose={() => {
                          setIsEdit(false)
                          setSelectedPiecework({})
                          setShowNewPieceWork(false)
                        }}
                        version={selectedVersion?._id}
                        setDetailsUpdate={setDetailsUpdate}
                        unitsDrop={unitsDrop}
                        isFullEdit={isFullEdit}
                        units={units}
                        editValues={{ ...selectedPiecework }}
                      />
                    </CustomModal>
                    <CustomModal show={showAddCustomRowPopup}>
                      <AddCustomRow
                        setShowAddCustomRowPopup={setShowAddCustomRowPopup}
                        setDetailsUpdate={setDetailsUpdate}
                        newRowDetail={newRowDetail}
                      />
                    </CustomModal>
                    <CustomModal show={showAddPieceworkVersion} className="width">
                      <AddPieceworkVersion
                        onClose={() => {
                          setShowAddPieceworkVersion(false)
                          setSelectedVersionId('')
                        }}
                        editValues={{
                          name: pwVersions?.find((itm) => itm?._id === selectedVersionId)?.name!,
                        }}
                        pwVersions={pwVersions}
                        selectedVersionId={selectedVersionId}
                        setDetailsUpdate={setDetailsUpdate}
                      />
                    </CustomModal>
                    <CustomModal show={showPositionModal} className="width">
                      <AddPositionVersion
                        onClose={() => {
                          setShowPositionModal(false)
                          setSelectedPosition({})
                        }}
                        editValues={{
                          position: selectedPosition?.position!,
                          selectedVersionId: selectedVersion?._id!,
                        }}
                        setDetailsUpdate={setDetailsUpdate}
                        positionsAvailableList={positionsAvailableList}
                        currentPositionList={currentPositionList}
                      />
                    </CustomModal>
                    <CustomModal show={showRemoveCustomRowPopup}>
                      <RemoveCustomRow
                        setShowRemoveCustomRowPopup={setShowRemoveCustomRowPopup}
                        setDetailsUpdate={setDetailsUpdate}
                        toDeleteRow={toDeleteRow}
                      />
                    </CustomModal>
                  </>
                ),
              },
              {
                title: 'Inactive',
                render: () => <DeletedPiecework />,
              },
            ]}
            filterComponent={<></>}
          />
        </SharedStyled.FlexCol>
      </SharedStyled.FlexRow>
    </>
  )
}

export default NewPieceWorkSettingsSalariedCrew
