import { Form, Formik } from 'formik'
import { useEffect, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate, useParams } from 'react-router-dom'
import * as Yup from 'yup'

import UnitSvg from '../../../../assets/newIcons/unitModal.svg'
import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import { upsertPieceWorkSetting } from '../../../../logic/apis/pieceWork'
import CustomSelect from '../../../../shared/customSelect/CustomSelect'
import { Dropdown } from '../../../../shared/dropDown/Dropdown'
import { StorageKey, UNITS } from '../../../../shared/helpers/constants'
import { onlyNumber } from '../../../../shared/helpers/regex'
import { getDataFromLocalStorage, isSuccess, notify } from '../../../../shared/helpers/util'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import * as SharedStyled from '../../../../styles/styled'
import { ModalHeaderInfo } from '../../../units/components/newUnitModal/style'
import * as Styled from './style'

/**
 * InitialValues is an interface declared here so that it can be used as a type for the useState hook
 */
interface InitialValues {
  name: string
  value: string
  unit: string
  workerType: string
  description: string
}

/**
 * I_AddCustomSettings is an interface which defines the props type which we receive from our parent component to this component
 */
interface I_AddCustomSettings {
  setShowAddCustomSettingsPopup: React.Dispatch<React.SetStateAction<boolean>>
  setDetailsUpdate: React.Dispatch<React.SetStateAction<boolean>>
  newSubType: number
}

export const AddCustomSettings = (props: I_AddCustomSettings) => {
  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */
  const [initialValues, setInitialValues] = useState<InitialValues>({
    name: '',
    value: '',
    unit: '',
    workerType: '',
    description: '',
  })

  const [crewDetails, setCrewDetails] = useState({})

  /**
   * loading will be the loading state when performing the operations
   */
  const [loading, setLoading] = useState<boolean>(false)
  const [deleteLoading, setDeleteLoading] = useState<boolean>(false)
  const [managerData, setManagerData] = useState<any>([])
  const [managerIdData, setManagerIdData] = useState<any>({})

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company

  const navigate = useNavigate()

  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [inputRef])
  /**
   * Destructuring the values from the props received
   */
  const { setShowAddCustomSettingsPopup, setDetailsUpdate, newSubType } = props

  /**
   * AddCustomSettingsSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */
  const AddCustomSettingsSchema = Yup.object().shape({
    name: Yup.string().required('No name provided.'),
    value: Yup.string().required('No value provided.').matches(onlyNumber, 'Enter Valid value'),
    unit: Yup.string().required('No unit provided.'),
    workerType: Yup.string().required('No Worker Type provided.'),
    description: Yup.string().required('No description provided.'),
  })

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async (submittedValues: InitialValues, { resetForm }: any) => {
    try {
      // if (Object.keys(currentCompany).length > 0) {
      setLoading(true)

      let createdBy = currentMember._id
      let dataObj: any = {
        name: submittedValues.name,
        amount: submittedValues.unit === '%' ? Number(submittedValues.value) / 100 : submittedValues.value,
        workerType: submittedValues.workerType,
        unit: submittedValues.unit,
        type: 'Custom',
        subType: newSubType,
        description: submittedValues.description,
        order: 1,
        createdBy,
      }
      let response = await upsertPieceWorkSetting([dataObj])
      if (isSuccess(response)) {
        notify('Added Custom Settings Successfully', 'success')
        setDetailsUpdate((prev) => !prev)
        setShowAddCustomSettingsPopup(false)
        resetForm()
        setLoading(false)
      } else {
        notify(response?.data?.message, 'error')
        setLoading(false)
      }
      // }
    } catch (error) {
      console.error('AddCustomSettings handleSubmit', error)
      setLoading(false)
    }
  }

  return (
    <Styled.AddCustomSettingsFormContainer>
      <Formik
        initialValues={initialValues}
        enableReinitialize={true}
        onSubmit={handleSubmit}
        validationSchema={AddCustomSettingsSchema}
        validateOnChange={true}
        validateOnBlur={false}
      >
        {({ values, errors, touched, setFieldValue, resetForm }) => {
          return (
            <>
              <Styled.ModalHeaderContainer>
                <SharedStyled.FlexRow>
                  <img src={UnitSvg} alt="modal icon" />
                  <SharedStyled.FlexCol>
                    <Styled.ModalHeader>Add Custom Settings</Styled.ModalHeader>
                  </SharedStyled.FlexCol>
                </SharedStyled.FlexRow>
                <Styled.CrossContainer onClick={() => setShowAddCustomSettingsPopup(false)}>
                  <CrossIcon />
                </Styled.CrossContainer>
              </Styled.ModalHeaderContainer>
              <SharedStyled.SettingModalContentContainer>
                <Form className="form">
                  <SharedStyled.Content maxWidth="706px" width="100%" disableBoxShadow={true} noPadding={true}>
                    <InputWithValidation
                      labelName="Name"
                      stateName="name"
                      value={values.name}
                      error={touched.name && errors.name ? true : false}
                      passRef={inputRef}
                    />
                    <InputWithValidation
                      labelName="Description"
                      stateName="description"
                      value={values.description}
                      error={touched.description && errors.description ? true : false}
                    />
                    <InputWithValidation
                      labelName="Value"
                      stateName="value"
                      value={values.value}
                      error={touched.value && errors.value ? true : false}
                    />
                    <CustomSelect
                      value={values.workerType}
                      labelName="Worker Type"
                      stateName="workerType"
                      dropDownData={['default', 'salaried']}
                      setFieldValue={setFieldValue}
                      setValue={() => {}}
                      margin="10px 0 0 0"
                      error={touched.workerType && errors.workerType ? true : false}
                    />
                    <CustomSelect
                      value={values.unit}
                      labelName="Unit"
                      stateName="unit"
                      dropDownData={UNITS}
                      setFieldValue={setFieldValue}
                      setValue={() => {}}
                      margin="10px 0 0 0"
                      error={touched.unit && errors.unit ? true : false}
                    />

                    <SharedStyled.ButtonContainer marginTop="20px">
                      <SharedStyled.Button type="submit">
                        {loading ? (
                          <>
                            Adding..
                            <SharedStyled.Loader />
                          </>
                        ) : (
                          'Add Custom Setting'
                        )}
                      </SharedStyled.Button>
                    </SharedStyled.ButtonContainer>
                  </SharedStyled.Content>
                </Form>
              </SharedStyled.SettingModalContentContainer>
            </>
          )
        }}
      </Formik>
    </Styled.AddCustomSettingsFormContainer>
  )
}
