import { Form, Formik } from 'formik'
import { useEffect, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useParams } from 'react-router-dom'
import * as Yup from 'yup'
import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import { upsertPieceWorkSetting } from '../../../../logic/apis/pieceWork'
import { Dropdown } from '../../../../shared/dropDown/Dropdown'
import { StorageKey, UNITS } from '../../../../shared/helpers/constants'
import { onlyNumber } from '../../../../shared/helpers/regex'
import { getDataFromLocalStorage, isSuccess, notify } from '../../../../shared/helpers/util'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import * as SharedStyled from '../../../../styles/styled'
import * as Styled from './style'

/**
 * InitialValues is an interface declared here so that it can be used as a type for the useState hook
 */
interface InitialValues {
  name: string
  value: string
  unit: string
}

/**
 * I_AddCustomRow is an interface which defines the props type which we receive from our parent component to this component
 */
interface I_AddCustomRow {
  setShowAddCustomRowPopup: React.Dispatch<React.SetStateAction<boolean>>
  setDetailsUpdate: React.Dispatch<React.SetStateAction<boolean>>
  newRowDetail: any
}

export const AddCustomRow = (props: I_AddCustomRow) => {
  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */
  const [initialValues, setInitialValues] = useState<InitialValues>({
    name: '',
    value: '',
    unit: '',
  })

  /**
   * loading will be the loading state when performing the operations
   */
  const [loading, setLoading] = useState<boolean>(false)

  /**
   * Destructuring the values from the props received
   */
  const { setShowAddCustomRowPopup, setDetailsUpdate, newRowDetail } = props

  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [inputRef])
  /**
   * AddCustomRowSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */
  const AddCustomRowSchema = Yup.object().shape({
    name: Yup.string().required('No name provided.'),
    value: Yup.string().required('No value provided.').matches(onlyNumber, 'Enter Valid value'),
    unit: Yup.string().required('No unit provided.'),
  })

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company
  const { crewId } = useParams()

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async (submittedValues: InitialValues, { resetForm }: any) => {
    try {
      // if (Object.keys(currentCompany).length > 0) {
      setLoading(true)

      let createdBy = currentMember._id
      let dataObj: any = {
        name: submittedValues.name,
        amount: submittedValues.unit === '%' ? Number(submittedValues.value) / 100 : submittedValues.value,
        unit: submittedValues.unit,
        type: 'Custom',
        subType: newRowDetail.subType,
        order: newRowDetail.order,
        createdBy,
      }
      let response = await upsertPieceWorkSetting([dataObj])
      if (isSuccess(response)) {
        notify('Added New Row Successfully', 'success')
        setDetailsUpdate((prev) => !prev)
        setShowAddCustomRowPopup(false)
        resetForm()
        setLoading(false)
      } else {
        notify(response?.data?.message, 'error')
        setLoading(false)
      }
      // }
    } catch (error) {
      console.error('AddCustomRow handleSubmit', error)
      setLoading(false)
    }
  }

  return (
    <Styled.AddCustomSettingsFormContainer>
      <Formik
        initialValues={initialValues}
        enableReinitialize={true}
        onSubmit={handleSubmit}
        validationSchema={AddCustomRowSchema}
        validateOnChange={true}
        validateOnBlur={false}
      >
        {({ values, errors, touched, setFieldValue, resetForm }) => {
          return (
            <>
              <Styled.ModalHeaderContainer>
                <Styled.ModalHeader>Add Custom Row</Styled.ModalHeader>
                <Styled.CrossContainer onClick={() => setShowAddCustomRowPopup(false)}>
                  <CrossIcon />
                </Styled.CrossContainer>
              </Styled.ModalHeaderContainer>
              <SharedStyled.SettingModalContentContainer padding="0px 20px 20px 20px">
                <Form className="form">
                  <SharedStyled.Content maxWidth="706px" width="100%" disableBoxShadow={true} noPadding={true}>
                    <InputWithValidation
                      labelName="Name"
                      stateName="name"
                      value={values.name}
                      error={touched.name && errors.name ? true : false}
                      passRef={inputRef}
                    />
                    <InputWithValidation
                      labelName="Value"
                      stateName="value"
                      value={values.value}
                      error={touched.value && errors.value ? true : false}
                    />
                    <Dropdown
                      value={values.unit}
                      labelName="Unit"
                      stateName="unit"
                      dropDownData={UNITS}
                      setFieldValue={setFieldValue}
                      error={touched.unit && errors.unit ? true : false}
                    />

                    <SharedStyled.ButtonContainer marginTop="20px">
                      <SharedStyled.Button type="submit">
                        {loading ? (
                          <>
                            Adding..
                            <SharedStyled.Loader />
                          </>
                        ) : (
                          'Add Custom Row'
                        )}
                      </SharedStyled.Button>
                    </SharedStyled.ButtonContainer>
                  </SharedStyled.Content>
                </Form>
              </SharedStyled.SettingModalContentContainer>
            </>
          )
        }}
      </Formik>
    </Styled.AddCustomSettingsFormContainer>
  )
}
