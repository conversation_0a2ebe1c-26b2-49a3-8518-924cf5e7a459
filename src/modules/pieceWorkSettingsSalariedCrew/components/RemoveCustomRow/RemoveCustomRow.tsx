import { Form, Formik } from 'formik'
import { useState } from 'react'
import { useSelector } from 'react-redux'
import { useParams } from 'react-router-dom'
import * as Yup from 'yup'
import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import { upsertPieceWorkSetting } from '../../../../logic/apis/pieceWork'
import { Dropdown } from '../../../../shared/dropDown/Dropdown'
import { StorageKey, UNITS } from '../../../../shared/helpers/constants'
import { onlyNumber } from '../../../../shared/helpers/regex'
import { getDataFromLocalStorage, isSuccess, notify } from '../../../../shared/helpers/util'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import * as SharedStyled from '../../../../styles/styled'
import { colors } from '../../../../styles/theme'
import * as Styled from './style'

/**
 * InitialValues is an interface declared here so that it can be used as a type for the useState hook
 */
interface InitialValues {
  name: string
  value: string
  unit: string
}

/**
 * I_AddCustomRow is an interface which defines the props type which we receive from our parent component to this component
 */
interface I_AddCustomRow {
  setShowRemoveCustomRowPopup: React.Dispatch<React.SetStateAction<boolean>>
  setDetailsUpdate: React.Dispatch<React.SetStateAction<boolean>>
  toDeleteRow: any
}

export const RemoveCustomRow = (props: I_AddCustomRow) => {
  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */
  const [initialValues, setInitialValues] = useState<InitialValues>({
    name: '',
    value: '',
    unit: '',
  })

  /**
   * loading will be the loading state when performing the operations
   */
  const [loading, setLoading] = useState<boolean>(false)

  /**
   * Destructuring the values from the props received
   */
  const { setShowRemoveCustomRowPopup, setDetailsUpdate, toDeleteRow } = props

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company
  const { crewId } = useParams()

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async () => {
    try {
      // if (Object.keys(currentCompany).length > 0) {
      setLoading(true)
      let createdBy = currentMember._id
      let dataObj: any = {
        ...toDeleteRow,
        createdBy,
        deleted: true,
      }
      let response = await upsertPieceWorkSetting([dataObj])
      if (isSuccess(response)) {
        notify('Deleted The Row Successfully', 'success')
        setDetailsUpdate((prev) => !prev)
        setShowRemoveCustomRowPopup(false)
        setLoading(false)
      } else {
        notify(response?.data?.message, 'error')
        setLoading(false)
      }
      // }
    } catch (error) {
      console.error('RemoveCustomRow handleSubmit', error)
      setLoading(false)
    }
  }

  return (
    <Styled.RemoveCustomRowFormContainer>
      <Styled.ModalHeaderContainer>
        <Styled.ModalHeader>Remove Custom Row</Styled.ModalHeader>
        <Styled.CrossContainer onClick={() => setShowRemoveCustomRowPopup(false)}>
          <CrossIcon />
        </Styled.CrossContainer>
      </Styled.ModalHeaderContainer>
      <Styled.ModalBodyContainer>
        <Styled.ModalDescription>Are you sure you want to delete this row ?</Styled.ModalDescription>
        <SharedStyled.FlexBox width="100%" alignItems="center" justifyContent="space-around" marginTop="20px">
          <SharedStyled.Button
            type="submit"
            bgColor={colors.error}
            color={colors.white}
            maxWidth="200px"
            onClick={() => handleSubmit()}
          >
            {loading ? (
              <>
                Deleting..
                <SharedStyled.Loader />
              </>
            ) : (
              'Yes'
            )}
          </SharedStyled.Button>
          <SharedStyled.Button type="submit" onClick={() => setShowRemoveCustomRowPopup(false)} maxWidth="200px">
            No
          </SharedStyled.Button>
        </SharedStyled.FlexBox>
      </Styled.ModalBodyContainer>
    </Styled.RemoveCustomRowFormContainer>
  )
}
