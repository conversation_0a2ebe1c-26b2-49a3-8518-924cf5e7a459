import { useCallback, useState } from 'react'
import { useSelector } from 'react-redux'

import { getDeletedPiecework, permDeletePiecework, restorePiecework } from '../../../../logic/apis/pieceWork'
import { Table } from '../../../../shared/table/Table'
import { isSuccess, notify } from '../../../../shared/helpers/util'
import { CustomModal } from '../../../../shared/customModal/CustomModal'
import DeletedModal, { IDeleted } from '../../../deleted/components/deletedModal/DeletedModal'

const DeletedPiecework = () => {
  const globalSelector = useSelector((state: any) => state)
  const { currentCompany } = globalSelector.company
  const [loading, setLoading] = useState<boolean>(false)
  const [data, setData] = useState<any[]>([])
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [inputData, setInputData] = useState<IDeleted>()
  const [btnLoadingRestore, setBtnLoadingRestore] = useState(false)
  const [btnLoadingDelete, setBtnLoadingDelete] = useState(false)

  const columns = [
    {
      Header: 'Name',
      accessor: 'name',
    },
    {
      Header: 'WorkTask',
      accessor: 'workTask',
    },
  ]

  const fetchData = useCallback(async () => {
    try {
      setLoading(true)
      const receivedData: any = []

      const res = await getDeletedPiecework(currentCompany?._id)

      if (isSuccess(res)) {
        const statusRes = res?.data?.data?.pieceWorkSettings

        const customResponse = statusRes?.map((itm: { workTask: { name: string } }) => ({
          ...itm,
          workTask: itm?.workTask?.name,
        }))

        receivedData.push(...customResponse)
      } else {
        notify(res?.data?.message, 'error')
      }

      setData(receivedData)
    } catch (error) {
      console.error('TeamTable fetchData error', error)
    } finally {
      setLoading(false)
    }
  }, [])

  const onDelete = async () => {
    setBtnLoadingDelete(true)

    try {
      const res = await permDeletePiecework({
        pieceworkId: inputData?._id!,
      })

      if (isSuccess(res)) {
        notify(res?.data?.data?.message, 'success')
      }
    } catch (err) {
      console.log('Piecework delete err', err)
    } finally {
      setShowDeleteModal(false)
      setInputData(undefined)
      fetchData()
      setBtnLoadingDelete(false)
    }
  }

  const onRestore = async () => {
    setBtnLoadingRestore(true)
    try {
      const res = await restorePiecework({
        pieceworkId: inputData?._id!,
      })
      if (isSuccess(res)) {
        notify(res?.data?.data?.message, 'success')
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('Piecework delete err', err)
    } finally {
      setShowDeleteModal(false)
      setInputData(undefined)
      fetchData()
      setBtnLoadingRestore(false)
    }
  }

  return (
    <section>
      <Table
        columns={columns}
        data={data}
        fetchData={fetchData}
        // client={true}
        noOverflow
        noSearch
        onRowClick={(data) => {
          setInputData(data)
          setShowDeleteModal(true)
        }}
        loading={loading}
      />

      <CustomModal show={showDeleteModal}>
        <DeletedModal
          onDelete={onDelete}
          onRestore={onRestore}
          onClose={() => {
            setShowDeleteModal(false)
            setInputData(undefined)
          }}
          inputData={inputData}
          title="Deleted Piecework"
          btnLoadingDelete={btnLoadingDelete}
          btnLoadingRestore={btnLoadingRestore}
        />
      </CustomModal>
    </section>
  )
}

export default DeletedPiecework
