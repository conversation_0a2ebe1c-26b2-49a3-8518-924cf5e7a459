import { memo, useEffect, useState } from 'react'
import { use<PERSON>avigate, Link } from 'react-router-dom'

import { getProfileInfo } from '../../../../logic/apis/profile'
import { setIsLoggedIn, setProfileInfo } from '../../../../logic/redux/actions/auth'
import { useAppDispatch, useAppSelector } from '../../../../logic/redux/reduxHook'
import { OptionItem } from '../../../../shared/dropDown/style'
import { StorageKey } from '../../../../shared/helpers/constants'
import { AvatarSvg, SettingsSvg, HelpSvg } from '../../../../shared/helpers/images'
import { getDataFromLocalStorage, getInitials, isSuccess, notify, uuidToColor } from '../../../../shared/helpers/util'
import { FlexRow } from '../../../../styles/styled'
import * as Styled from '../style'
import LogoutSvg from '../../../../assets/newIcons/logout.svg'

import ProfileEditSvg from '../../../../assets/newIcons/profileEdit.svg'
import { profilePath, signinPath } from '../../../../logic/paths'
import { setCompanies, setCurrentCompany } from '../../../../logic/redux/actions/company'
import { setIsInvited } from '../../../../logic/redux/actions/invitation'
import { signout } from '../../../../logic/apis/auth'
import { SmallLoaderCont } from '../../../../shared/components/loader/style'
import { Info, NameCont } from '../../../../shared/components/profileInfo/style'

const ProfileDrop = () => {
  const dispatch = useAppDispatch()
  const globalSelector = useAppSelector((state: any) => state)
  const { profileInfo } = globalSelector.auth
  const { position, triggerRefetch } = globalSelector.company
  const navigate = useNavigate()
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    const getDetails = async () => {
      try {
        const response = await getProfileInfo()
        if (isSuccess(response)) {
          const user = response?.data?.data?.user

          dispatch(setProfileInfo({ ...user }))
        } else {
          notify(response?.data?.message, 'error')
        }
      } catch (error) {
        console.error('getDetails error', error)
      }
    }

    getDetails()
  }, [triggerRefetch])

  const handleLogOut = async (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    e.stopPropagation()

    try {
      setLoading(true)
      // const response = await signout()
      // if (isSuccess(response)) {
      notify('Signed out successfully', 'success')
      localStorage.clear()
      dispatch(setIsLoggedIn(false))
      dispatch(setCompanies([]))
      dispatch(setIsInvited(false))
      dispatch(setCurrentCompany({}))
      navigate(signinPath)
      window.location.reload()
      // }
    } catch (error) {
      console.error('Signout error=====>', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Styled.ProfileCont>
      <FlexRow gap="12px" as={Link} to={profilePath}>
        {profileInfo?.imageUrl ? (
          <Styled.Avatar src={profileInfo?.imageUrl ?? AvatarSvg} alt="avatar icon" />
        ) : (
          <NameCont bg={uuidToColor(profileInfo?.id!)} className="drop">
            <Info>{getInitials(`${profileInfo?.firstName} ${profileInfo?.lastName}`)}</Info>
          </NameCont>
        )}

        <Styled.ProfileInfoCont gap="4px">
          <p>{position}</p>

          <h5>
            {(profileInfo as any)?.firstName} {(profileInfo as any)?.lastName}
          </h5>
        </Styled.ProfileInfoCont>

        <Styled.EditIcon src={ProfileEditSvg} alt="profile edit icon" />
      </FlexRow>

      <OptionItem onClick={handleLogOut}>
        <FlexRow justifyContent="space-between" alignItems="center">
          <FlexRow>
            <img src={LogoutSvg} alt="settings icon" />
            <p>Logout</p>
          </FlexRow>

          {loading && (
            <div>
              <SmallLoaderCont className="black" />
            </div>
          )}
        </FlexRow>
      </OptionItem>
    </Styled.ProfileCont>
  )
}

export default memo(ProfileDrop)
