import styled from 'styled-components'
import { OptionItem } from '../../../shared/dropDown/style'
import { Nue } from '../../../shared/helpers/constants'
import { FlexRow, FlexCol } from '../../../styles/styled'
import { colors, screenSizes } from '../../../styles/theme'
import { ModalHeaderContainer } from '../../newProject/style'
import { DropDownContainer, DropDownItem } from '../../sales/style'

export const HeaderContainer = styled.header`
  grid-area: nav;
  width: 100%;
  min-height: 52px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  padding: 0 16px;
  border-bottom: 1px solid ${colors.lightGray};
  top: 0;
  position: sticky;
  top: 0;
  z-index: 100;
  background: white;

  & > div {
    height: 100%;
  }

  @media (min-width: ${screenSizes.XS}px) {
    min-height: 64px;
    padding: 0 20px;
    z-index: 10;
  }

  @media print {
    display: none;
  }
`

export const HamIconStyle = styled.img`
  cursor: pointer;

  &:hover {
    filter: brightness(50%);
  }

  &.rotate {
    transform: rotate(180deg);
  }
`

export const NavLeftWrap = styled(FlexRow)`
  height: 100%;
  align-items: center;

  .org {
    display: none;

    @media (min-width: ${screenSizes.XS}px) {
      display: block;
    }
  }

  gap: 10px;

  @media (min-width: ${screenSizes.XS}px) {
    gap: 20px;
  }

  .searchIcon {
    width: 20px;
    height: 20px;
  }
`

export const NavRightWrap = styled(FlexRow)`
  position: relative;
  width: max-content;
  justify-content: space-between;

  .add {
    display: none;
    @media (min-width: ${screenSizes.M}px) {
      display: block;
      width: max-content;
    }
  }

  .pop-avatar {
    height: 32px;
    @media (min-width: ${screenSizes.XS}px) {
      height: 40px;
    }
  }
`

export const SearchCont = styled(FlexRow)`
  gap: 0px;
  border-left: 1px solid ${colors.lightGray};

  padding-left: 10px;

  &.in-active {
    width: 100%;
    @media (min-width: ${screenSizes.M}px) {
      width: 400px;
    }
  }

  &#show-border {
    /* border: 1px solid #03a9f4; */
  }

  &.active {
    transition: width 0.4s;

    width: 100%;
    @media (min-width: ${screenSizes.M}px) {
      width: 600px;
    }
  }
  position: relative;
  .search-loader {
    position: static;
    input {
      height: 64px;
      border-radius: 0px;
      background: white;

      @media (max-width: ${screenSizes.XS}px) {
        padding: 0px 10px;

        &::placeholder {
          font-size: 10px;
        }
      }

      border-left: none;
      border-top: none;
      border-bottom: none;
    }
  }

  ${DropDownContainer} {
    position: static;
  }

  ${DropDownItem} {
    border-radius: 0px;
    @media (max-width: ${screenSizes.XS}px) {
      font-size: 12px;
    }
  }

  .height {
    top: 64px;
    box-shadow: rgba(0, 0, 0, 0.15) 0px 15px 25px, rgba(0, 0, 0, 0.05) 0px 5px 10px;
    border-radius: 0px 0px 8px 8px;
    border: 1px solid ${colors.lightGray};
  }

  @media (min-width: ${screenSizes.XS}px) {
    display: flex;
  }
`

export const Avatar = styled.img<{ bg?: string }>`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 1px solid #9747ff;
  cursor: pointer;
  aspect-ratio: 1;
  background: ${(props: any) => props.bg};

  @media (min-width: ${screenSizes.XS}px) {
    width: 40px;
    height: 40px;
  }
`

export const SelectedOrg = styled(FlexCol)`
  height: 100%;
  border: 1px solid ${colors.lightGray};
  padding: 0 12px;
  gap: 8px;
  white-space: nowrap;
  align-items: flex-start;

  p {
    color: ${colors.gray1};
    font-family: ${Nue.medium};
    font-size: 10px;
  }

  h5 {
    font-family: ${Nue.bold};
  }
  img {
    width: 20px;
    height: 20px;
    border-radius: 50%;
  }
`

export const ProfileCont = styled(FlexCol)`
  gap: 6px;
  padding: 8px;

  ${OptionItem} {
    display: flex;
    align-items: center;
    width: 100%;
    margin: 0;
    gap: 8px;
  }

  h5 {
    text-transform: capitalize;
    letter-spacing: 1px;
    font-family: ${Nue.medium};
  }
`

export const ProfileInfoCont = styled(SelectedOrg)`
  padding: 0;
  gap: 4px;
  border: none;
`

export const EditIcon = styled.img`
  padding-right: 10px;
`

export const SelectOrgCont = styled.section`
  width: 90vw;
  max-height: 450px;
  @media (min-width: ${screenSizes.XS}px) {
    display: none;
  }

  ${ModalHeaderContainer} {
    position: sticky;
    top: 0;
    background: white;
  }
`

export const OptionCont = styled(FlexCol)`
  gap: 8px;
  & > div {
    margin: 0;
    width: 100%;
  }
`
