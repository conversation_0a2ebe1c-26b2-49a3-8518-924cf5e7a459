import { Component, ErrorInfo, memo } from 'react'
import { ErrorBoundaryCont } from './style'
import Button from '../../../shared/components/button/Button'

interface Props {
  children?: React.ReactNode
}

interface State {
  hasError: boolean
}

class ErrorBoundary extends Component<Props, State> {
  state: State = {
    hasError: false,
  }

  static getDerivedStateFromError() {
    // Update state so the next render will show the fallback UI.
    return { hasError: true }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Uncaught error:', error, errorInfo)
  }

  render() {
    return this.state.hasError ? (
      <ErrorBoundaryCont>
        <h1> Sorry.. there was an error</h1>

        <Button
          onClick={() => {
            window?.location?.reload()
          }}
        >
          Reload
        </Button>
      </ErrorBoundaryCont>
    ) : (
      this.props.children
    )
  }
}

export default memo(ErrorBoundary)
