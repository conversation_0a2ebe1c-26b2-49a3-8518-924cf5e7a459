import styled from 'styled-components'
import { screenSizes } from '../../../styles/theme'
import AuthBg from '../../../assets/images/AuthBg.jpg'
import { opacityAnimation } from '../../../styles/styled'

// ======================== Auth-Layout ========================

export const ChildrenContainer = styled.div`
  height: 100vh;
  padding: 15px;
`

export const AuthLayoutContainer = styled.section`
  display: grid;
  height: 100vh;
  grid-template-columns: 1fr;
  height: 100%;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;

  @media (min-width: ${screenSizes.XS}px) {
    background-image: url(${AuthBg});

    &.plans {
      background-image: none;
    }
  }

  a {
    &:hover {
      text-decoration: underline;
    }
  }
`

// ============ Dashboard Layout ============

export const StyledRoutesWrapper = styled.div<{ navCollapsed?: boolean }>`
  display: block;
  box-sizing: border-box;
  @media (min-width: ${screenSizes.L}px) {
    display: grid;
    grid-template-columns: ${(props) => (props.navCollapsed ? '80px 1fr' : '18vw 1fr')};
    height: 100vh;
  }

  @media (min-width: ${screenSizes.XXL}px) {
    grid-template-columns: ${(props) => (props.navCollapsed ? '80px 1fr' : '256px 1fr')};
  }
  position: relative;
  transition: 0.4s;
`
export const SideNavCont = styled.div<{ navCollapsed?: boolean; showMobileSideNav?: boolean }>`
  display: ${(props) => (props.showMobileSideNav ? 'block' : 'none')};
  position: relative;
  width: ${(props) => (props.navCollapsed ? '80px' : '18vw')};

  max-width: 256px;

  box-sizing: border-box;
  overflow: hidden;
  @media (min-width: ${screenSizes.L}px) {
    display: block;
  }
  transition: width 0.4s;
`
export const StyledRoutes = styled.main`
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  position: relative;
  width: 100%;
`
export const ChildContainer = styled.section`
  padding: 24px;
  width: 100%;
  min-height: calc(100vh - 72px);
  /* max-width: 1440px; */
  & > * {
    animation: ${opacityAnimation} 250ms ease-in-out;
  }

  @media (max-width: ${screenSizes.XS}px) {
    padding: 24px 16px;
  }

  @media print {
    padding: 0;
  }
`
