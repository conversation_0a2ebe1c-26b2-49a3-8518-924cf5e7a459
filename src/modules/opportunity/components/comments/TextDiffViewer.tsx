import { useEffect, useState } from 'react'

const getFormattedDiff = async (original: string, edited: string) => {
  // @ts-ignore
  const DiffMatchPatch = (await import('diff-match-patch')).default
  const dmp = new DiffMatchPatch()

  const diffs = dmp.diff_main(original, edited)
  dmp.diff_cleanupSemantic(diffs)

  return diffs.map(([type, text]: any, index: number) => {
    switch (type) {
      case DiffMatchPatch.DIFF_INSERT:
        return (
          <span key={index} style={{ backgroundColor: '#d4fcdc', color: '#22863a' }}>
            {text}
          </span>
        )
      case DiffMatchPatch.DIFF_DELETE:
        return (
          <span key={index} style={{ backgroundColor: '#fcdcdc', color: '#b31d28', textDecoration: 'line-through' }}>
            {text}
          </span>
        )
      case DiffMatchPatch.DIFF_EQUAL:
      default:
        return <span key={index}>{text}</span>
    }
  })
}

const TextDiffViewer = ({ original = '', edited = '' }) => {
  const [val, setVal] = useState<any>(null)

  useEffect(() => {
    getFormattedDiff(original, edited).then((res) => {
      setVal(res)
    })
  }, [original, edited])

  return <div style={{ whiteSpace: 'pre-wrap', padding: '10px' }}>{val}</div>
}

export default TextDiffViewer
