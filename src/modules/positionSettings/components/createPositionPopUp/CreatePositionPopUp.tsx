import { Form, Formik } from 'formik'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import * as Yup from 'yup'

import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import UnitSvg from '../../../../assets/newIcons/unitModal.svg'

import { createPosition } from '../../../../logic/apis/position'
import { Permissions, PermissionsNum, StorageKey } from '../../../../shared/helpers/constants'
import { getDataFromLocalStorage, isSuccess, notify, simplifyBackendError } from '../../../../shared/helpers/util'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import { Table } from '../../../../shared/table/Table'
import * as SharedStyled from '../../../../styles/styled'
import * as Styled from './style'
import Button from '../../../../shared/components/button/Button'

interface InitialValues {
  positionName: string
  description: string
}

export interface PermissionResource {
  name: string
  permissions: number
  crud?: any
}

export interface PermissionCategory {
  category: string
  resources: PermissionResource[]
  resource: PermissionResource[]
}

interface I_CreatePositionPopUp {
  setShowCreatePositionPopUp: React.Dispatch<React.SetStateAction<boolean>>
  setDetailsUpdate: React.Dispatch<React.SetStateAction<boolean>>
  initialPermissionData: PermissionCategory[]
}

export const CreatePositionPopUp = (props: I_CreatePositionPopUp) => {
  const { setShowCreatePositionPopUp, setDetailsUpdate, initialPermissionData } = props

  const [permissionState, setPermissionState] = useState<PermissionCategory[]>([])
  const [checkboxState, setCheckboxState] = useState<any[]>([])
  const [expandedCategories, setExpandedCategories] = useState<{ [key: string]: boolean }>({})

  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [inputRef])

  const [loading, setLoading] = useState<boolean>(false)
  const [pageCount, setPageCount] = useState<number>(10)
  const [data, setData] = useState<any>([])
  const fetchIdRef = useRef(0)

  const onPermissionChange = (categoryIndex: number, resourceIndex: number, name: string, checked: boolean) => {
    try {
      const newPermissionState = [...permissionState]
      const newCheckboxState = [...checkboxState]

      if (name === 'None') {
        newPermissionState[categoryIndex].resources[resourceIndex].crud = {
          read: false,
          write: false,
          update: false,
          delete: false,
        }

        // Update checkbox state
        newCheckboxState[categoryIndex].resources[resourceIndex].crud = {
          read: false,
          write: false,
          update: false,
          delete: false,
        }
      }

      // Update permission state
      newPermissionState[categoryIndex].resources[resourceIndex].permissions = Permissions[name]

      // Update checkbox state
      newCheckboxState[categoryIndex].resources[resourceIndex] = {
        full: false,
        managed: false,
        self: false,
        none: false,
        [name.toLowerCase()]: checked,
      }

      setPermissionState(newPermissionState)
      setCheckboxState(newCheckboxState)
    } catch (error) {
      console.error('onPermissionChange error', error)
    }
  }

  const onReadOnlyChange = (categoryIndex: number, resourceIndex: number, readOnly: boolean) => {
    const newPermissionState = [...permissionState]

    if (readOnly) {
      newPermissionState[categoryIndex].resources[resourceIndex].crud = {
        read: true,
        write: false,
        update: false,
        delete: false,
      }
    }

    setPermissionState(newPermissionState)
  }

  const toggleCategoryExpand = (category: string) => {
    setExpandedCategories((prev) => ({
      ...prev,
      [category]: !prev[category],
    }))
  }

  const fetchData = useCallback(
    async ({ pageSize, pageIndex }: any) => {
      try {
        let receivedData: any[] = []

        if (initialPermissionData) {
          initialPermissionData.forEach((category, categoryIndex) => {
            // Add category header row
            receivedData.push({
              isCategoryHeader: true,
              category: category.category,
              categoryIndex: categoryIndex,
            })

            // Add resource rows if category is expanded
            if (expandedCategories[category.category] !== false) {
              category.resources.forEach((resource, resourceIndex) => {
                receivedData.push({
                  permissionName: <Styled.NameText>{resource.name}</Styled.NameText>,
                  full: (
                    <select
                      onChange={(e) => {
                        onPermissionChange(categoryIndex, resourceIndex, e.target.value, true)
                      }}
                    >
                      <option value="None" selected={resource?.permissions?.none || false}>
                        None
                      </option>
                      <option value="Self" selected={resource?.permissions?.self || false}>
                        Self
                      </option>

                      <option value="Managed" selected={resource?.permissions?.managed || false}>
                        Managed
                      </option>
                      <option value="Full" selected={resource?.permissions?.full || false}>
                        Full
                      </option>
                    </select>
                  ),
                  managed: (
                    <SharedStyled.FlexRow>
                      <Styled.CheckBox
                        width="15px"
                        height="20px"
                        type="checkbox"
                        id={`checkbox-${resourceIndex}`}
                        defaultChecked={false}
                        onChange={(e: any) => {
                          onReadOnlyChange(categoryIndex, resourceIndex, e.target.checked)
                        }}
                      />
                      <label htmlFor={`checkbox-${resourceIndex}`} style={{ color: 'black' }}>
                        Read-only
                      </label>
                    </SharedStyled.FlexRow>
                  ),
                  // self: (
                  //   <SharedStyled.CheckboxZoneLabel>
                  //     <Styled.CheckBox
                  //       width="15px"
                  //       height="20px"
                  //       type="checkbox"
                  //       checked={checkboxState[categoryIndex]?.resource[resourceIndex]?.self || false}
                  //       onChange={(e: any) => {
                  //         onPermissionChange(categoryIndex, resourceIndex, 'Self', e.target.checked)
                  //       }}
                  //     />
                  //   </SharedStyled.CheckboxZoneLabel>
                  // ),
                  // none: (
                  //   <SharedStyled.FlexRow>
                  //     <Styled.CheckBox width="15px" height="20px" type="checkbox" id={`checkbox-${resourceIndex}`} />
                  //     <label htmlFor={`checkbox-${resourceIndex}`} style={{ color: 'black' }}>
                  //       Read-only
                  //     </label>
                  //   </SharedStyled.FlexRow>
                  // ),
                })
              })
            }
          })
        }

        const fetchId = ++fetchIdRef.current

        if (fetchId === fetchIdRef.current) {
          const startRow = pageSize * pageIndex
          const endRow = startRow + pageSize
          // setTimeout(() => {
          setData(receivedData)
          // }, 1000)
          setPageCount(Math.ceil(receivedData.length / pageSize))
        }
      } catch (error) {
        console.error('TeamTable fetchData error', error)
      }
    },
    [initialPermissionData, checkboxState, expandedCategories]
  )

  const columns: any = useMemo(
    () => [
      {
        Header: '',
        accessor: 'permissionName',
        Cell: ({ row }: any) => {
          if (row.original.isCategoryHeader) {
            return (
              <Styled.CategoryHeader onClick={() => toggleCategoryExpand(row.original.category)}>
                {row.original.category.charAt(0).toUpperCase() + row.original.category.slice(1)}
                <span>{expandedCategories[row.original.category] === false ? <>&#9654;</> : <>&#9660;</>}</span>
              </Styled.CategoryHeader>
            )
          }
          return row.original.permissionName
        },
      },
      {
        Header: '',
        accessor: 'full',
      },
      {
        Header: '',
        accessor: 'managed',
      },
      // {
      //   Header: '',
      //   accessor: 'self',
      // },
      // {
      //   Header: '',
      //   accessor: 'none',
      // },
    ],
    [expandedCategories]
  )

  const [initialValues, setInitialValues] = useState<InitialValues>({
    positionName: '',
    description: '',
  })

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company

  const PositionCreationFormSchema = Yup.object().shape({
    positionName: Yup.string().required('No position name provided.'),
  })

  const handleSubmit = async (submittedValues: InitialValues, { resetForm }: any) => {
    try {
      // if (Object.keys(currentCompany).length > 0 && Object.keys(currentMember).length > 0) {
      setLoading(true)

      const permissionData = permissionState.map((category: any) => ({
        category: category.category,
        resources: category.resources,
      }))
      let dataObj = {
        position: submittedValues.positionName,
        description: submittedValues.description,
        createdBy: currentMember._id,
        permissions: permissionData,
      }

      let response = await createPosition(dataObj)
      if (isSuccess(response)) {
        notify('Position Created Successfully', 'success')
        resetForm()
        setDetailsUpdate((prev) => !prev)
        setLoading(false)
        setShowCreatePositionPopUp(false)
      } else {
        setLoading(false)
        notify(simplifyBackendError(response?.data?.message), 'error')
      }
      // }
    } catch (error) {
      console.error('Position Creation error', error)
    }
  }

  const onPermissionAction = () => {
    try {
      const newPermissionState: PermissionCategory[] = initialPermissionData.map((category) => ({
        ...category,
        resource: category.resources.map((resource) => ({
          ...resource,
          permissions: resource.permissions,
        })),
      }))

      const newCheckboxState = initialPermissionData.map((category) => ({
        category: category.category,
        resource: category.resources.map((resource) => ({
          full: false,
          managed: false,
          self: false,
          none: false,
          [PermissionsNum[resource.permissions].toLowerCase()]: true,
        })),
      }))

      setPermissionState(newPermissionState)
      setCheckboxState(newCheckboxState)
    } catch (error) {
      console.error('onPermissionAction error', error)
    }
  }

  useEffect(() => {
    onPermissionAction()
    // Initialize all categories as expanded by default
    const initialExpandedState = initialPermissionData.reduce((acc, category) => {
      acc[category.category] = true
      return acc
    }, {} as { [key: string]: boolean })
    setExpandedCategories(initialExpandedState)
  }, [initialPermissionData])

  return (
    <Styled.CreatePositionPopUpContainer>
      <Formik
        initialValues={initialValues}
        onSubmit={handleSubmit}
        validationSchema={PositionCreationFormSchema}
        validateOnChange={true}
        validateOnBlur={false}
      >
        {({ touched, errors, resetForm }) => (
          <>
            <Styled.ModalHeaderContainer>
              <SharedStyled.FlexRow>
                <img src={UnitSvg} alt="modal icon" />
                <SharedStyled.FlexCol>
                  <Styled.ModalHeader>Create Position</Styled.ModalHeader>
                </SharedStyled.FlexCol>
              </SharedStyled.FlexRow>
              <Styled.CrossContainer
                onClick={() => {
                  setShowCreatePositionPopUp(false)
                  setLoading(false)
                  resetForm()
                }}
              >
                <CrossIcon />
              </Styled.CrossContainer>
            </Styled.ModalHeaderContainer>
            <SharedStyled.SettingModalContentContainer>
              <Form className="form">
                <SharedStyled.Content maxWidth="706px" width="100%" disableBoxShadow={true} noPadding={true}>
                  <InputWithValidation
                    labelName="Position Name"
                    stateName="positionName"
                    error={touched.positionName && errors.positionName ? true : false}
                    passRef={inputRef}
                  />
                  <InputWithValidation
                    labelName="Description"
                    stateName="description"
                    error={touched.description && errors.description ? true : false}
                  />
                  <br />

                  <Styled.FieldHeader marginTop="8px">Permissions:</Styled.FieldHeader>

                  <Styled.TableSpacing>
                    <Table
                      columns={columns}
                      data={data}
                      loading={loading}
                      pageCount={pageCount}
                      fetchData={fetchData}
                      noLink={true}
                      noPagination={true}
                      noSearch={true}
                      minWidth="100%"
                    />
                  </Styled.TableSpacing>

                  <SharedStyled.ButtonContainer marginTop="20px">
                    <Button type="submit" isLoading={loading}>
                      Create Position
                    </Button>
                  </SharedStyled.ButtonContainer>
                </SharedStyled.Content>
              </Form>
            </SharedStyled.SettingModalContentContainer>
          </>
        )}
      </Formik>
    </Styled.CreatePositionPopUpContainer>
  )
}
