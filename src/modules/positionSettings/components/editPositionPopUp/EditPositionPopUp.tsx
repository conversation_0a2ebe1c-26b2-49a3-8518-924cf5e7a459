import { Field, Form, Formik } from 'formik'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate, useParams } from 'react-router-dom'
import * as Yup from 'yup'
import UnitSvg from '../../../../assets/newIcons/unitModal.svg'

import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import { RevokeIcon } from '../../../../assets/icons/RevokeIcon'
import { updatePosition } from '../../../../logic/apis/position'
import { Permissions, PermissionsNum, StorageKey } from '../../../../shared/helpers/constants'
import { getDataFromLocalStorage, isSuccess, notify } from '../../../../shared/helpers/util'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import { Table } from '../../../../shared/table/Table'
import * as SharedStyled from '../../../../styles/styled'
import { ModalHeaderInfo } from '../../../units/components/newUnitModal/style'
import * as Styled from './style'
import Button from '../../../../shared/components/button/Button'
import { CategoryHeader } from '../createPositionPopUp/style'
import { PermissionCategory } from '../createPositionPopUp/CreatePositionPopUp'

/**
 * InitialValues is an interface declared here so that it can be used as a type for the useState hook
 */
interface InitialValues {
  positionName: string
  description: string
  // default: boolean
}

function isReadOnly(permissions: any) {
  try {
    const { read, write } = permissions
    return read === true && write === false
  } catch (error) {
    console.error('isReadOnly error', error)
  }
}

/**
 * I_EditPositionPopUp is an interface which defines the props type which we receive from our parent component to this component
 */
interface I_EditPositionPopUp {
  setShowEditPositionPopUp: React.Dispatch<React.SetStateAction<boolean>>
  setDetailsUpdate: React.Dispatch<React.SetStateAction<boolean>>
  positionData: any
}

export const EditPositionPopUp = (props: I_EditPositionPopUp) => {
  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */
  const { setShowEditPositionPopUp, setDetailsUpdate, positionData } = props
  const [expandedCategories, setExpandedCategories] = useState<{ [key: string]: boolean }>({})
  const [initialValues, setInitialValues] = useState<InitialValues>({
    positionName: '',
    description: '',
    // default: positionData?.editable,
  })

  const [permissionState, setPermissionState] = useState<PermissionCategory[]>([])
  const [checkboxState, setCheckboxState] = useState<any>([])

  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [inputRef])

  // const [dummyData, setDummyData] = useState<any>([
  //   {
  //     permissionName: 'Team',
  //     full: <Styled.CheckBox width="15px" height="20px" type="checkbox" />,
  //     managed: <Styled.CheckBox width="15px" height="20px" type="checkbox" />,
  //     self: <Styled.CheckBox width="15px" height="20px" type="checkbox" />,
  //     none: <Styled.CheckBox width="15px" height="20px" type="checkbox" />,
  //   },
  //   {
  //     permissionName: 'Crew',
  //     full: <Styled.CheckBox width="15px" height="20px" type="checkbox" />,
  //     managed: <Styled.CheckBox width="15px" height="20px" type="checkbox" />,
  //     self: <Styled.CheckBox width="15px" height="20px" type="checkbox" />,
  //     none: <Styled.CheckBox width="15px" height="20px" type="checkbox" />,
  //   },
  //   {
  //     permissionName: 'TimeCard',
  //     full: <Styled.CheckBox width="15px" height="20px" type="checkbox" />,
  //     managed: <Styled.CheckBox width="15px" height="20px" type="checkbox" />,
  //     self: <Styled.CheckBox width="15px" height="20px" type="checkbox" />,
  //     none: <Styled.CheckBox width="15px" height="20px" type="checkbox" />,
  //   },
  // ])
  const [loading, setLoading] = useState<boolean>(false)

  const [pageCount, setPageCount] = useState<number>(10)

  const [data, setData] = useState<any>([])
  const fetchIdRef = useRef(0)

  const navigate = useNavigate()

  /**
   * Destructuring the values from the props received
   */

  const onReadOnlyChange = (categoryIndex: number, resourceIndex: number, readOnly: boolean) => {
    const newPermissionState = [...permissionState]
    const newCheckboxState = [...checkboxState]

    if (readOnly) {
      newPermissionState[categoryIndex].resource[resourceIndex].crud = {
        read: true,
        write: false,
        // update: false,
        // delete: false,
      }

      // Update checkbox state
      newCheckboxState[categoryIndex].resource[resourceIndex].crud = {
        read: true,
        write: false,
        // update: false,
        // delete: false,
      }
    } else {
      newPermissionState[categoryIndex].resource[resourceIndex].crud = {
        read: true,
        write: true,
        // update: true,
        // delete: true,
      }

      // Update checkbox state
      newCheckboxState[categoryIndex].resource[resourceIndex].crud = {
        read: true,
        write: true,
        // update: true,
        // delete: true,
      }
    }

    setPermissionState(newPermissionState)
    setCheckboxState(newCheckboxState)
  }

  // const onPermissionChange = (name: string, index: number, checked: boolean) => {
  //   try {
  //     let permissionData: any = [...permissionState]
  //     let checkBoxData: any = [...checkboxState]
  //     permissionData.forEach((perm: any, index1: number) => {
  //       if (index1 === index) {
  //         perm.permissions = [Permissions[name]]
  //       }
  //     })
  //     checkBoxData.forEach((check: any, index1: number) => {
  //       if (index1 === index) {
  //         checkBoxData[index1] = {
  //           full: false,
  //           managed: false,
  //           self: false,
  //           none: false,
  //           [name.toLowerCase()]: checked,
  //         }
  //       }
  //     })
  //     setPermissionState(permissionData)
  //     setCheckboxState(checkBoxData)
  //   } catch (error) {
  //     console.error('onPermissionChange error', error)
  //   }
  // }

  const onPermissionChange = (categoryIndex: number, resourceIndex: number, name: string, checked: boolean) => {
    try {
      const newPermissionState = [...permissionState]
      const newCheckboxState = [...checkboxState]

      if (name === 'None') {
        newPermissionState[categoryIndex].resources[resourceIndex].crud = {
          read: false,
          write: false,
          // update: false,
          // delete: false,
        }

        // Update checkbox state
        newCheckboxState[categoryIndex].resources[resourceIndex].crud = {
          read: false,
          write: false,
          // update: false,
          // delete: false,
        }
      }
      if (name === 'Self' || name === 'Managed' || name === 'Full') {
        newPermissionState[categoryIndex].resources[resourceIndex].crud = {
          read: true,
          write: true,
        }

        newCheckboxState[categoryIndex].resources[resourceIndex].crud = {
          read: true,
          write: true,
        }
      }

      // Update permission state
      newPermissionState[categoryIndex].resources[resourceIndex].permissions = Permissions[name]

      // Update checkbox state
      newCheckboxState[categoryIndex].resources[resourceIndex] = {
        // full: false,
        // managed: false,
        // self: false,
        // none: false,
        ...newCheckboxState[categoryIndex].resources[resourceIndex],
        permissions: Permissions[name],
        // [name.toLowerCase()]: checked,
      }

      setPermissionState(newPermissionState)
      setCheckboxState(newCheckboxState)
    } catch (error) {
      console.error('onPermissionChange error', error)
    }
  }

  const fetchData = useCallback(
    async ({ pageSize, pageIndex }: any) => {
      try {
        // This will get called when the table needs new data

        let receivedData: any = []

        // console.log('currentCompany', currentCompany)
        // let currentCompanyData: any = localStorage.getItem('currentCompany')

        // const statusResponse = await getTeamMembers(
        //   { skip: pageIndex, limit: pageSize, deleted: false, companyId: JSON.parse(currentCompanyData)._id },
        //   id
        // )

        // if (isSuccess(statusResponse)) {
        //   let statusRes = statusResponse?.data?.data?.memberData
        //   statusRes.forEach((res: any, index: number) => {
        //     receivedData.push({
        //       name: res.name,
        //       username: res.username,
        //       email: res.email,
        //       id: res.user,
        //       memberId: res._id,
        //       managerId: res?.managerId ? res.managerId : '0',
        //       position: res?.position ? res?.position : '0',
        //     })
        //   })
        //   console.log('receivedData', receivedData)
        // } else {
        //   notify(statusResponse?.data?.message, 'error')
        // }

        if (positionData?.permissions) {
          positionData?.permissions.forEach((category, categoryIndex) => {
            // Add category header row
            receivedData.push({
              isCategoryHeader: true,
              category: category.category,
              categoryIndex: categoryIndex,
            })

            // Add resource rows if category is expanded
            if (expandedCategories[category.category] !== false) {
              category.resources.forEach((resource, resourceIndex) => {
                receivedData.push({
                  permissionName: <Styled.NameText>{resource.name}</Styled.NameText>,
                  full: (
                    <select
                      onChange={(e) => {
                        onPermissionChange(categoryIndex, resourceIndex, e.target.value, true)
                      }}
                    >
                      <option value="Full" selected={resource?.permissions === 1 || false}>
                        Full
                      </option>
                      <option value="Managed" selected={resource?.permissions === 2 || false}>
                        Managed
                      </option>
                      <option value="Self" selected={resource?.permissions === 3 || false}>
                        Self
                      </option>
                      <option value="None" selected={resource?.permissions === 4 || false}>
                        None
                      </option>
                    </select>
                  ),
                  managed: (
                    <SharedStyled.FlexRow>
                      <Styled.CheckBox
                        width="15px"
                        height="20px"
                        type="checkbox"
                        id={`checkbox-${categoryIndex}-${resourceIndex}`}
                        checked={isReadOnly(checkboxState[categoryIndex]?.resource[resourceIndex]?.crud) || false}
                        onChange={(e: any) => {
                          onReadOnlyChange(categoryIndex, resourceIndex, e.target.checked)
                        }}
                      />
                      <label htmlFor={`checkbox-${categoryIndex}-${resourceIndex}`} style={{ color: 'black' }}>
                        Read-only
                      </label>
                    </SharedStyled.FlexRow>
                  ),
                  // self: (
                  //   <SharedStyled.CheckboxZoneLabel>
                  //     <Styled.CheckBox
                  //       width="15px"
                  //       height="20px"
                  //       type="checkbox"
                  //       checked={checkboxState[categoryIndex]?.resource[resourceIndex]?.self || false}
                  //       onChange={(e: any) => {
                  //         onPermissionChange(categoryIndex, resourceIndex, 'Self', e.target.checked)
                  //       }}
                  //     />
                  //   </SharedStyled.CheckboxZoneLabel>
                  // ),
                  // none: (
                  //   <SharedStyled.FlexRow>
                  //     <Styled.CheckBox width="15px" height="20px" type="checkbox" id={`checkbox-${resourceIndex}`} />
                  //     <label htmlFor={`checkbox-${resourceIndex}`} style={{ color: 'black' }}>
                  //       Read-only
                  //     </label>
                  //   </SharedStyled.FlexRow>
                  // ),
                })
              })
            }
          })
        }
        // Give this fetch an ID
        // const fetchId = ++fetchIdRef.current

        // Set the loading state
        // setLoading(true)

        // We'll even set a delay to simulate a server here
        // setTimeout(() => {
        // Only update the data if this is the latest fetch

        setData(receivedData)
        // }, 1000)
      } catch (error) {
        console.error('TeamTable fetchData error', error)
      }
    },
    [positionData, checkboxState, expandedCategories]
  )

  const columns: any = useMemo(
    () => [
      {
        Header: '',
        accessor: 'permissionName',
        Cell: ({ row }: any) => {
          if (row.original.isCategoryHeader) {
            return (
              <CategoryHeader onClick={() => toggleCategoryExpand(row.original.category)}>
                {row.original.category.charAt(0).toUpperCase() + row.original.category.slice(1)}
                <span>{expandedCategories[row.original.category] === false ? <>&#9654;</> : <>&#9660;</>}</span>
              </CategoryHeader>
            )
          }
          return row.original.permissionName
        },
      },
      {
        Header: '',
        accessor: 'full',
      },
      {
        Header: '',
        accessor: 'managed',
      },
      // {
      //   Header: '',
      //   accessor: 'self',
      // },
      // {
      //   Header: '',
      //   accessor: 'none',
      // },
    ],
    [expandedCategories]
  )

  const toggleCategoryExpand = (category: string) => {
    setExpandedCategories((prev) => ({
      ...prev,
      [category]: !prev[category],
    }))
  }

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company

  /**
   * PositionEditFormSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */
  const PositionEditFormSchema = Yup.object().shape({
    positionName: Yup.string().required('No position name provided.'),
    // description: Yup.string().required('No description provided.'),
  })

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async (submittedValues: InitialValues, { resetForm }: any) => {
    try {
      // if (Object.keys(currentCompany).length > 0 && Object.keys(currentMember).length > 0) {
      setLoading(true)
      const permissionData = permissionState.map((category: any) => ({
        category: category.category,
        resources: category.resource,
      }))

      let dataObj = {
        id: positionData.id,
        position: submittedValues.positionName,
        description: submittedValues.description,
        createdBy: currentMember._id,
        permissions: permissionData,
        // editable: submittedValues.default,
      }

      let response = await updatePosition(dataObj)
      if (isSuccess(response)) {
        notify('Position Edited Successfully', 'success')
        resetForm()
        setDetailsUpdate((prev) => !prev)
        setLoading(false)
        setShowEditPositionPopUp(false)
      } else {
        setLoading(false)
        notify(response?.data?.message, 'error')
      }
      // }
    } catch (error) {
      console.error('Position Editing error', error)
    }
  }

  const setInitialValuesFunc = () => {
    try {
      setInitialValues({
        ...initialValues,
        positionName: positionData.positionName,
        description: positionData.description,
      })
    } catch (error) {
      console.log('Edit position setInitialValues error', error)
    }
  }

  const onPermissionAction = () => {
    try {
      let permissionData: any = []
      let checkBoxData: any = []
      positionData.permissions.map((permission: any) => {
        permissionData.push({ ...permission, resource: permission?.resources }),
          // permission?.resource?.map((res: any) => {
          checkBoxData.push({
            ...permission,
            resource: permission?.resources,
          })
        // })
        // checkBoxData.push({
        //   full: false,
        //   managed: false,
        //   self: false,
        //   none: false,
        //   [PermissionsNum[permission.permissions[0]].toLowerCase()]: true,
        // })
      })
      setPermissionState(permissionData)
      setCheckboxState(checkBoxData)
    } catch (error) {
      console.error('onPermissionAction error', error)
    }
  }

  useEffect(() => {
    setInitialValuesFunc()
    onPermissionAction()
  }, [positionData])

  return (
    <Styled.EditPositionPopUpContainer>
      {' '}
      <Formik
        initialValues={initialValues}
        onSubmit={handleSubmit}
        validationSchema={PositionEditFormSchema}
        validateOnChange={true}
        validateOnBlur={false}
        enableReinitialize={true}
      >
        {({ touched, errors, values, resetForm }) => {
          return (
            <>
              <Styled.ModalHeaderContainer>
                <SharedStyled.FlexRow>
                  <img src={UnitSvg} alt="modal icon" />
                  <SharedStyled.FlexCol>
                    <Styled.ModalHeader>Edit Position</Styled.ModalHeader>
                  </SharedStyled.FlexCol>
                </SharedStyled.FlexRow>
                <Styled.CrossContainer
                  onClick={() => {
                    setShowEditPositionPopUp(false)
                    setLoading(false)
                    resetForm()
                  }}
                >
                  <CrossIcon />
                </Styled.CrossContainer>
              </Styled.ModalHeaderContainer>
              <SharedStyled.SettingModalContentContainer>
                <Form className="form">
                  <SharedStyled.Content maxWidth="706px" width="100%" disableBoxShadow={true} noPadding={true}>
                    <InputWithValidation
                      labelName="Position Name"
                      stateName="positionName"
                      error={touched.positionName && errors.positionName ? true : false}
                      passRef={inputRef}
                    />
                    <InputWithValidation
                      labelName="Description"
                      stateName="description"
                      error={touched.description && errors.description ? true : false}
                    />

                    <br />
                    {/* <SharedStyled.FlexBox margin="0 auto 0 0">
                      <Field type="checkbox" name="default" checked={values.default} />
                      <SharedStyled.Text fontSize="14px">
                        <b>Is Editable</b>
                      </SharedStyled.Text>
                    </SharedStyled.FlexBox> */}
                    {/* <Field type="checkbox" name="default" checked={values.default} />
                    <SharedStyled.Text fontSize="14px">
                      Is Editable
                    </SharedStyled.Text> */}
                    <Styled.FieldHeader marginTop="8px" style={{ marginBottom: '8px' }}>
                      Permissions:
                    </Styled.FieldHeader>
                    <Styled.TableSpacing>
                      <Table
                        columns={columns}
                        data={data}
                        loading={loading}
                        pageCount={pageCount}
                        fetchData={fetchData}
                        noLink={true}
                        noPagination={true}
                        noSearch={true}
                        minWidth="100%"
                        pageSizes={100}
                      />
                    </Styled.TableSpacing>
                    <SharedStyled.ButtonContainer marginTop="20px">
                      <Button type="submit" isLoading={loading}>
                        Save Changes
                      </Button>
                    </SharedStyled.ButtonContainer>
                  </SharedStyled.Content>
                </Form>
              </SharedStyled.SettingModalContentContainer>
            </>
          )
        }}
      </Formik>
    </Styled.EditPositionPopUpContainer>
  )
}
