import { useState } from 'react'
import { useSelector } from 'react-redux'
import { useParams } from 'react-router-dom'

import UnitSvg from '../../../../assets/newIcons/unitModal.svg'
import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import { deletePosition, restorePosition } from '../../../../logic/apis/position'
import { getDataFromLocalStorage, isSuccess, notify } from '../../../../shared/helpers/util'
import * as SharedStyled from '../../../../styles/styled'
import { colors } from '../../../../styles/theme'
import { ModalHeaderInfo } from '../../../units/components/newUnitModal/style'
import * as Styled from './style'
import Button from '../../../../shared/components/button/Button'
import { StorageKey } from '../../../../shared/helpers/constants'

interface I_ConfirmationPopUp {
  setShowConfirmationPopUp: React.Dispatch<React.SetStateAction<boolean>>
  setDetailsUpdate: React.Dispatch<React.SetStateAction<boolean>>
  positionData: any
  header: string
}

export const ConfirmationPopUp = (props: I_ConfirmationPopUp) => {
  const { setShowConfirmationPopUp, setDetailsUpdate, header, positionData } = props

  /**
   * loading will be the loading state when performing the operations
   */
  const [loading, setLoading] = useState<boolean>(false)

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany } = globalSelector.company

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async () => {
    try {
      // if (Object.keys(currentCompany).length > 0) {
      setLoading(true)

      let dataObj = {
        positionId: positionData.id,
      }

      if (header === 'Delete Position') {
        let response = await deletePosition(dataObj)

        if (isSuccess(response)) {
          notify('Position Deleted Successfully', 'success')
          setDetailsUpdate((prev) => !prev)
          setLoading(false)
          setShowConfirmationPopUp(false)
        } else {
          setLoading(false)
          notify(response?.data?.message, 'error')
        }
      } else {
        let response = await restorePosition(dataObj)

        if (isSuccess(response)) {
          notify('Position Restored Successfully', 'success')
          setDetailsUpdate((prev) => !prev)
          setLoading(false)
          setShowConfirmationPopUp(false)
        } else {
          setLoading(false)
          notify(response?.data?.message, 'error')
        }
      }
      // } else {
      //   notify('Integration is pending', 'warning')
      // }
    } catch (error) {
      console.error('Delete Position handleSubmit', error)
      setLoading(false)
    }
  }

  return (
    <Styled.ConfirmationContainer>
      {' '}
      <Styled.ModalHeaderContainer>
        <SharedStyled.FlexRow>
          <img src={UnitSvg} alt="modal icon" />
          <SharedStyled.FlexCol>
            <Styled.ModalHeader>{header}</Styled.ModalHeader>
          </SharedStyled.FlexCol>
        </SharedStyled.FlexRow>
        <Styled.CrossContainer onClick={() => setShowConfirmationPopUp(false)}>
          <CrossIcon />
        </Styled.CrossContainer>
      </Styled.ModalHeaderContainer>
      <Styled.ModalBodyContainer>
        <Styled.ModalDescription>
          {header === 'Delete Position'
            ? 'Are you sure you want to delete this Position ?'
            : 'Are you sure you want to restore this Position ?'}
        </Styled.ModalDescription>
        <SharedStyled.FlexBox
          width="100%"
          alignItems="center"
          gap="14px"
          justifyContent="space-around"
          marginTop="20px"
        >
          <Button
            type="submit"
            bgColor={header === 'Delete Position' ? colors.error : colors.blueLight}
            color={colors.white}
            onClick={() => handleSubmit()}
            isLoading={loading}
          >
            Yes
          </Button>
          <Button type="submit" onClick={() => setShowConfirmationPopUp(false)}>
            No
          </Button>
        </SharedStyled.FlexBox>
      </Styled.ModalBodyContainer>
    </Styled.ConfirmationContainer>
  )
}
