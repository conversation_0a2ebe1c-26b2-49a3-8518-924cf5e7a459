import React, { useC<PERSON>back, useMemo, useRef, useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'

import * as Styled from './style'
import * as SharedStyled from '../../../../styles/styled'
import { Table } from '../../../../shared/table/Table'
import { notify } from '../../../../shared/helpers/util'
import { colors } from '../../../../styles/theme'

const Permission = () => {
  interface I_Data {
    positionName: string
    full: any
    managed: any
    self: any
  }

  const navigate = useNavigate()

  const [dummyData, setDummyData] = useState<any>([
    {
      permissionName: 'Team',
      full: <Styled.CheckBox width="15px" height="20px" type="checkbox" />,
      managed: <Styled.CheckBox width="15px" height="20px" type="checkbox" />,
      self: <Styled.CheckBox width="15px" height="20px" type="checkbox" />,
    },
    {
      permissionName: 'Crew',
      full: <Styled.CheckBox width="15px" height="20px" type="checkbox" />,
      managed: <Styled.CheckBox width="15px" height="20px" type="checkbox" />,
      self: <Styled.CheckBox width="15px" height="20px" type="checkbox" />,
    },
    {
      permissionName: 'TimeCard',
      full: <Styled.CheckBox width="15px" height="20px" type="checkbox" />,
      managed: <Styled.CheckBox width="15px" height="20px" type="checkbox" />,
      self: <Styled.CheckBox width="15px" height="20px" type="checkbox" />,
    },
  ])
  const [loading, setLoading] = useState<boolean>(false)
  const [showConfirmationPopUp, setShowConfirmationPopUp] = useState<boolean>(false)
  const [showCreatePositionPopUp, setShowCreatePositionPopUp] = useState<boolean>(false)
  const [showEditPositionPopUp, setShowEditPositionPopUp] = useState<boolean>(false)
  const [positionData, setPositionData] = useState<any>({})
  const [detailsUpdate, setDetailsUpdate] = useState(false)
  const [pageCount, setPageCount] = useState<number>(10)
  const [data, setData] = useState<I_Data[]>([])
  const fetchIdRef = useRef(0)

  const fetchData = useCallback(
    async ({ pageSize, pageIndex }: any) => {
      try {
        // This will get called when the table needs new data

        // let receivedData: any = []
        // console.log('currentCompany', currentCompany)
        // let currentCompanyData: any = localStorage.getItem('currentCompany')

        // const statusResponse = await getTeamMembers(
        //   { skip: pageIndex, limit: pageSize, deleted: false, companyId: JSON.parse(currentCompanyData)._id },
        //   id
        // )

        // if (isSuccess(statusResponse)) {
        //   let statusRes = statusResponse?.data?.data?.memberData
        //   statusRes.forEach((res: any, index: number) => {
        //     receivedData.push({
        //       name: res.name,
        //       username: res.username,
        //       email: res.email,
        //       id: res.user,
        //       memberId: res._id,
        //       managerId: res?.managerId ? res.managerId : '0',
        //       position: res?.position ? res?.position : '0',
        //     })
        //   })
        //   console.log('receivedData', receivedData)
        // } else {
        //   notify(statusResponse?.data?.message, 'error')
        // }

        // Give this fetch an ID
        const fetchId = ++fetchIdRef.current

        // Set the loading state
        // setLoading(true)

        // We'll even set a delay to simulate a server here
        setTimeout(() => {
          // Only update the data if this is the latest fetch
          if (fetchId === fetchIdRef.current) {
            const startRow = pageSize * pageIndex
            const endRow = startRow + pageSize
            setData(dummyData.slice(startRow, endRow))

            // Your server could send back total page count.
            // For now we'll just fake it, too
            setPageCount(Math.ceil(dummyData.length / pageSize))
            // setLoading(false)
          }
        }, 1000)
      } catch (error) {
        console.error('TeamTable fetchData error', error)
      }
    },
    [detailsUpdate]
  )

  const columns: any = useMemo(
    () => [
      {
        Header: 'Permission Name',
        accessor: 'permissionName',
      },
      {
        Header: 'Full',
        accessor: 'full',
      },
      {
        Header: 'Managed',
        accessor: 'managed',
      },
      {
        Header: 'Self',
        accessor: 'self',
      },
    ],
    []
  )

  const onSave = () => {
    try {
      notify('Integration is pending', 'warning')
    } catch (error) {
      console.error('onSave error', error)
    }
  }

  return (
    <Styled.PermissionContainer>
      <SharedStyled.ContentHeader textAlign="left">Permission</SharedStyled.ContentHeader>
      <SharedStyled.HorizontalDivider />
      <Table
        columns={columns}
        data={data}
        loading={loading}
        pageCount={pageCount}
        fetchData={fetchData}
        noLink={true}
      />
      <SharedStyled.FlexBox width="100%" marginTop="20px" gap="15px" alignItems="center">
        <SharedStyled.Button type="button" maxWidth="200px" onClick={() => navigate(`/settings/position`)}>
          Back
        </SharedStyled.Button>
        <SharedStyled.Button type="button" maxWidth="200px" bgColor={colors.blueLight} onClick={() => onSave()}>
          Save
        </SharedStyled.Button>
      </SharedStyled.FlexBox>
    </Styled.PermissionContainer>
  )
}

export default Permission
