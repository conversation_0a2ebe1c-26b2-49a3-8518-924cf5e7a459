import { formatNumberToCommaS } from '../../shared/helpers/util'

interface MetricConfig {
  label: string
  value: any
  isRounded?: boolean
  isCurrency?: boolean
  suffix?: string
}

export const hideDollarValue = (value: string | number, visibility: boolean) => {
  return !visibility ? value : '--'
}

export const isDateGreaterThanToday = (dateStr: string) => {
  const inputDate = new Date(dateStr)

  const today = new Date()
  today.setHours(0, 0, 0, 0)

  return inputDate > today
}

export const getColumns = (isPrivacy: boolean) => {
  return [
    {
      Header: 'PO#',
      accessor: 'oppPO',
      Cell: ({ value }: { value: string }) => (value ? value : '---'),
    },
    {
      Header: 'Budget',
      accessor: 'budget',
      Cell: ({ value }: { value: number }) =>
        value ? `$${hideDollarValue(formatNumberToCommaS(value), isPrivacy)}` : '---',
    },
    {
      Header: 'RR',
      accessor: 'rr',
      Cell: ({ value }: { value: number }) =>
        value ? `$${hideDollarValue(formatNumberToCommaS(value), isPrivacy)}` : '---',
    },
    {
      Header: 'Vol',
      accessor: 'vol',
      Cell: ({ value }: { value: number }) =>
        value ? `$${hideDollarValue(formatNumberToCommaS(value), isPrivacy)}` : '---',
    },

    {
      Header: 'Extra Hours',
      accessor: 'totalExtraHours',
      Cell: ({ value }: { value: number }) => (value ? value?.toFixed(2) : '---'),
    },
    {
      Header: 'Sheets',
      accessor: 'totalPlywood',
      Cell: ({ value }: { value: number }) => (value ? value?.toFixed(2) : '---'),
    },
    {
      Header: 'Roofing',
      accessor: 'roofingDone',
      Cell: ({ value }: { value: number }) => (value ? `${value?.toFixed(2)} SQ` : '---'),
    },
    {
      Header: 'Tear off',
      accessor: 'tearOffDone',
      Cell: ({ value }: { value: number }) => (value ? `${value?.toFixed(2)} SQ` : '---'),
    },
    {
      Header: '% Done',
      accessor: 'percentDone',
      Cell: ({ value }: { value: number }) => (value ? (value * 100)?.toFixed(2) : '---'),
    },
  ]
}

export const getCrewMetricConfigs = (crew: any): MetricConfig[] => [
  { label: 'Labor Budget', value: crew?.logStats?.dayBudget },
  { label: 'Labor Actual', value: crew?.paid },
  { label: 'Real Revenue', value: crew?.logStats?.dayRR },
  { label: 'Volume', value: crew?.logStats?.dayVol },
  { label: 'RR / $', value: crew?.rrDollar, isRounded: false },
  { label: 'Crew Working', value: crew?.numWorking, isCurrency: false },
  { label: 'Time Worked', value: crew?.timeWorked, isRounded: false, isCurrency: false, suffix: 'hrs' },
]

export const getNonCrewMetricConfigs = (detailsForNonCrew: any): MetricConfig[] => [
  { label: 'Num Working', value: detailsForNonCrew?.numWorking, isCurrency: false },
  { label: 'Paid', value: detailsForNonCrew?.paid },
  { label: 'Time Worked', value: detailsForNonCrew?.timeWorked, suffix: 'hrs', isCurrency: false, isRounded: false },
  { label: 'RR / $', value: detailsForNonCrew?.rrDollar, isRounded: false },
]
