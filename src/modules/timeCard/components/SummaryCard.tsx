import { SLoader } from '../../../shared/components/loader/Loader'
import { hideDollarValue } from '../timecardUtils'
import { MiniContainer, MiniContainerHeader, MiniContainerValue } from './approveTimeCards/style'

const SummaryCard = ({
  label,
  value,
  isCurrency = true,
  isPrivacy,
  dailyLogsLoading,
  suffix,
  isRounded = true,
}: {
  label: string
  value: number | string
  isCurrency?: boolean
  isPrivacy?: boolean
  dailyLogsLoading?: boolean
  suffix?: string
  isRounded?: boolean
}) => {
  return value ? (
    <MiniContainer>
      <MiniContainerHeader>{label}</MiniContainerHeader>
      <MiniContainerValue>
        {dailyLogsLoading ? (
          <SLoader height={30} />
        ) : (
          <>
            {isCurrency
              ? `$${hideDollarValue(
                  isRounded ? Math.round(Number(Number(value).toFixed(2)) || 0) : Number(Number(value).toFixed(2)) || 0,
                  isPrivacy!
                )}`
              : `${isRounded ? Math.round(Number(Number(value).toFixed(2)) || 0) : Number(value).toFixed(2)}${
                  suffix ? suffix : ''
                }`}
          </>
        )}
      </MiniContainerValue>
    </MiniContainer>
  ) : null
}

export default SummaryCard
