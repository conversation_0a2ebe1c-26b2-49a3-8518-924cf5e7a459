import styled, { css } from 'styled-components'
import { colors, screenSizes } from '../../../../styles/theme'
import { Nue } from '../../../../shared/helpers/constants'
import { getTimeCardColor } from '../../../../shared/helpers/util'
import { FlexCol } from '../../../../styles/styled'

export const PickTheDayContainer = styled.div<any>`
  max-width: ${(props) => (props.maxWidth ? props.maxWidth : '380px')};
  width: 100%;
  display: flex;
  align-items: flex-end;
  gap: 5px;
  @media (max-width: ${screenSizes.S}px) {
    flex-wrap: wrap;
  }
`

export const CurrentDayDetail = styled.p<any>`
  width: 100%;
  text-align: center;
  margin: 0;
  font-size: 20px;
  color: ${colors.text};
  font-family: ${Nue.medium};
  margin-top: ${(props) => props.marginTop};
  @media (min-width: ${screenSizes.M}px) {
    font-size: 28px;
  }
`

export const PrivacyContainer = styled.div<any>`
  display: flex;
  justify-content: flex-start;
  gap: 5px;
  align-items: center;
  margin-top: ${(props) => props.marginTop};
`

export const PrivacyText = styled.p`
  margin: 0;
  font-size: 14px;
`

export const SwitchDiv = styled.label`
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
  input {
    opacity: 0;
    width: 0;
    height: 0;
  }
  .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: 0.4s;
    transition: 0.4s;
  }
  .slider:before {
    position: absolute;
    content: '';
    height: 12px;
    width: 12px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    -webkit-transition: 0.4s;
    transition: 0.4s;
  }
  input:checked + .slider {
    background-color: ${colors.darkGrey};
  }
  input:focus + .slider {
    box-shadow: 0 0 1px ${colors.darkGrey};
  }
  input:checked + .slider:before {
    -webkit-transform: translateX(20px);
    -ms-transform: translateX(20px);
    transform: translateX(20px);
  }
  /* Rounded sliders */
  .slider.round {
    border-radius: 24px;
  }
  .slider.round:before {
    border-radius: 50%;
  }
`

export const CheckboxInput = styled.input``

export const SliderRound = styled.span``

export const CrewDetailContainer = styled.div<any>`
  margin-top: ${(props) => props.marginTop};
  width: 100%;
`

export const CrewNameText = styled.p<any>`
  width: 100%;
  text-align: start;
  margin: 0;
  font-size: 20px;
  font-family: ${Nue.medium};
  margin-top: ${(props) => props.marginTop};
  @media (min-width: ${screenSizes.M}px) {
    font-size: 24px;
  }
`

export const DailyLogContainer = styled.div<any>`
  width: 100%;
  display: grid;
  grid-template-columns: 1fr;
  padding: 10px;
  background-color: ${colors.lightGrey10};
  border: 1px solid ${colors.lightGrey8};
  border-radius: 5px;
  margin-top: ${(props) => props.marginTop};
  margin-bottom: 14px;

  table {
    td:not(:first-child),
    th:not(:first-child) {
      text-align: right;
    }
  }
`

export const MetricsContainer = styled(DailyLogContainer)``

export const DailyLogHeader = styled.p<any>`
  margin: 0;
  font-size: 16px;
  font-weight: 300;
  width: 100%;
  text-align: left;
`

export const TimeCardGridContainer = styled.div<any>`
  width: 100%;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 300px));
  gap: 15px;
  margin-top: ${(props) => props.marginTop};
`

export const SingleTimeCardContainer = styled.div<any>`
  padding: 10px;
  border-radius: 4px;
  border: 1px solid ${colors.lightGrey8};
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: flex-start;
  /* background-color: ${(props) => (props.allApproved ? colors.lightGreen : colors.white)}; */

  ${(props) =>
    props.allApproved &&
    css`
      background-color: ${getTimeCardColor('Approved')};
    `}
`

export const MemberNameAddButtonContainerDiv = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`

export const MemberName = styled.p`
  margin: 0;
  font-size: 12px;
  font-family: ${Nue.medium};
  text-transform: capitalize;
  @media (min-width: ${screenSizes.M}px) {
    font-size: 18px;
  }
  white-space: nowrap;
  width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
`

export const KeyValueSpan = styled.span`
  font-size: 12px;
  font-family: ${Nue.regular};
  &.bolder {
    font-family: ${Nue.bold};
  }
  &.extra {
    font-size: 14px;
  }
  &.dollar {
    font-size: 14px;
  }
  &.header {
    font-size: 16px;
  }
  &.work {
    font-size: 14px;
  }
  &.note {
    font-size: 12px;
    font-style: italic;
  }
  &.managerNotes {
    font-size: 12px;
    font-family: ${Nue.bold};
    font-style: italic;
  }
  &.timeInOut {
    font-size: 20px;
    @media (min-width: ${screenSizes.M}px) {
      font-size: 24px;
    }
  }
  .project {
    font-family: ${Nue.medium};
    font-size: 16px;
    @media (min-width: ${screenSizes.M}px) {
      font-size: 18px;
    }
  }
`

export const TimeCardNoTimeCardDayOffButton = styled.div<any>`
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 72px;
  padding: 6px 12px;
  background-color: ${(props) => (props.isDayOff ? `${colors.errorRed}` : `${colors.yellow}`)};
  color: ${(props) => (props.isDayOff ? `${colors.white}` : `${colors.lightBlack3}`)};
  border-radius: 4px;
  cursor: pointer;
  :hover {
    transform: scale(1.02);
  }
`

export const TimeCardNoTimeCardDayOffButtonDiv = styled.div``

export const TimeCardNoTimeCardDayOffButtonText = styled.p`
  margin: 0;
  width: 100%;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: ${Nue.regular};
`

export const TimeCardApproveNoApproveRightDiv = styled.div<any>`
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 5px;
  svg {
    width: 35px;
    height: 35px;
    margin-left: 2px;
  }
  svg path {
    stroke: ${(props) => (props.isApproved ? `${colors.errorRed}` : `${colors.green}`)};
  }
`

export const PageLoadingContainer = styled.div<any>`
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: ${(props) => props.marginTop};
`

export const PageLoadingText = styled.p<any>`
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  @media (min-width: ${screenSizes.M}px) {
    font-size: 24px;
  }
  margin-right: ${(props) => props.marginRight};
`

export const OuterMiniContainer = styled.div`
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  position: relative;
`

export const MiniContainer = styled.div<any>`
  max-width: 170px;
  width: 100%;

  padding: 12px 16px;
  border: 1px solid ${colors.lightGrey8};
  background-color: ${colors.white};
  border-radius: 8px;
  box-shadow: 0px 2px 20px 0px rgba(156, 145, 145, 0.25);
  /* display: flex;
  flex-direction: column;
  align-items: center; */
  p {
    text-align: center;
  }
`

export const MiniContainerHeader = styled.p`
  width: 100%;
  margin: 0;
  font-size: 14px;
  color: ${colors.lightBlack3};
  text-align: left;
  font-family: ${Nue.regular};
`

export const MiniContainerValue = styled.p`
  width: 100%;
  margin: 0;
  font-size: 24px;
  font-family: ${Nue.bold};
  color: ${colors.lightBlack3};
  text-align: left;
  color: #121619;
`

export const DisplayProjectHeader = styled.p<any>`
  margin: 0;
  font-size: 18;
  font-weight: bold;
  margin-top: ${(props) => props.marginTop};
  cursor: pointer;
  /* text-decoration: underline;
  :hover {
    text-decoration: none;
  } */
`

export const IconContainer = styled.div``

export const EditContainer = styled.div`
  cursor: pointer;
  transition: all 0.3s ease 0s;
  /* position: absolute;
  right: 0px;
  top: 0px; */
  text-decoration: underline;
  :hover {
    text-decoration: none;
  }
`

export const WorkDoneTimeCardContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 5px;
`

export const WorkDoneTimeCardHeader = styled.p`
  margin: 0;
  font-size: 14px;
  font-family: ${Nue.regular};
`

export const KeyValuePair = styled.p`
  margin: 0;
  font-size: 14px;
`
//table
export const TableContainer = styled.div`
  padding: 20px 0;
`

export const TableHeading = styled.div`
  display: grid;
  grid-template-columns: repeat(9, 1fr);
  border: 1px solid #000;
  padding: 5px 0;
`
export const TableTitle = styled.div`
  font-size: 18px;
  font-weight: 600;
`

export const TableContent = styled.div`
  display: grid;
  grid-template-columns: repeat(9, 1fr);
  padding: 5px 0;
`
export const CrewReportTableContentLabel = styled.div`
  /* ... */
`

export const EditTimeOffCont = styled(FlexCol)`
  gap: 10px;
  .pto-date {
    width: 100%;
    input {
      background: #e9e7e5;
    }
  }
`
