import dayjs from 'dayjs'
import 'dayjs/locale/en'
import { Form, Formik } from 'formik'
import { Fragment, memo, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom'
import * as Yup from 'yup'

import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import { TickmarkIcon } from '../../../../assets/icons/TickmarkIcon'
import {
  cancelDayOff,
  getCrewsToApprove,
  getNonCrewsToApprove,
  takeDayOff,
  updateTimeCardToApproveOrReject,
} from '../../../../logic/apis/approveTimeCard'
import { getDailyLog } from '../../../../logic/apis/dailyLog'
import { getSubContractorToApprove, getSubContractors } from '../../../../logic/apis/subcontractor'
import Button from '../../../../shared/components/button/Button'
import Dropdown from '../../../../shared/components/dropdown/Dropdown'
import { CustomModal } from '../../../../shared/customModal/CustomModal'
import { SharedDate } from '../../../../shared/date/SharedDate'
import {
  StorageKey,
  SubscriptionPlanType,
  TimeCardStatusEnum,
  subDailyLogKeyValue,
  timeOffPositions,
} from '../../../../shared/helpers/constants'
import { onlyMmDdYyyy } from '../../../../shared/helpers/regex'
import {
  dayjsFormat,
  getDataFromLocalStorage,
  getFormattedDate,
  getFormattedDateForTimeCard,
  getHoursAndMinutes,
  isSuccess,
  moveCrewLeadFirst,
  notify,
  startOfDate,
} from '../../../../shared/helpers/util'
import { Table as SubTable, Table } from '../../../../shared/table/Table'
import * as SharedStyled from '../../../../styles/styled'
import { colors } from '../../../../styles/theme'
import { SettingsCont } from '../../../settings/style'
import { AddTimeCardPopUp } from '../addTimeCardPopUp/AddTimeCardPopUp'
import { EditTimeCardPopUp } from '../editTimeCardPopUp/EditTimeCardPopUp'
import { SubDailyLog } from '../subDailyLog/SubDailyLog'
import * as Styled from './style'
import DailyLog from '../dailyLog/DailyLog'
import { SLoader } from '../../../../shared/components/loader/Loader'
import Timecard from '../../../../shared/timecard/Timecard'
import TimeOffRequest from '../timeOffRequest/TimeOffRequest'
import EditTimeOffRequest from '../timeOffRequest/EditTimeOffRequest'
import { getTeamMembers } from '../../../../logic/apis/team'
import {
  getColumns,
  getCrewMetricConfigs,
  getNonCrewMetricConfigs,
  hideDollarValue,
  isDateGreaterThanToday,
} from '../../timecardUtils'
import SummaryCard from '../SummaryCard'

interface InitialValues {
  timeCardDate: string
}

const ApproveTimeCards = () => {
  const [isPrivacy, setIsPrivacy] = useState<boolean>(localStorage.getItem(StorageKey.privacy) === 'true' || false)
  const [showAddTimeCardPopUp, setShowAddTimeCardPopUp] = useState<boolean>(false)
  const [showEditTimeCardPopUp, setShowEditTimeCardPopUp] = useState<boolean>(false)
  const [showSubDailyLogModal, setShowSubDailyLogModal] = useState<boolean>(false)
  const [showAddSubDailyLogModal, setShowAddSubDailyLogModal] = useState<boolean>(false)
  const [showCrewDailyLog, setShowCrewDailyLog] = useState(false)
  const [isEditDailyLog, setIsEditDailyLog] = useState(false)
  const [isSubEditDailyLog, setIsSubEditDailyLog] = useState(false)
  const [logId, setLogId] = useState('')
  const [currentDay, setCurrentDay] = useState<string>(
    getDataFromLocalStorage('currentDate') ? '' : getFormattedDateForTimeCard()
  )
  const [isLoaded, setIsLoaded] = useState(false)
  const [isAddTimeCard, setIsAddTimeCard] = useState(false)

  const [poList, setPoList] = useState([])
  const [subcontractorData, setSubcontractorData] = useState<any>([])
  const [currentDayDate, setCurrentDayDate] = useState<string>('')
  const [crewTimeCardDetails, setCrewTimeCardDetails] = useState<any>()
  const [allMembers, setAllMembers] = useState<any>([])

  const [dailyLogsLoading, setDailyLogsLoading] = useState<boolean>(false)
  const [crewTimeCardDetailsForNonCrew, setCrewTimeCardDetailsForNonCrew] = useState<any>([])
  const [detailsForNonCrew, setDetailsForNonCrew] = useState<any>([])
  const [approveRejectLoadingObj, setApproveRejectLoadingObj] = useState<any>({})
  const [dayOffLoadingObj, setDayOffLoadingObj] = useState<any>({})
  const [pageLoading, setPageLoading] = useState<boolean>(true)
  const [allApproveObj, setAllApproveObj] = useState<any>({})
  const [allNonCrewApproveObj, setAllNonCrewApproveObj] = useState<any>({})
  const [selectedCrew, setSelectedCrew] = useState('')
  const [showTimeOffModal, setShowTimeOffModal] = useState(false)
  const [showEditTimeOffModal, setShowEditTimeOffModal] = useState(false)
  const [modifiedTimecardId, setModifiedTimecardId] = useState('')

  const crewRef = useRef<AbortController>()
  const nonCrewRef = useRef<AbortController>()
  const subContractorRef = useRef<AbortController>()
  const [ptoData, setPtoData] = useState<any>()

  const [editPopUpValues, setEditPopUpValues] = useState<any>({
    name: '',
    date: '',
    project: '',
    task: '',
    allHourlyPayCheckbox: false,
    removeFromLeadBonusCheckbox: false,
    workDone: [{ pitch: '', sqs: '', layers: '' }],

    extraTime: [
      {
        hours: '0',
        minutes: '0',
      },
    ],
    notes: '',
    managerNotes: '',
    status: '',
    timeIn: '',
    timeOut: '',
  })
  // const [subDailyDataTemp, setSubDailyDataTemp] = useState([])
  const [subDailyDataDropDown, setSubDailyDataDropDown] = useState([])
  const [subDailyData, setSubDailyData] = useState({})
  const [subDailyLogId, setSubDailyLogId] = useState('')
  const [timeCardData, setTimeCardData] = useState<any>({})
  const [editDailyLogData, setEditDailyLogData] = useState<any>({})
  const [dataUpdate, setDataUpdate] = useState<boolean>(false)

  const [loading, setLoading] = useState<boolean>(false)
  const [showProjects, setShowProjects] = useState<boolean>(false)
  const [subDailyUpdate, setSubDailyUpdate] = useState<boolean>(false)

  const [initialValues] = useState<InitialValues>({
    timeCardDate: '',
  })
  const [detailsUpdate, setDetailsUpdate] = useState(false)
  const [subPageCount, setSubPageCount] = useState<number>(10)
  const [data, setData] = useState<any>([])
  const [subData, setSubData] = useState<any>([])
  const [subToApprove, setSubToApprove] = useState<any>([])
  const [crewOptions, setCrewOptions] = useState<{ label: string; value: string | Record<string, unknown> }[]>()
  const [currentCrew, setCurrentCrew] = useSearchParams('')
  const [isTimeCardPath, setIsTimeCardPath] = useState(false)
  const navigate = useNavigate()

  const subFetchIdRef = useRef(0)

  const { pathname } = useLocation()

  const ApproveTimeCardsFormSchema = Yup.object().shape({
    timeCardDate: Yup.string(),
    date: Yup.string().matches(onlyMmDdYyyy, 'Enter the date in MM/DD/YYYY format'),
  })

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember, companySettingForAll, position, positionDetails, positionPermissions } =
    globalSelector.company

  const selectedDay: any = currentDay.split(',')[0]

  const isForeman = position === 'Foreman' || position === 'CrewLead'
  const isProPlusPlan = currentCompany?.planType === SubscriptionPlanType.PROPLUS

  const subFetchData = useCallback(
    async ({ pageSize, pageIndex }: { pageSize: number; pageIndex: number }) => {
      try {
        // Give this fetch an ID
        const fetchId = ++subFetchIdRef.current

        // Set the loading state
        // setLoading(true)

        // We'll even set a delay to simulate a server here
        setTimeout(() => {
          // Only update the data if this is the latest fetch
          if (fetchId === subFetchIdRef.current) {
            const startRow = pageSize * pageIndex
            const endRow = startRow + pageSize
          }
        }, 1000)
      } catch (error) {
        console.error('TeamTable fetchData error', error)
      }
    },
    [detailsUpdate]
  )

  useEffect(() => {
    if (crewTimeCardDetails?.length || Array.isArray(crewTimeCardDetails)) {
      setLoading(false)
    }
  }, [crewTimeCardDetails])

  const columns = useMemo(() => {
    return getColumns(isPrivacy)
  }, [isPrivacy])

  const handleSubmit = async (submittedValues: InitialValues, { resetForm }: any) => {
    try {
      if (submittedValues.timeCardDate === '') {
        notify('Please Select the Date First', 'error')
        return
      }
      localStorage.setItem('currentDate', JSON.stringify(submittedValues.timeCardDate))
      let formattedDate = getFormattedDateForTimeCard(submittedValues.timeCardDate)
      setCurrentDay(formattedDate)
      setCurrentDayDate(submittedValues.timeCardDate)
      getApproveTimeCardsDetail(submittedValues.timeCardDate)
      getApproveTimeCardsDetailForNonCrew(submittedValues.timeCardDate)
      // resetForm()
      setPageLoading(true)
    } catch (error) {
      console.error('CrewManagementForm handleSubmit', error)
    }
  }
  const onNavigateCurrentDate = (action: string) => {
    try {
      setIsLoaded(false)
      if (action === 'next') {
        let currentDate = dayjs(new Date(currentDay)).add(1, 'days').format('YYYY-MM-DD')
        setCurrentDay(getFormattedDateForTimeCard(currentDate))
        setCurrentDayDate(currentDate)
        localStorage.setItem('currentDate', JSON.stringify(currentDate))
        getApproveTimeCardsDetail(currentDate)
        getApproveTimeCardsDetailForNonCrew(currentDate)
        setPageLoading(true)
      } else {
        let currentDate = dayjs(new Date(currentDay)).subtract(1, 'days').format('YYYY-MM-DD')
        setCurrentDay(getFormattedDateForTimeCard(currentDate))
        setCurrentDayDate(currentDate)
        localStorage.setItem('currentDate', JSON.stringify(currentDate))
        getApproveTimeCardsDetail(currentDate)
        getApproveTimeCardsDetailForNonCrew(currentDate)
        setPageLoading(true)
      }
    } catch (error) {
      console.error('onNavigateCurrentDate error', error)
    }
  }

  const onTogglePrivacy = (e: any) => {
    try {
      setIsPrivacy(e.target.checked)
      localStorage.setItem(StorageKey.privacy, e.target.checked)
    } catch (error) {
      console.error('onTogglePrivacy', error)
    }
  }

  const getApproveTimeCardsDetailForNonCrew = async (value?: string) => {
    let currentDateupdated: any = getDataFromLocalStorage('currentDate')

    try {
      // if (Object.keys(currentCompany).length > 0) {
      let dateValue
      if (value) {
        dateValue = dayjs(new Date(value)).format('YYYY-MM-DD')
      } else {
        dateValue = dayjs(new Date()).format('YYYY-MM-DD')
      }
      let dateValueFormatted
      if (localStorage.getItem('currentDate') && !value) {
        let currentDate: any = localStorage.getItem('currentDate')
        dateValueFormatted = getFormattedDate(JSON.parse(currentDate))
      } else {
        dateValueFormatted = getFormattedDate(dateValue)
        // localStorage.setItem('currentDate', JSON.stringify(dateValue))
      }
      if (nonCrewRef?.current) {
        nonCrewRef?.current?.abort()
      }

      const abortController = new AbortController()
      nonCrewRef.current = abortController
      let response = await getNonCrewsToApprove(currentDateupdated, nonCrewRef?.current?.signal, setLoading)

      const nonCrewReportData = response?.data?.data?.nonCrewReport
      // let response = await getNonCrewsToApprove(currentCompany._id, dateValueFormatted)

      if (isSuccess(response)) {
        setDetailsForNonCrew(nonCrewReportData)
        setCrewTimeCardDetailsForNonCrew(nonCrewReportData?.members)

        let loadingObj: any = {}
        let dayOffLoadObj: any = {}
        let allNonCrewApprObj: any = {}
        setDailyLogsLoading(false)
        nonCrewReportData?.members?.forEach((member: any, index: number) => {
          if (member.cards.length > 0) {
            allNonCrewApprObj = { ...allNonCrewApprObj, [`${member.memberId}`]: { allApproved: true } }
          } else {
            allNonCrewApprObj = { ...allNonCrewApprObj, [`${member.memberId}`]: { allApproved: false } }
            dayOffLoadObj = { ...dayOffLoadObj, [`${member?.memberId}`]: { loading: false } }
          }

          if (member.cards[0] === null) {
            allNonCrewApprObj = { ...allNonCrewApprObj, [`${member?.memberId}`]: { allApproved: false } }
            dayOffLoadObj = { ...dayOffLoadObj, [`${member?.memberId}`]: { loading: false } }
          } else {
            member?.cards?.forEach((card: any, index2: number) => {
              loadingObj = { ...loadingObj, [`${card?._id}`]: { loading: false } }
              if (card?.status === 'Unapproved' || card?.status === 'LeadApproved') {
                allNonCrewApprObj = { ...allNonCrewApprObj, [`${member?.memberId}`]: { allApproved: false } }
              }
            })
          }
        })

        setAllNonCrewApproveObj(allNonCrewApprObj)

        setPageLoading(false)
      } else {
        notify(response?.data?.message, 'error')
        setPageLoading(false)
      }
      // }
    } catch (error) {
      console.error('getApproveTimeCardsDetail error', error)
      setPageLoading(false)
    } finally {
      if (isAddTimeCard) {
        setTimeout(() => {
          setModifiedTimecardId('')
        }, 2000)
      } else {
        setModifiedTimecardId('')
      }
    }
  }

  const getApproveTimeCardsDetail = async (value?: string) => {
    let currentDateupdated: any = localStorage.getItem('currentDate')
    setDailyLogsLoading(true)
    try {
      // if (Object.keys(currentCompany).length > 0) {
      let dateValue
      if (value) {
        dateValue = dayjs(new Date(value)).format('YYYY-MM-DD')
      } else {
        dateValue = dayjs(new Date()).format('YYYY-MM-DD')
      }
      let dateValueFormatted
      if (localStorage.getItem('currentDate') && !value) {
        let currentDate: any = localStorage.getItem('currentDate')
        dateValueFormatted = getFormattedDate(JSON.parse(currentDate))
      } else {
        dateValueFormatted = getFormattedDate(dateValue)
        // localStorage.setItem('currentDate', JSON.stringify(dateValue))
      }
      let dateInString = dayjs(dateValueFormatted).format('YYYY-MM-DD')

      if (crewRef?.current) {
        crewRef?.current?.abort()
      }

      const abortController = new AbortController()
      crewRef.current = abortController

      // setCrewTimeCardDetails([])
      let response = await getCrewsToApprove(
        JSON.parse(currentDateupdated),
        dateInString,
        crewRef?.current?.signal,
        setLoading
      )

      if (positionPermissions?.team) {
        const statusResponse = await getTeamMembers({ limit: 200, deleted: false })

        const memberData = statusResponse?.data?.data?.memberData
          ?.filter((member: any) => {
            return member?.position?.position !== 'CEO'
          })
          ?.map((itm: any) => {
            return {
              memberId: itm?._id,
              name: itm?.name,
            }
          })
          ?.sort((a: any, b: any) => a?.name?.localeCompare(b?.name))
        setAllMembers(memberData)
      }

      const crewsToApproveData = response?.data?.data?.report
      // let response = await getCrewsToApprove(currentCompany._id, dateValueFormatted, dateInString)
      if (isSuccess(response)) {
        let loadingObj: any = {}
        let dayOffLoadObj: any = {}
        let allApprObj: any = {}
        setDailyLogsLoading(false)
        crewsToApproveData?.forEach((crew: any, index: number) => {
          crew.members.forEach((member: any, index1: number) => {
            if (member.cards.length > 0) {
              allApprObj = { ...allApprObj, [`${member.memberId}`]: { allApproved: true } }
            } else {
              allApprObj = { ...allApprObj, [`${member.memberId}`]: { allApproved: false } }
              dayOffLoadObj = { ...dayOffLoadObj, [`${member?.memberId}`]: { loading: false } }
            }

            if (member.cards[0] === null) {
              allApprObj = { ...allApprObj, [`${member?.memberId}`]: { allApproved: false } }
              dayOffLoadObj = { ...dayOffLoadObj, [`${member?.memberId}`]: { loading: false } }
            } else {
              member?.cards?.forEach((card: any, index2: number) => {
                loadingObj = { ...loadingObj, [`${card?._id}`]: { loading: false } }
                if (card?.status === 'Unapproved' || card?.status === 'LeadApproved') {
                  allApprObj = { ...allApprObj, [`${member?.memberId}`]: { allApproved: false } }
                }
              })
            }
          })
        })

        setCrewTimeCardDetails(crewsToApproveData)
        const crewData = crewsToApproveData?.map((item: { name?: string }) => ({
          value: item,
          label: `${item?.name}`,
        }))

        setCrewOptions([{ label: 'All', value: 'all' }, ...crewData])
        setApproveRejectLoadingObj(loadingObj)
        setAllApproveObj(allApprObj)
        setDayOffLoadingObj(dayOffLoadObj)
        setPageLoading(false)
      } else {
        notify(response?.data?.message, 'error')
        setPageLoading(false)
        setDailyLogsLoading(false)
      }
      // }
    } catch (error) {
      console.error('getApproveTimeCardsDetail error', error)
      setPageLoading(false)
    } finally {
      if (isAddTimeCard) {
        setTimeout(() => {
          setModifiedTimecardId('')
        }, 2000)
      } else {
        setModifiedTimecardId('')
      }
    }
  }
  const setMainCurrentDay = () => {
    try {
      if (localStorage.getItem('currentDate')) {
        let currentDate: any = localStorage.getItem('currentDate')
        setCurrentDay(getFormattedDateForTimeCard(JSON.parse(currentDate)))
        setCurrentDayDate(JSON.parse(currentDate))
      }
      // -----------------------my changes--------------------
      // let currentDate: any = new Date()
      // setCurrentDay(getFormattedDateForTimeCard(currentDate))
      // setCurrentDayDate(formatDateymd(currentDate))

      // -----------------------my changes--------------------
    } catch (error) {
      console.error('setMainCurrentDay error', error)
    }
  }

  const onEditingTimeCard = (timeCard: any, name: string, date?: string) => {
    try {
      setTimeCardData(timeCard)

      let timeCardDate = date

      let editDataObj = {
        name: name,
        date: timeCardDate,
        allHourlyPayCheckbox: timeCard.hasOwnProperty('allHourly') ? timeCard.allHourly : false,
        project: timeCard.projectPO,
        task: timeCard.task.replace('_', ' '),
        taskName: timeCard?.taskName,
        timeIn: getHoursAndMinutes(timeCard.timeIn),
        timeOut: getHoursAndMinutes(timeCard.timeOut),
        status: timeCard.status.replace('_', ' '),
        workDone: timeCard?.work?.work?.workDone,
        extraTime: [
          {
            hours: timeCard?.work?.work?.extraTime?.extraHrs,
            minutes: timeCard?.work?.work?.extraTime?.extraMin,
          },
        ],
        notes: timeCard?.notes,
        managerNotes: timeCard?.managerNotes,
        removeFromLeadBonusCheckbox: timeCard?.work?.removeLeadBonus,
      }

      setEditPopUpValues({ ...editPopUpValues, ...editDataObj })
      setIsAddTimeCard(false)
      setShowEditTimeCardPopUp(true)
    } catch (error) {
      console.error('onEditingTimeCard', error)
    }
  }

  useEffect(() => {
    if (currentDay && positionPermissions?.subcontractor) {
      fetchSubContractorToApprove()
    }
  }, [currentDay])

  useEffect(() => {
    if (pathname.split('/')[1] === 'time-cards') {
      setIsTimeCardPath(true)
      setCurrentCrew({
        crew: 'All',
      })
    }

    return () => {
      setIsTimeCardPath(false)
    }
  }, [])

  const fetchSubContractorToApprove = async () => {
    try {
      const currentDay = getDataFromLocalStorage('currentDate')

      if (subContractorRef?.current) {
        subContractorRef?.current?.abort()
      }

      const abortController = new AbortController()
      subContractorRef.current = abortController
      const res = await getSubContractorToApprove(currentDay, subContractorRef?.current?.signal, setLoading)
      if (isSuccess(res)) {
        setSubToApprove(res?.data?.data?.updatedSubs)
      } else notify(res?.data?.message, 'error')
    } catch (error) {
      console.error(error)
    }
  }

  const onTakeDayOff = async (memberId: string) => {
    try {
      // if (Object.keys(currentCompany).length > 0 && Object.keys(currentMember).length > 0) {
      setDayOffLoadingObj({ ...dayOffLoadingObj, [memberId]: { loading: true } })
      let currDate = startOfDate(currentDayDate)
      // setApproveRejectLoadingObj({ ...approveRejectLoadingObj, [_id]: { loading: true } })
      const newDate = new Date().toISOString()
      let dataObj = {
        memberId: memberId,
        timeIn: currDate,
        hrs: 0,
        timeOut: currDate,
        createdBy: currentMember._id,
      }

      const response = await takeDayOff(dataObj)

      if (isSuccess(response)) {
        setIsLoaded(true)
        await getApproveTimeCardsDetail(currentDayDate)
        await getApproveTimeCardsDetailForNonCrew(currentDayDate)
        notify('Day Off Taken Successfully', 'success')
      } else {
        notify(response?.data?.message, 'error')
        setDayOffLoadingObj({ ...dayOffLoadingObj, [memberId]: { loading: false } })
      }
      // }
    } catch (error) {
      console.error('onTakeDayOff error', error)
      setDayOffLoadingObj({ ...dayOffLoadingObj, [memberId]: { loading: false } })
    } finally {
      setLoading(false)
    }
  }

  const onCancelDayOff = async (timeCardId: string, memberId: string) => {
    try {
      // if (Object.keys(currentCompany).length > 0 && Object.keys(currentMember).length > 0) {
      setDayOffLoadingObj({ ...dayOffLoadingObj, [memberId]: { loading: true } })

      let dataObj = {
        timeCardId: timeCardId,
      }

      const response = await cancelDayOff(dataObj)

      if (isSuccess(response)) {
        setIsLoaded(true)
        await getApproveTimeCardsDetail(currentDayDate)
        await getApproveTimeCardsDetailForNonCrew(currentDayDate)
        notify('Day Off Cancelled Successfully', 'success')
      } else {
        notify(response?.data?.message, 'error')
        setDayOffLoadingObj({ ...dayOffLoadingObj, [memberId]: { loading: false } })
      }
      // }
    } catch (error) {
      setDayOffLoadingObj({ ...dayOffLoadingObj, [memberId]: { loading: false } })
    } finally {
      setLoading(false)
    }
  }

  const singleApproveOrReject = async (event: any, _id: string, status: string, prevStatus?: string) => {
    try {
      event.stopPropagation()

      if (isForeman && (prevStatus === 'Approved' || status === 'Approved')) {
        notify('Cannot unapprove a manager approved time card', 'error')
        return
      }

      setApproveRejectLoadingObj({ ...approveRejectLoadingObj, [_id]: { loading: true } })

      let response = await updateTimeCardToApproveOrReject({ _id, status })

      if (isSuccess(response)) {
        setIsLoaded(true)
        await getApproveTimeCardsDetail(currentDayDate)
        await getApproveTimeCardsDetailForNonCrew(currentDayDate)
        notify(`Time Card ${status} Successfully`, 'success')
      } else {
        notify(response?.data?.message, 'error')
        setApproveRejectLoadingObj({ ...approveRejectLoadingObj, [_id]: { loading: false } })
      }
    } catch (error) {
      setApproveRejectLoadingObj({ ...approveRejectLoadingObj, [_id]: { loading: false } })
    } finally {
      setLoading(false)
    }
  }

  const getSubcontractordetail = async () => {
    try {
      // if (Object.keys(currentCompany).length > 0) {

      const statusResponse = await getSubContractors({ deleted: false, retired: false, limit: '500' })

      // const statusResponse1 = await getDailyLog(currentCompany._id, statusResponse?.data?.data?.subcontractor[0]._id)
      if (isSuccess(statusResponse)) {
        let statusRes = statusResponse?.data?.data?.subcontractor
        // let statusRes1 = statusResponse1?.data?.data?.dailyLog
        // setSubDailyDataTemp(statusRes1)
        setSubcontractorData([statusRes[0]])
        setSubDailyDataDropDown(statusRes)
      } else {
        notify(statusResponse?.data?.message, 'error')
      }
      // }
    } catch (error) {
      console.error('getSubcontractordetail error', error)
    }
  }

  useEffect(() => {
    getApproveTimeCardsDetail()
    getApproveTimeCardsDetailForNonCrew()
    setMainCurrentDay()
  }, [currentMember])

  useEffect(() => {
    getApproveTimeCardsDetail(currentDayDate)
    getApproveTimeCardsDetailForNonCrew(currentDayDate)
  }, [dataUpdate, subDailyUpdate])

  useEffect(() => {
    if (positionPermissions?.subcontractor) {
      getSubcontractordetail()
    }
  }, [subDailyUpdate, currentMember, positionPermissions?.subcontractor])

  const renderCrewMetrics = useCallback(
    (crew: any) => {
      const configs = getCrewMetricConfigs(crew)

      return configs.map((config) => (
        <SummaryCard
          key={config.label}
          label={config.label}
          value={config.value}
          isRounded={config.isRounded}
          isCurrency={config.isCurrency}
          isPrivacy={isPrivacy}
          dailyLogsLoading={dailyLogsLoading}
          suffix={config.suffix}
        />
      ))
    },
    [isPrivacy, dailyLogsLoading]
  )

  const renderNonCrewMetrics = useCallback(
    (nonCrewData: any) => {
      const configs = getNonCrewMetricConfigs(nonCrewData)

      return configs.map((config) => (
        <SummaryCard
          key={config.label}
          label={config.label}
          value={config.value}
          isRounded={config.isRounded}
          isCurrency={config.isCurrency}
          isPrivacy={isPrivacy}
          dailyLogsLoading={dailyLogsLoading}
          suffix={config.suffix}
        />
      ))
    },
    [isPrivacy, dailyLogsLoading]
  )

  return (
    <>
      <SettingsCont className="half">
        <SharedStyled.FlexCol gap="12px">
          <SharedStyled.FlexRow justifyContent="space-between" flexWrap="wrap">
            <SharedStyled.SectionTitle>Approve Time Cards</SharedStyled.SectionTitle>
            <SharedStyled.FlexRow width="max-content">
              {timeOffPositions?.includes(positionDetails?.symbol) ? (
                <Button
                  width="max-content"
                  onClick={() => {
                    setShowTimeOffModal(true)
                  }}
                >
                  New Time Off Request
                </Button>
              ) : null}
              <Button
                width="max-content"
                className="gray"
                onClick={() => {
                  navigate('/time-cards/deleted')
                }}
              >
                Deleted Time Cards
              </Button>
            </SharedStyled.FlexRow>
          </SharedStyled.FlexRow>
          <Formik
            initialValues={initialValues}
            onSubmit={handleSubmit}
            validationSchema={ApproveTimeCardsFormSchema}
            enableReinitialize={true}
            validateOnChange={true}
            validateOnBlur={false}
          >
            {({ errors, touched, values, setFieldValue }) => {
              return (
                <Form className="form">
                  <Styled.PickTheDayContainer>
                    <SharedDate
                      value={values.timeCardDate}
                      labelName="Pick The Day"
                      stateName="timeCardDate"
                      error={touched.timeCardDate && errors.timeCardDate ? true : false}
                      setFieldValue={setFieldValue}
                    />
                    <Button width="max-content" type="submit" height="52px" disabled={!values.timeCardDate}>
                      Go
                    </Button>
                  </Styled.PickTheDayContainer>
                </Form>
              )
            }}
          </Formik>
        </SharedStyled.FlexCol>
        <Styled.CurrentDayDetail marginTop="14px">{currentDay}</Styled.CurrentDayDetail>
        <SharedStyled.FlexRow width="100%" alignItems="center" justifyContent="space-between" margin="12px 0 0 0">
          <Button
            type="button"
            className="yellow"
            // disabled={pageLoading || dailyLogsLoading}
            onClick={() => onNavigateCurrentDate('previous')}
          >
            Previous Day
          </Button>
          <Button
            type="button"
            disabled={isDateGreaterThanToday(dayjsFormat(currentDay, 'YYYY-MM-DD'))}
            onClick={() => onNavigateCurrentDate('next')}
          >
            Next Day
          </Button>
        </SharedStyled.FlexRow>
        {crewTimeCardDetails?.length ? (
          <Styled.PrivacyContainer marginTop="24px">
            <Styled.PrivacyText>Privacy</Styled.PrivacyText>
            <Styled.SwitchDiv>
              <Styled.CheckboxInput type="checkbox" checked={isPrivacy} onChange={onTogglePrivacy} />
              <Styled.SliderRound className="slider round"></Styled.SliderRound>
            </Styled.SwitchDiv>
          </Styled.PrivacyContainer>
        ) : null}

        <SharedStyled.FlexRow justifyContent={'flex-end'} margin="16px 0 0 0">
          {currentCrew?.get('crew') !== 'All' ? (
            <Styled.CrewNameText>Crew: {currentCrew?.get('crew')}</Styled.CrewNameText>
          ) : null}

          {crewOptions?.length! > 1 ? (
            <Dropdown
              onSelectValue={(val: any) => {
                isTimeCardPath &&
                  setCurrentCrew({
                    crew: val?.label,
                  })
              }}
              options={crewOptions}
              selectedValue={{
                label: currentCrew.get('crew'),
              }}
            >
              <p>{currentCrew.get('crew')}</p>
            </Dropdown>
          ) : null}
        </SharedStyled.FlexRow>

        {!isLoaded && (pageLoading || dailyLogsLoading || loading) ? (
          <Styled.PageLoadingContainer marginTop="50px">
            <SharedStyled.FlexCol gap="20px">
              <SLoader height={40} width={150} />

              <SLoader height={100} />
              <SharedStyled.FlexRow>
                <SLoader height={200} />
                <SLoader height={200} />
                <SLoader height={200} />
                <SLoader height={200} />
              </SharedStyled.FlexRow>
            </SharedStyled.FlexCol>
          </Styled.PageLoadingContainer>
        ) : (
          <>
            {crewTimeCardDetails?.length ? (
              <>
                {crewTimeCardDetails
                  ?.filter((item: any) =>
                    currentCrew?.get('crew') === 'All' ? item : item?.name === currentCrew?.get('crew')
                  )
                  ?.sort((a: any, b: any) => a?.order - b?.order)
                  ?.map((crew: any, index: number) => (
                    <Styled.CrewDetailContainer marginTop="24px" key={index}>
                      {currentCrew?.get('crew') === 'All' ? (
                        <Styled.CrewNameText>Crew: {crew?.name}</Styled.CrewNameText>
                      ) : null}
                      {isProPlusPlan ? (
                        <Styled.MetricsContainer marginTop="12px">
                          {crew?.logStats?._id ? (
                            <div>
                              <Styled.OuterMiniContainer>{renderCrewMetrics(crew)}</Styled.OuterMiniContainer>
                              <SharedStyled.FlexBox
                                width="100%"
                                alignItems="center"
                                justifyContent="space-between"
                                gap="10px"
                                margin="8px 0 18px 0"
                              >
                                <Styled.DisplayProjectHeader
                                  onClick={() => setShowProjects((prev) => !prev)}
                                  marginTop="10px"
                                >
                                  {showProjects ? 'Hide' : 'Show'} Projects
                                </Styled.DisplayProjectHeader>
                                <Styled.EditContainer
                                  onClick={() => {
                                    setLogId(crew?.logStats?._id)
                                    setIsEditDailyLog(true)
                                    setPoList(crew?.POList)
                                    setShowCrewDailyLog(true)
                                    setSelectedCrew(crew.crewId)
                                  }}
                                >
                                  Edit
                                </Styled.EditContainer>
                              </SharedStyled.FlexBox>
                            </div>
                          ) : (
                            <div>
                              <Styled.DailyLogHeader style={{ margin: '8px 0' }}>No Daily Log</Styled.DailyLogHeader>
                              <div>
                                <Button
                                  width="max-content"
                                  onClick={() => {
                                    setIsEditDailyLog(false)
                                    setPoList(crew?.POList)
                                    setShowCrewDailyLog(true)
                                    setSelectedCrew(crew.crewId)
                                  }}
                                  disabled={!crew?.POList?.length}
                                >
                                  Add Daily Log
                                </Button>
                              </div>
                            </div>
                          )}

                          {crew?.logStats?._id && showProjects && (
                            <Table
                              noOverflow
                              columns={columns}
                              data={crew?.logStats?.projects}
                              pageCount={1}
                              fetchData={() => {}}
                              noSearch
                              minWidth=""
                              noBorder
                              noPagination
                            />
                          )}
                        </Styled.MetricsContainer>
                      ) : (
                        <>
                          {crew?.numWorking ? (
                            <Styled.MetricsContainer marginTop="12px">
                              <Styled.OuterMiniContainer>
                                <SummaryCard
                                  label="Num Working"
                                  value={crew?.numWorking}
                                  isCurrency={false}
                                  dailyLogsLoading={dailyLogsLoading}
                                />
                                <SummaryCard
                                  label="Paid"
                                  value={crew?.paid}
                                  isCurrency={true}
                                  dailyLogsLoading={dailyLogsLoading}
                                />
                                <SummaryCard
                                  label="Time Worked"
                                  value={crew?.timeWorked}
                                  isCurrency={false}
                                  dailyLogsLoading={dailyLogsLoading}
                                  suffix="hrs"
                                />
                              </Styled.OuterMiniContainer>
                            </Styled.MetricsContainer>
                          ) : null}
                        </>
                      )}
                      {/* ========================================= Time-card ========================================= */}

                      <Styled.TimeCardGridContainer marginTop="10px">
                        {moveCrewLeadFirst(crew?.members)?.map((member: any, index1: number) => (
                          <Styled.SingleTimeCardContainer
                            key={index1}
                            allApproved={allApproveObj[member.memberId]?.allApproved || member?.dayOff}
                          >
                            <Styled.MemberNameAddButtonContainerDiv>
                              <Styled.MemberName>{member?.name}</Styled.MemberName>

                              {member?.dayOff ? null : (
                                <SharedStyled.Button
                                  maxWidth="18px"
                                  maxHeight="18px"
                                  mediaFontSize="14px"
                                  borderRadius="4px"
                                  type="submit"
                                  bgColor={colors.darkGrey}
                                  color={colors.white}
                                  onClick={() => {
                                    setTimeCardData(member)
                                    setIsAddTimeCard(true)
                                    setShowAddTimeCardPopUp(true)
                                  }}
                                >
                                  +
                                </SharedStyled.Button>
                              )}
                            </Styled.MemberNameAddButtonContainerDiv>
                            <Styled.KeyValueSpan className="bolder">
                              {member?.hours.toFixed(2) ?? ''}h $
                              {hideDollarValue(member?.totalEarned.toFixed(2), isPrivacy) ?? '--'}
                            </Styled.KeyValueSpan>

                            {member?.wageType === 'hourly' && (
                              <Styled.KeyValueSpan>
                                Hourly: ${hideDollarValue(member?.dayBase.toFixed(2), isPrivacy) ?? ''}{' '}
                              </Styled.KeyValueSpan>
                            )}
                            {member?.wageType === 'salary' && (
                              <Styled.KeyValueSpan>
                                Salary: ${hideDollarValue(member?.dayBase.toFixed(2), isPrivacy) ?? ''}{' '}
                              </Styled.KeyValueSpan>
                            )}

                            {member?.travel !== 0 && (
                              <Styled.KeyValueSpan>
                                Travel: ${member?.travel ? hideDollarValue(member?.travel.toFixed(2), isPrivacy) : ''}{' '}
                              </Styled.KeyValueSpan>
                            )}
                            {member?.crewLead ? (
                              <>
                                {member?.leadBonus ? (
                                  <Styled.KeyValueSpan>
                                    Lead Bonus: $
                                    {member?.leadBonus
                                      ? hideDollarValue(member?.leadBonus.toFixed(2), isPrivacy)
                                      : '--'}
                                  </Styled.KeyValueSpan>
                                ) : null}
                                {member?.pwTotal ? (
                                  <Styled.KeyValueSpan>
                                    Piece Work: $
                                    {member?.pwTotal ? hideDollarValue(member?.pwTotal.toFixed(2), isPrivacy) : '--'}{' '}
                                  </Styled.KeyValueSpan>
                                ) : null}
                              </>
                            ) : (
                              <>
                                {member?.pwTotal ? (
                                  <Styled.KeyValueSpan>
                                    Piece Work: $
                                    {member?.pwTotal ? hideDollarValue(member?.pwTotal.toFixed(2), isPrivacy) : ''}{' '}
                                  </Styled.KeyValueSpan>
                                ) : null}
                              </>
                            )}

                            {!member?.dayOff && member?.cards.length > 0 ? (
                              <>
                                {member?.cards?.map((timeCard: any, index3: number) => {
                                  return (
                                    <Fragment key={index3}>
                                      <Timecard
                                        timeCard={timeCard}
                                        member={member}
                                        hideDollar={isPrivacy}
                                        isLoading={
                                          timeCard?.memberId === modifiedTimecardId ||
                                          timeCard?._id === modifiedTimecardId
                                        }
                                        onTimeCardClick={(data) => {
                                          onEditingTimeCard(
                                            { ...data?.timeCard, positionId: member?.positionId },
                                            data?.name,
                                            data?.date
                                          )
                                        }}
                                        renderApprove={() => (
                                          <Styled.TimeCardApproveNoApproveRightDiv
                                            isApproved={
                                              timeCard?.status === TimeCardStatusEnum.Approved ||
                                              (timeCard?.status === TimeCardStatusEnum.Lead_Approved && isForeman)
                                            }
                                            isLeadApproved={timeCard?.status === TimeCardStatusEnum.Lead_Approved}
                                            title={
                                              timeCard?.status === 'Unapproved' ||
                                              (timeCard?.status === 'LeadApproved' && !isForeman)
                                                ? 'Approve Timecard'
                                                : 'Unapprove Timecard'
                                            }
                                          >
                                            <Styled.IconContainer
                                              onClick={(event: any) => {
                                                timeCard?.status === 'Unapproved' ||
                                                (timeCard?.status === 'LeadApproved' && !isForeman)
                                                  ? singleApproveOrReject(
                                                      event,
                                                      timeCard._id,
                                                      isForeman ? 'LeadApproved' : 'Approved'
                                                    )
                                                  : singleApproveOrReject(
                                                      event,
                                                      timeCard._id,
                                                      'Unapproved',
                                                      timeCard?.status
                                                    )
                                              }}
                                            >
                                              {(timeCard?.status === TimeCardStatusEnum.Lead_Approved && isForeman) ||
                                              timeCard?.status === TimeCardStatusEnum.Approved ? (
                                                <>
                                                  {approveRejectLoadingObj[timeCard?._id]?.loading ? (
                                                    <SharedStyled.Loader color={colors.darkGrey} />
                                                  ) : (
                                                    <CrossIcon />
                                                  )}
                                                </>
                                              ) : (
                                                <>
                                                  {approveRejectLoadingObj[timeCard?._id]?.loading ? (
                                                    <SharedStyled.Loader color={colors.darkGrey} />
                                                  ) : (
                                                    <TickmarkIcon />
                                                  )}
                                                </>
                                              )}
                                            </Styled.IconContainer>

                                            {timeCard?.work?.removeLeadBonus && modifiedTimecardId !== timeCard?._id ? (
                                              <span style={{ alignSelf: 'flex-end' }}>x</span>
                                            ) : null}
                                          </Styled.TimeCardApproveNoApproveRightDiv>
                                        )}
                                      />
                                    </Fragment>
                                  )
                                })}
                              </>
                            ) : (
                              !companySettingForAll?.weekEndDays?.includes(selectedDay) && (
                                <SharedStyled.FlexCol>
                                  <Styled.TimeCardNoTimeCardDayOffButton
                                    isDayOff={member?.dayOff}
                                    onClick={() => {
                                      if (member?.cards?.[0]?.ptoUsed) {
                                        notify('Cannot cancel a Day Off when PTO is used', 'error')
                                        return
                                      }
                                      member?.dayOff
                                        ? onCancelDayOff(member?.dayOffTimeCardId, member?.memberId)
                                        : onTakeDayOff(member?.memberId)
                                    }}
                                  >
                                    {member?.dayOff ? (
                                      <Styled.TimeCardNoTimeCardDayOffButtonDiv>
                                        {dayOffLoadingObj[member?.memberId]?.loading ? (
                                          <>
                                            <Styled.TimeCardNoTimeCardDayOffButtonText>
                                              Canceling Day Off.. <SharedStyled.Loader />
                                            </Styled.TimeCardNoTimeCardDayOffButtonText>
                                          </>
                                        ) : (
                                          <>
                                            <Styled.TimeCardNoTimeCardDayOffButtonText>
                                              Took The Day Off!
                                            </Styled.TimeCardNoTimeCardDayOffButtonText>
                                            <Styled.TimeCardNoTimeCardDayOffButtonText>
                                              (click to cancel)
                                            </Styled.TimeCardNoTimeCardDayOffButtonText>
                                          </>
                                        )}
                                      </Styled.TimeCardNoTimeCardDayOffButtonDiv>
                                    ) : (
                                      <Styled.TimeCardNoTimeCardDayOffButtonDiv>
                                        {dayOffLoadingObj[member?.memberId]?.loading ? (
                                          <>
                                            <Styled.TimeCardNoTimeCardDayOffButtonText>
                                              Taking Day Off.. <SharedStyled.Loader color={colors.darkGrey} />
                                            </Styled.TimeCardNoTimeCardDayOffButtonText>
                                          </>
                                        ) : (
                                          <>
                                            <Styled.TimeCardNoTimeCardDayOffButtonText>
                                              No Time Cards...
                                            </Styled.TimeCardNoTimeCardDayOffButtonText>
                                            <Styled.TimeCardNoTimeCardDayOffButtonText>
                                              Day Off?
                                            </Styled.TimeCardNoTimeCardDayOffButtonText>
                                          </>
                                        )}
                                      </Styled.TimeCardNoTimeCardDayOffButtonDiv>
                                    )}
                                  </Styled.TimeCardNoTimeCardDayOffButton>
                                  {/* {member?.dayOff ? (
                                    <> */}
                                  {member?.cards?.[0]?.ptoUsed ? (
                                    <SharedStyled.FlexRow margin="8px 0 0 0">
                                      <div>
                                        <b>PTO Taken</b>
                                      </div>
                                      {timeOffPositions?.includes(positionDetails?.symbol) ? (
                                        <div
                                          className="App-link"
                                          onClick={() => {
                                            setPtoData({
                                              name: member?.name,
                                              date: member?.date,
                                              ptoUsed: member?.cards?.[0]?.ptoUsed,
                                              timeCardId: member?.cards?.[0]?._id,
                                            })
                                            setShowEditTimeOffModal(true)
                                          }}
                                        >
                                          Edit
                                        </div>
                                      ) : null}
                                    </SharedStyled.FlexRow>
                                  ) : null}
                                  {/* </>
                                  ) : null} */}
                                </SharedStyled.FlexCol>
                              )
                            )}
                          </Styled.SingleTimeCardContainer>
                        ))}
                      </Styled.TimeCardGridContainer>
                    </Styled.CrewDetailContainer>
                  ))}
              </>
            ) : null}

            {/* ========================================= Non Crew ========================================= */}

            {crewTimeCardDetailsForNonCrew?.length ? (
              <>
                <Styled.CrewDetailContainer marginTop="24px">
                  {crewTimeCardDetailsForNonCrew?.length ? <Styled.CrewNameText>Non Crew:</Styled.CrewNameText> : null}

                  {detailsForNonCrew?.numWorking ? (
                    <Styled.MetricsContainer marginTop="12px">
                      <Styled.OuterMiniContainer>{renderNonCrewMetrics(detailsForNonCrew)}</Styled.OuterMiniContainer>
                    </Styled.MetricsContainer>
                  ) : null}

                  <Styled.TimeCardGridContainer marginTop="10px">
                    {crewTimeCardDetailsForNonCrew?.map((member: any, index: number) => (
                      <Styled.SingleTimeCardContainer
                        key={index}
                        allApproved={allNonCrewApproveObj[member.memberId]?.allApproved || member?.dayOff}
                      >
                        <Styled.MemberNameAddButtonContainerDiv>
                          <SharedStyled.TooltipContainer
                            width="250px"
                            positionLeft="0px"
                            positionBottom="0px"
                            positionLeftDecs="120px"
                            positionBottomDecs="20px"
                          >
                            <span className="tooltip-content">{member?.name}</span>

                            <Styled.MemberName>{member?.name}</Styled.MemberName>
                          </SharedStyled.TooltipContainer>

                          {member?.dayOff ? null : (
                            <SharedStyled.Button
                              maxWidth="18px"
                              maxHeight="18px"
                              mediaFontSize="14px"
                              borderRadius="4px"
                              type="submit"
                              bgColor={colors.darkGrey}
                              color={colors.white}
                              onClick={() => {
                                setTimeCardData(member)
                                setIsAddTimeCard(true)
                                setShowAddTimeCardPopUp(true)
                              }}
                            >
                              +
                            </SharedStyled.Button>
                          )}
                        </Styled.MemberNameAddButtonContainerDiv>
                        <Styled.KeyValueSpan className="bolder">
                          {Number(member?.hours)?.toFixed(2) ?? ''}h $
                          {hideDollarValue(member?.totalEarned.toFixed(2), isPrivacy) ?? '--'}
                        </Styled.KeyValueSpan>
                        <Styled.KeyValueSpan>
                          Hourly: ${hideDollarValue(member?.dayBase.toFixed(2), isPrivacy) ?? ''}{' '}
                        </Styled.KeyValueSpan>

                        {member?.travel !== 0 && (
                          <Styled.KeyValueSpan>
                            Travel: ${hideDollarValue(member?.travel.toFixed(2), isPrivacy)}{' '}
                          </Styled.KeyValueSpan>
                        )}
                        {member?.crewLead ? (
                          <>
                            {member?.leadBonus ? (
                              <Styled.KeyValueSpan>
                                Lead Bonus: ${hideDollarValue(member?.leadBonus.toFixed(2), isPrivacy) ?? ''}
                              </Styled.KeyValueSpan>
                            ) : null}{' '}
                          </>
                        ) : (
                          <></>
                        )}

                        {!member?.dayOff && member?.cards.length > 0 ? (
                          <>
                            {member?.cards?.map((timeCard: any, index3: number) => (
                              <Timecard
                                key={index3}
                                timeCard={timeCard}
                                member={member}
                                isLoading={
                                  timeCard?._id === modifiedTimecardId || timeCard?.memberId === modifiedTimecardId
                                }
                                hideDollar={isPrivacy}
                                onTimeCardClick={(data) => {
                                  onEditingTimeCard(
                                    { ...data?.timeCard, positionId: member?.positionId },
                                    data?.name,
                                    data?.date
                                  )
                                }}
                                renderApprove={() => (
                                  <Styled.TimeCardApproveNoApproveRightDiv
                                    isApproved={timeCard?.status === TimeCardStatusEnum.Approved}
                                    isLeadApproved={timeCard?.status === TimeCardStatusEnum.Lead_Approved}
                                  >
                                    <Styled.IconContainer
                                      onClick={(event: any) => {
                                        timeCard?.status === TimeCardStatusEnum.Unapproved
                                          ? singleApproveOrReject(event, timeCard._id, 'Approved')
                                          : singleApproveOrReject(event, timeCard._id, 'Unapproved')
                                      }}
                                    >
                                      {timeCard?.status === TimeCardStatusEnum.Lead_Approved ||
                                      timeCard?.status === TimeCardStatusEnum.Approved ? (
                                        <>
                                          {approveRejectLoadingObj[timeCard?._id]?.loading ? (
                                            <SharedStyled.Loader color={colors.darkGrey} />
                                          ) : (
                                            <CrossIcon />
                                          )}
                                        </>
                                      ) : (
                                        <>
                                          {approveRejectLoadingObj[timeCard?._id]?.loading ? (
                                            <SharedStyled.Loader color={colors.darkGrey} />
                                          ) : (
                                            <TickmarkIcon />
                                          )}
                                        </>
                                      )}
                                    </Styled.IconContainer>
                                  </Styled.TimeCardApproveNoApproveRightDiv>
                                )}
                              />
                            ))}
                          </>
                        ) : (
                          !companySettingForAll?.weekStartDay?.includes(selectedDay) && (
                            <SharedStyled.FlexCol>
                              <Styled.TimeCardNoTimeCardDayOffButton
                                isDayOff={member?.dayOff}
                                onClick={() => {
                                  if (member?.cards?.[0]?.ptoUsed) {
                                    notify('Cannot cancel a Day Off when PTO is used', 'error')
                                    return
                                  }
                                  member?.dayOff
                                    ? onCancelDayOff(member?.dayOffTimeCardId, member?.memberId)
                                    : onTakeDayOff(member?.memberId)
                                }}
                              >
                                {member?.dayOff ? (
                                  <Styled.TimeCardNoTimeCardDayOffButtonDiv>
                                    {dayOffLoadingObj[member?.memberId]?.loading ? (
                                      <>
                                        <Styled.TimeCardNoTimeCardDayOffButtonText>
                                          Canceling Day Off.. <SharedStyled.Loader />
                                        </Styled.TimeCardNoTimeCardDayOffButtonText>
                                      </>
                                    ) : (
                                      <>
                                        <Styled.TimeCardNoTimeCardDayOffButtonText>
                                          Took The Day Off!
                                        </Styled.TimeCardNoTimeCardDayOffButtonText>
                                        <Styled.TimeCardNoTimeCardDayOffButtonText>
                                          (click to cancel)
                                        </Styled.TimeCardNoTimeCardDayOffButtonText>
                                      </>
                                    )}
                                  </Styled.TimeCardNoTimeCardDayOffButtonDiv>
                                ) : (
                                  <Styled.TimeCardNoTimeCardDayOffButtonDiv>
                                    {dayOffLoadingObj[member?.memberId]?.loading ? (
                                      <>
                                        <Styled.TimeCardNoTimeCardDayOffButtonText>
                                          Taking Day Off.. <SharedStyled.Loader color={colors.darkGrey} />
                                        </Styled.TimeCardNoTimeCardDayOffButtonText>
                                      </>
                                    ) : (
                                      <>
                                        <Styled.TimeCardNoTimeCardDayOffButtonText>
                                          No Time Cards...
                                        </Styled.TimeCardNoTimeCardDayOffButtonText>
                                        <Styled.TimeCardNoTimeCardDayOffButtonText>
                                          Day Off?
                                        </Styled.TimeCardNoTimeCardDayOffButtonText>
                                      </>
                                    )}
                                  </Styled.TimeCardNoTimeCardDayOffButtonDiv>
                                )}
                              </Styled.TimeCardNoTimeCardDayOffButton>
                              {member?.cards?.[0]?.ptoUsed ? (
                                <SharedStyled.FlexRow margin="8px 0 0 0">
                                  <div>
                                    <b>PTO Taken</b>
                                  </div>
                                  <div
                                    className="App-link"
                                    onClick={() => {
                                      setPtoData({
                                        name: member?.name,
                                        date: member?.date,
                                        ptoUsed: member?.cards?.[0]?.ptoUsed,
                                        timeCardId: member?.cards?.[0]?._id,
                                      })
                                      setShowEditTimeOffModal(true)
                                    }}
                                  >
                                    Edit
                                  </div>
                                </SharedStyled.FlexRow>
                              ) : null}
                            </SharedStyled.FlexCol>
                          )
                        )}
                      </Styled.SingleTimeCardContainer>
                    ))}
                  </Styled.TimeCardGridContainer>
                </Styled.CrewDetailContainer>
              </>
            ) : (
              <>
                {Array.isArray(crewTimeCardDetails) && !crewTimeCardDetails?.length && !loading && !dailyLogsLoading ? (
                  <SharedStyled.FlexRow justifyContent="center" margin="20px 0 0 0">
                    <h1>No data found</h1>
                  </SharedStyled.FlexRow>
                ) : null}
              </>
            )}

            {/* ========================================= Subcontractor  ========================================= */}

            {isProPlusPlan ? (
              <>
                {subToApprove?.map((data: any, index: number) => (
                  <div key={index}>
                    <Styled.CrewNameText marginTop="30px">Sub: {data?.name}</Styled.CrewNameText>
                    <Styled.MetricsContainer marginTop="12px">
                      <>
                        <Styled.OuterMiniContainer>
                          {data?.report?._id &&
                            subDailyLogKeyValue?.map((box: any, index: number) => (
                              <Fragment key={index}>
                                {data?.report?.[box?.value] || box?.showAlways ? (
                                  <Styled.MiniContainer>
                                    <Styled.MiniContainerHeader>{box.header}</Styled.MiniContainerHeader>
                                    <Styled.MiniContainerValue>
                                      ${hideDollarValue(data?.report?.[box?.value]?.toFixed(2), isPrivacy)}
                                    </Styled.MiniContainerValue>
                                  </Styled.MiniContainer>
                                ) : null}
                              </Fragment>
                            ))}
                        </Styled.OuterMiniContainer>
                        <SharedStyled.FlexBox
                          width="100%"
                          alignItems="center"
                          justifyContent="space-between"
                          gap="10px"
                          margin="8px 0 18px 0"
                        >
                          {data?.report?._id ? (
                            <Styled.DisplayProjectHeader
                              onClick={() => setShowProjects((prev) => !prev)}
                              marginTop="10px"
                            >
                              {showProjects ? 'Hide' : 'Show'} Projects
                            </Styled.DisplayProjectHeader>
                          ) : (
                            <Button
                              width="max-content"
                              type="submit"
                              onClick={() => {
                                setSubDailyData({ ...data })
                                setShowAddSubDailyLogModal(true)
                              }}
                            >
                              Add Daily Log
                            </Button>
                          )}

                          {data?.report?._id && (
                            <Styled.EditContainer
                              onClick={() => {
                                setSubDailyData({ id: data?._id })
                                setIsSubEditDailyLog(true)
                                setShowSubDailyLogModal(true)
                                setSubDailyLogId(data?.report?.log?._id)
                                setEditDailyLogData(data?.report?.log)
                              }}
                            >
                              Edit
                            </Styled.EditContainer>
                          )}
                        </SharedStyled.FlexBox>

                        {data?.report?._id && showProjects && (
                          <SubTable
                            columns={columns}
                            data={data?.report?.projects}
                            loading={loading}
                            pageCount={subPageCount}
                            fetchData={subFetchData}
                            noLink={true}
                            noSearch={true}
                            noPagination={true}
                          />
                        )}
                      </>
                    </Styled.MetricsContainer>
                  </div>
                ))}
              </>
            ) : null}
          </>
        )}
      </SettingsCont>
      <CustomModal show={showAddTimeCardPopUp}>
        {showAddTimeCardPopUp && (
          <AddTimeCardPopUp
            setShowTimeCardPopUp={setShowAddTimeCardPopUp}
            timeCardData={timeCardData}
            setDataUpdate={setDataUpdate}
            setModifiedTimecardId={setModifiedTimecardId}
            setIsLoaded={setIsLoaded}
            setIsAddTimeCard={setIsAddTimeCard}
          />
        )}
      </CustomModal>
      <CustomModal show={showCrewDailyLog} className="top">
        {showCrewDailyLog && (
          <DailyLog
            isEdit={isEditDailyLog}
            onClose={() => {
              setShowCrewDailyLog(false)
              setIsEditDailyLog(false)
            }}
            onSubmit={() => {
              setDataUpdate((p) => !p)
            }}
            selectedCrewId={selectedCrew}
            logId={logId}
            pickPo={poList}
          />
        )}
      </CustomModal>
      <CustomModal show={showEditTimeCardPopUp} className="top timecard">
        {showEditTimeCardPopUp && (
          <EditTimeCardPopUp
            setShowTimeCardPopUp={setShowEditTimeCardPopUp}
            editPopUpValues={editPopUpValues}
            timeCardData={timeCardData}
            setDataUpdate={setDataUpdate}
            setIsLoaded={setIsLoaded}
            isTimecardScreen
            setModifiedTimecardId={setModifiedTimecardId}
          />
        )}
      </CustomModal>
      <CustomModal show={showSubDailyLogModal} className="top timecard">
        {showSubDailyLogModal && (
          <SubDailyLog
            setShowSubDailyLogModal={setShowSubDailyLogModal}
            action="Edit Daily Log"
            subcontractorData={subcontractorData}
            setIsSubEditDailyLog={setIsSubEditDailyLog}
            setSubDailyUpdate={setSubDailyUpdate}
            subDailyDataDropDown={subDailyDataDropDown}
            subDailyLogId={subDailyLogId}
            editDailyLogData={editDailyLogData}
            isSubEditDailyLog={isSubEditDailyLog}
            fetchSubContractorToApprove={fetchSubContractorToApprove}
          />
        )}
      </CustomModal>
      <CustomModal show={showAddSubDailyLogModal} className="top timecard">
        <SubDailyLog
          setShowSubDailyLogModal={setShowAddSubDailyLogModal}
          action="Add Daily Log"
          selectedContractor={subDailyData}
          subcontractorData={subcontractorData}
          setSubDailyUpdate={setSubDailyUpdate}
          subDailyDataDropDown={subDailyDataDropDown}
          isSubEditDailyLog={isSubEditDailyLog}
          fetchSubContractorToApprove={fetchSubContractorToApprove}
        />
      </CustomModal>

      <CustomModal show={showTimeOffModal}>
        <TimeOffRequest
          setShowTimeOffModal={setShowTimeOffModal}
          allMembersList={allMembers}
          setDataUpdate={setDataUpdate}
        />
      </CustomModal>
      <CustomModal show={showEditTimeOffModal}>
        <EditTimeOffRequest
          data={ptoData}
          setShowEditTimeOffModal={setShowEditTimeOffModal}
          setDataUpdate={setDataUpdate}
        />
      </CustomModal>
    </>
  )
}

export default memo(ApproveTimeCards)
