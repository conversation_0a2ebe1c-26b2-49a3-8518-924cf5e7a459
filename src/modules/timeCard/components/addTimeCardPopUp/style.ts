import { Field, FieldArray } from 'formik'
import styled from 'styled-components'
import { colors, screenSizes } from '../../../../styles/theme'
import { SettingModalHeaderContainer } from '../../../units/components/newUnitModal/style'
import { FlexCol, SettingModalContentContainer } from '../../../../styles/styled'
import { DropDownOuterContainer } from '../../../../shared/customSelect/style'
import { Nue } from '../../../../shared/helpers/constants'

export const TimeCardPopUpContainer = styled.div`
  background: ${colors.white};
  width: 600px;
  height: 100%;
  border-radius: 10px;
  @media (max-width: 768px) {
    width: 90vw;
  }

  .extra-container {
    * {
      height: 52px;
    }
    input {
      padding: 12px;
      max-width: 40px;
    }
  }
`

export const ModalHeaderContainer = styled(SettingModalHeaderContainer)``

export const ModalHeader = styled.h5`
  margin: 0;
  line-height: 1.5;
  font-size: 20px;
  font-weight: 600;
  color: ${colors.darkGrey};
`

export const CrossContainer = styled.div`
  cursor: pointer;
  svg {
    width: 25px;
    height: 25px;
    svg path {
      stroke: ${colors.darkGrey};
    }
    :hover {
      svg path {
        stroke: ${colors.grey};
      }
      transform: scale(1.03);
    }
    transition: all 0.01s linear;
  }
`

export const LabelDiv = styled.label<any>`
  font-size: 14px;
  font-weight: 500;
  color: ${colors.darkGrey};
  margin-top: ${(props) => props.marginTop};
  text-align: ${(props) => props.textAlign};
  width: ${(props) => props.width};
  cursor: ${(props) => props.cursor};
  @media (min-width: ${screenSizes.M}px) {
    font-size: 16px;
  }
`

export const CheckBox = styled(Field)<any>`
  width: ${(props) => (props.width ? props.width : '100%')};
  height: ${(props) => (props.height ? props.height : '48px')};
  margin-top: ${(props) => props.marginTop};
  cursor: pointer;
`

export const TextArea = styled(Field)<any>`
  width: ${(props) => (props.width ? props.width : '100%')};
  height: ${(props) => (props.height ? props.height : '48px')};
  cursor: pointer;
  resize: vertical;
  outline: none;
  border: 1px solid ${colors.lightGray};
  border-radius: 8px;
  padding: 12px 18px;
  :focus {
    border: 1px solid ${colors.lightBlue1};
    box-shadow: ${colors.lightBlue} 0px 0px 5px 0px;
  }
  margin-top: ${(props) => props.$marginTop};
  &.error {
    border: 1px solid #c24f44;
    background: #f9f2f2;
  }
`

export const CheckBoxDescription = styled.p<any>`
  margin: 0;
  font-size: 12px;
  font-weight: 500;
  margin-top: ${(props) => props.marginTop};
  text-align: ${(props) => props.textAlign};
  @media (min-width: ${screenSizes.M}px) {
    font-size: 16px;
  }
`

export const WorkDoneContainer = styled(FieldArray)<any>`
  /* width: 100%;
  border-radius: 8px;
  padding: 6px;
  margin-top: ${(props) => props.marginTop};
  border: 1px solid ${colors.darkGrey}; */
`

export const UnitDiv = styled.div`
  padding: 4px 12px;
  background-color: ${colors.lightGrey3};
  border-radius: 0px 4px 4px 0px;
  border: 1px solid ${colors.lightGrey6};
  font-size: 12px;
  color: ${colors.lightGrey4};
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
`

export const NameValueUnitContainer = styled.div<any>`
  width: ${(props) => props.width};
  display: flex;
  align-items: center;
  justify-content: ${(props) => props.justifyContent};
  margin-top: ${(props) => props.marginTop};
  flex-wrap: ${(props) => props.flexWrap};
  gap: ${(props) => (props.gap ? props.gap : '8px')};
  justify-content: flex-start;

  p {
    width: 156px;
  }

  &:nth-child(4) {
    p {
      text-align: left;
      width: 156px;
    }
  }

  @media (min-width: ${screenSizes.XS}px) {
    justify-content: flex-end;
    p {
      width: max-content;
    }

    &:nth-child(4) {
      p {
        text-align: right;
        width: 156px;
      }
    }
  }

  ${UnitDiv} {
    padding: 4px;
  }

  input {
    padding: 3.7px 4px;
    max-width: 35px;
  }
`

export const ValueInput = styled(Field)<any>`
  padding: 2.7px 10px;
  outline: none;
  max-width: 50px;
  color: ${colors.lightGrey4};
  border: 1px solid ${colors.lightGrey};
  width: ${(props) => props.width};
  ::-webkit-outer-spin-button,
  ::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  :focus {
    border: 1px solid ${colors.lightBlue1};
    box-shadow: ${colors.lightBlue} 0px 0px 5px 0px;
    /* background-color: ${colors.white}; */
  }
  border-radius: 4px 0px 0px 4px;
  margin-left: ${(props) => props.marginLeft};
`

export const TimecardCont = styled.div`
  .piecework {
    width: 100%;
    select {
      margin: 8px 0 0 0;
    }
    button {
      margin-top: 10px;
    }

    & > div {
      margin-bottom: -3px;

      @media (max-width: 560px) {
        flex-wrap: wrap;
      }
    }

    @media (max-width: 560px) {
      ${DropDownOuterContainer} {
        max-width: 100%;
      }
    }
  }

  .info {
    span {
      font-size: 16px;
      font-family: ${Nue.bold};
    }
    p {
      font-size: 14px;
      margin-top: 4px;
      color: ${colors.gray1};
    }
  }
  .top-padding {
    padding-top: 0;
  }
`

export const SwitchFromCont = styled(FlexCol)`
  gap: 14px;
  align-items: center;
  width: 100%;
`
export const SwitchToCont = styled(FlexCol)`
  gap: 14px;
  align-items: center;
  width: 100%;
`
