import { Form, Formik } from 'formik'
import { useCallback, useEffect, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate, useParams } from 'react-router-dom'
import * as Yup from 'yup'
import { getProfileInfo } from '../../logic/apis/profile'
import { getPackageApi, getProjectTypes, getUnitsApi } from '../../logic/apis/projects'
import { CustomModal } from '../../shared/customModal/CustomModal'
import { Dropdown } from '../../shared/dropDown/Dropdown'
import { StorageKey, TIME_ZONES } from '../../shared/helpers/constants'
import { onlyText } from '../../shared/helpers/regex'
import { getDataFromLocalStorage, getNameFromId, isSuccess, notify } from '../../shared/helpers/util'
import { InputWithValidation } from '../../shared/inputWithValidation/InputWithValidation'
import { Table } from '../../shared/table/Table'
import * as SharedStyled from '../../styles/styled'
import { IType } from '../newProject/NewProject'
import AddPackagesModal from './addPackagesModal/AddPackagesModal'
import * as Styled from './style'
import TabBar from '../../shared/components/tabBar/TabBar'
import { SettingsCont, ButtonCont } from '../units/style'
import DeletedPackages from './deletedPackages/DeletedPackages'
import Button from '../../shared/components/button/Button'
import DropdownWithCheckboxesWithoutFormik from '../../shared/dropdownWithCheckboxesWithoutFormik/DropdownWithCheckboxesWithoutFormik'

/**
 * InitialValues is an interface declared here so that it can be used as a type for the useState hook
 */
interface InitialValues {
  firstName: string
  lastName: string
  preferredName: string
  // username: string
  email: string
  roles: string
}

const Packages = () => {
  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */
  const [showCreatePackage, setShowCreatePackage] = useState<boolean>(false)
  const [packageType, setPackageType] = useState('')
  const [types, setTypes] = useState<IType[]>([])
  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company
  const [initialValues, setInitialValues] = useState<InitialValues>({
    firstName: '',
    lastName: '',
    preferredName: '',
    // username: '',
    email: '',
    roles: '',
  })

  /**
   * This showChangePasswordModal is a boolean state which will be used to change the state of the modal
   */
  const [showChangePasswordModal, setShowChangePasswordModal] = useState<boolean>(false)
  const [units, setUnits] = useState([])
  const [packageTableValues, setPackageTableValues] = useState([])
  const [editPackageVals, setEditPackageVals] = useState<any>(null)

  /**
   * loading will be the loading state when performing the operations
   */
  const [loading, setLoading] = useState<boolean>(false)
  const loadmoreRef = useRef(null)
  const [selectedOptions, setSelectedOptions] = useState<any[]>([])
  const [timeZ, setTimeZ] = useState<string>(TIME_ZONES[0])
  const fetchIdRef = useRef(0)
  // const [pageCount, setPageCount] = useState<number>(10)
  const [data, setData] = useState<any[]>([])
  // const [dataDefaultTrue, setDataDefaultTrue] = useState<any>({})
  const [detailsUpdate, setDetailsUpdate] = useState(false)

  const navigate = useNavigate()
  const { isLoggedIn } = globalSelector.auth
  const { companies } = globalSelector.company

  /**
   * ProfileSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */
  const ProfileSchema = Yup.object().shape({
    firstName: Yup.string()
      .min(2, 'Too Short!')
      .max(50, 'Too Long!')
      .required('Required')
      .matches(onlyText, 'Enter Valid Name'),
    lastName: Yup.string()
      .min(2, 'Too Short!')
      .max(50, 'Too Long!')
      .required('Required')
      .matches(onlyText, 'Enter Valid Name'),
    preferredName: Yup.string().min(2, 'Too Short!').max(50, 'Too Long!').matches(onlyText, 'Enter Valid Name'),
    // username: Yup.string().min(2, 'Too Short!').max(50, 'Too Long!').required('Required'),
    email: Yup.string().email('Invalid email').required('Required'),
    roles: Yup.string(),
  })

  const inputColumns = [
    {
      Header: 'Package Name',
      accessor: 'name',
    },
    {
      Header: 'Order',
      accessor: 'order',
    },
    // {
    //   Header: 'Default',
    //   accessor: 'default',
    // },
    {
      Header: 'Project Type',
      accessor: 'type',
      Cell: ({ value }: { value: string }) => getNameFromId(value, types),
    },
    {
      Header: 'Group',
      accessor: 'group',
    },
  ]

  useEffect(() => {
    initFetch()
    fetchUnitsData()
  }, [])

  const fetchUnitsData = async () => {
    try {
      const res = await getUnitsApi({ deleted: false })
      if (isSuccess(res)) {
        const { unit } = res.data.data
        setUnits(unit)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('Failed init fetch', err)
    }
  }

  const initFetch = async () => {
    try {
      const res = await getProjectTypes({ deleted: false })
      if (isSuccess(res)) {
        const { projectType } = res.data.data
        const object = projectType.map(({ _id, name, groups }: { _id: string; name: string; groups: string[] }) => ({
          name: name,
          id: _id,
          value: _id,
          label: name,
          groups: groups,
        }))
        // setProjectTypes(projectType)
        setTypes(object)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('Failed init fetch', err)
    }
  }

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */

  // const handleSubmit = async (submittedValues: InitialValues) => {
  //   setLoading(true)
  //   try {
  //     let data: any = { ...submittedValues }
  //     delete data.roles
  //     const response = await updateProfile(id, data)
  //     if (isSuccess(response)) {
  //       notify('Updated Profile Successfully', 'success')
  //       setLoading(false)
  //     } else {
  //       notify(response?.data?.message, 'error')
  //       setLoading(false)
  //     }
  //   } catch (error) {
  //     console.error('Profile handleSubmit', error)
  //     setLoading(false)
  //   }
  // }

  // useEffect(() => {
  //   if (currentCompany && currentCompany._id) {
  //     fetchPackage()
  //   }
  // }, [currentCompany])

  // const fetchPackage = async () => {
  //   try {
  //     const res = await getPackageApi({ companyId: currentCompany._id, deleted: false })
  //     if (isSuccess(res)) {
  //       const { packageDetail } = res?.data?.data
  //       const tableData = packageDetail.reduce((prev: any, cur: any) => {
  //         return [
  //           ...prev,
  //           {
  //             ...cur,
  //             name: cur.name,
  //             type: cur.type,
  //             order: cur.order,
  //             group: cur.group,
  //             default: `${cur.default}`,
  //             // labor: cur.labor.tLabor,
  //             // material: cur.material.tMat,
  //           },
  //         ]
  //       }, [])
  //       setPackageTableValues(tableData)
  //     } else throw new Error(res?.data?.message)
  //   } catch (error) {
  //     console.log('init fetch failed!', error)
  //   }
  // }

  const fetchData = useCallback(
    async ({ pageSize, pageIndex }: any) => {
      try {
        // This will get called when the table needs new data
        setLoading(true)
        let receivedData: any = []
        const currentCompany: any = getDataFromLocalStorage('currentCompany')

        const clientResponse = await getPackageApi(
          { deleted: false, limit: pageSize },
          selectedOptions?.map((v) => v._id)?.join(',')
        )

        if (isSuccess(clientResponse)) {
          const { packageDetail } = clientResponse?.data?.data
          // setDataDefaultTrue(packageDetail.find((v: any) => v.default === true))

          const tableData = packageDetail.reduce((prev: any, cur: any) => {
            return [
              ...prev,
              {
                ...cur,
                name: cur.name,
                type: cur.type,
                order: cur.order,
                group: cur.group,
                // default: `${cur.default}`,
                // labor: cur.labor.tLabor,
                // material: cur.material.tMat,
              },
            ]
          }, [])

          receivedData.push(...tableData)
        } else {
          notify(clientResponse?.data?.message, 'error')
        }

        // Give this fetch an ID
        const fetchId = ++fetchIdRef.current

        // Set the loading state
        // setLoading(true)

        // We'll even set a delay to simulate a server here
        // setTimeout(() => {
        // Only update the data if this is the latest fetch
        if (fetchId === fetchIdRef.current) {
          const startRow = pageSize * pageIndex
          const endRow = startRow + pageSize
          setData(receivedData.slice(startRow, endRow))
          // Your server could send back total page count.
          // For now we'll just fake it, too
          // setPageCount(Math.ceil(receivedData.length / pageSize))
          // setLoading(false)
        }
        // }, 1000)
      } catch (error) {
        console.error('TeamTable fetchData error', error)
      } finally {
        setLoading(false)
      }
    },
    [detailsUpdate, selectedOptions]
  )

  const getDetails = async () => {
    try {
      getProfileInfo()
      const response = await getProfileInfo()
      if (isSuccess(response)) {
        let user = response?.data?.data?.user
        let userObject = {
          firstName: user.firstName,
          lastName: user.lastName,
          preferredName: user.preferredName,
          // username: user.username,
          email: user.email,
          roles: 'madmin',
        }
        setInitialValues({ ...initialValues, ...userObject })
      } else {
        notify(response?.data?.message, 'error')
      }
    } catch (error) {
      console.error('getDetails error', error)
    }
  }

  useEffect(() => {
    getDetails()
  }, [])

  return (
    <SettingsCont gap="24px">
      <SharedStyled.FlexRow justifyContent="space-between">
        <SharedStyled.SectionTitle>Packages</SharedStyled.SectionTitle>
        <ButtonCont>
          <Button
            onClick={() => {
              setShowCreatePackage(true)
              setPackageType('add')
            }}
          >
            Add Package
          </Button>
        </ButtonCont>
      </SharedStyled.FlexRow>

      <SharedStyled.FlexRow alignItems="flex-start">
        <SharedStyled.FlexCol gap="24px">
          <TabBar
            tabs={[
              {
                title: 'Active',
                render: () => (
                  <Table
                    noOverflow
                    columns={inputColumns}
                    data={data}
                    loading={loading}
                    fetchData={fetchData}
                    onRowClick={(vals) => {
                      setShowCreatePackage(true)
                      setEditPackageVals(vals)
                    }}
                    noSearch
                    minWidth=""
                    noBorder
                    ref={loadmoreRef}
                    isLoadMoreLoading={loading}
                  />
                ),
              },
              {
                title: 'Deleted',
                render: () => <DeletedPackages />,
              },
            ]}
            filterComponent={
              <>
                <SharedStyled.FlexRow justifyContent="flex-end">
                  <DropdownWithCheckboxesWithoutFormik
                    options={types?.map((v) => ({ name: v.name, _id: v.id })) || []}
                    selectedOptions={selectedOptions} // Pass local state
                    onChange={(selected) => setSelectedOptions(selected)} // Update local state
                  />
                </SharedStyled.FlexRow>
              </>
            }
          />
        </SharedStyled.FlexCol>
      </SharedStyled.FlexRow>
      <CustomModal show={showCreatePackage} className="top">
        <AddPackagesModal
          setShowCreatePackage={setShowCreatePackage}
          onClose={() => {
            setShowCreatePackage(false)
            setEditPackageVals(null)
          }}
          header={!!editPackageVals ? `Edit Package` : `Add Package`}
          inputData={editPackageVals}
          isEditing={!!editPackageVals}
          packageType={packageType}
          setPackageType={setPackageType}
          onComplete={() => {
            fetchData({
              pageIndex: 0,
              pageSize: 20,
            })
          }}
          types={types}
          units={units}
          // dataDefaultTrue={dataDefaultTrue || {}}
        />
      </CustomModal>
    </SettingsCont>
  )
}

export default Packages
