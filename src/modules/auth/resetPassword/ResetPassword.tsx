import { Form, Formik } from 'formik'
import { useEffect, useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import * as Yup from 'yup'

import AvtSvg from '../../../assets/newIcons/avatar.svg'
import SuccessSvg from '../../../assets/newIcons/success.svg'
import { resetPassword } from '../../../logic/apis/auth'
import { isPasswordValid } from '../../../shared/helpers/regex'
import { isSuccess, notify } from '../../../shared/helpers/util'
import { InputWithValidation } from '../../../shared/inputWithValidation/InputWithValidation'
import * as SharedStyled from '../../../styles/styled'
import * as Styled from './style'
import { HeaderCont, Logo, OtherOptionsDiv, OtherOptionsText } from '../signin/style'
import { ModalHeader } from '../../taxJurisdiction/taxJurisdictionModal/style'
import { signinPath } from '../../../logic/paths'
import { nhrLogoSvg } from '../../../shared/helpers/images'

/**
 * InitialValues is an interface declared here so that it can be used as a type for the useState hook
 */
interface InitialValues {
  newPassword: string
  confirmPassword: string
}

/**
 *
 * @returns A ResetPassword component with all the validations to its input fields
 */
const ResetPassword = () => {
  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */
  const [initialValues, setInitialValues] = useState<InitialValues>({
    newPassword: '',
    confirmPassword: '',
  })

  const [isResetSuccess, setIsResetSuccess] = useState(false)

  const navigate = useNavigate()

  /**
   * resetToken is the token which we get from url when we click the reset password link which was sent to our mail
   */
  const [resetToken, setResetToken] = useState<any>('')

  /**
   * loading will be the loading state when performing the operations
   */
  const [loading, setLoading] = useState<boolean>(false)

  let { token: token1 } = useParams()

  useEffect(() => {
    setResetToken(token1)
  }, [token1])
  /**
   * ResetPasswordSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */
  const ResetPasswordSchema = Yup.object().shape({
    newPassword: Yup.string()
      .required('No password provided.')
      .matches(
        isPasswordValid,
        'Password should be minimum eight characters, at least one uppercase letter, one lowercase letter, one number and one special character'
      ),
    confirmPassword: Yup.string()
      .oneOf([Yup.ref('newPassword'), null], 'Passwords must match')
      .required('Required'),
  })

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async (submittedValues: InitialValues, { resetForm }: any) => {
    setLoading(true)
    try {
      let password = submittedValues.confirmPassword
      let token: any = resetToken
      const response = await resetPassword({ token, password })
      if (isSuccess(response)) {
        notify('Your password has been reset successfully', 'success')
        setLoading(false)
        setIsResetSuccess(true)
        resetForm()
        navigate('/signin')
      } else {
        notify(response?.data?.message, 'error')
        setLoading(false)
      }
    } catch (error) {
      console.error('ResetPassword handleSubmit error', error)
      setLoading(false)
    }
  }

  const modalTitle = isResetSuccess ? 'Password Reset Successful' : 'Reset Password'
  const icon = isResetSuccess ? SuccessSvg : AvtSvg

  return (
    <Formik
      initialValues={initialValues}
      onSubmit={handleSubmit}
      validationSchema={ResetPasswordSchema}
      validateOnChange={true}
      validateOnBlur={false}
    >
      {({ values, errors, touched }) => {
        return (
          <Styled.ResetpasswordContainer className="top">
            <SharedStyled.FlexCol alignItems="center" gap="30px">
              <Logo src={'https://pieceworkpro.s3.us-west-1.amazonaws.com/Admin/pieceworkpro.svg'} alt="nhr logo" />
              <SharedStyled.SettingModalContentContainer>
                <Form className="form">
                  <SharedStyled.Content maxWidth="500px" width="100%" borderRadius="8px">
                    <SharedStyled.ContentHeader>
                      <HeaderCont>
                        <img src={icon} alt="modal icon" />
                        <SharedStyled.FlexCol>
                          <ModalHeader>{modalTitle}</ModalHeader>
                        </SharedStyled.FlexCol>
                      </HeaderCont>
                    </SharedStyled.ContentHeader>

                    {isResetSuccess && (
                      <Styled.Info>
                        You have successfully updated your password. You can now Sign In with your new password.
                      </Styled.Info>
                    )}

                    {!isResetSuccess && (
                      <SharedStyled.FlexCol gap="8px">
                        <InputWithValidation
                          type="password"
                          labelName="New Password"
                          stateName="newPassword"
                          value={values.newPassword}
                          error={touched.newPassword && errors.newPassword ? true : false}
                        />
                        <InputWithValidation
                          type="password"
                          labelName="Confirm Password"
                          stateName="confirmPassword"
                          value={values.confirmPassword}
                          error={touched.confirmPassword && errors.confirmPassword ? true : false}
                        />
                      </SharedStyled.FlexCol>
                    )}
                    <SharedStyled.ButtonContainer marginTop="20px">
                      {isResetSuccess ? (
                        <SharedStyled.Button
                          type="button"
                          onClick={() => {
                            navigate(signinPath)
                          }}
                        >
                          Sign in
                        </SharedStyled.Button>
                      ) : (
                        <SharedStyled.Button type="submit">
                          {loading ? (
                            <>
                              Resetting Password
                              <SharedStyled.Loader />
                            </>
                          ) : (
                            'Reset Password'
                          )}
                        </SharedStyled.Button>
                      )}
                    </SharedStyled.ButtonContainer>

                    {!isResetSuccess && (
                      <OtherOptionsDiv marginTop="16px">
                        <OtherOptionsText>
                          <span onClick={() => navigate(signinPath)}>Back to Sign In</span>
                        </OtherOptionsText>
                      </OtherOptionsDiv>
                    )}
                  </SharedStyled.Content>
                </Form>
              </SharedStyled.SettingModalContentContainer>
            </SharedStyled.FlexCol>
          </Styled.ResetpasswordContainer>
        )
      }}
    </Formik>
  )
}

export default ResetPassword
