import { Form, Formik } from 'formik'
import { useEffect, useState } from 'react'
import { useDispatch } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import * as Yup from 'yup'

import CreateSvg from '../../../assets/newIcons/account.svg'
import { authenticate, signup } from '../../../logic/apis/auth'
import { getAllCompanies } from '../../../logic/apis/company'
import { getInvitationDetails } from '../../../logic/apis/invitation'
import { setIsLoggedIn } from '../../../logic/redux/actions/auth'
import { setIsInvited } from '../../../logic/redux/actions/invitation'
import { isPasswordValid, onlyText, userName } from '../../../shared/helpers/regex'
import { isSuccess, notify, simplifyBackendError } from '../../../shared/helpers/util'
import { InputWithValidation } from '../../../shared/inputWithValidation/InputWithValidation'
import * as SharedStyled from '../../../styles/styled'
import { colors } from '../../../styles/theme'
import * as Styled from './style'
import { HeaderCont, Logo, OtherOptionsDiv, OtherOptionsText } from '../signin/style'
import { ModalHeader } from '../../taxJurisdiction/taxJurisdictionModal/style'
import { profilePath, signinPath } from '../../../logic/paths'
import { nhrLogoSvg } from '../../../shared/helpers/images'
import Button from '../../../shared/components/button/Button'
import { SharedPhone } from '../../../shared/sharedPhone/SharedPhone'
import { SharedDate } from '../../../shared/date/SharedDate'
import '../../../shared/helpers/yupExtension'
import { InvitationStatusEnum } from '../../../shared/helpers/constants'

/**
 * InitialValues is an interface declared here so that it can be used as a type for the useState hook
 */
interface InitialValues {
  firstName: string
  lastName: string
  // username: string
  email: string
  password: string
  confirmPassword: string
  phone?: string
  hireDate: string
}

/**
 *
 * @returns A SignUp component with all the validations to its input fields
 */
const Signup = () => {
  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */
  const [initialValues, setInitialValues] = useState<InitialValues>({
    firstName: '',
    lastName: '',
    // username: '',
    email: '',
    password: '',
    confirmPassword: '',
    phone: '',
    hireDate: '',
  })

  /**
   * loading will be the loading state when performing the operations
   */
  const [loading, setLoading] = useState<boolean>(false)

  const [rejectloading, setRejectLoading] = useState<boolean>(false)

  const [data, setData] = useState<any>({})
  const [preferredName, setPreferredName] = useState('')

  const navigate = useNavigate()
  const dispatch = useDispatch()
  const { state } = useLocation()

  const getInvitationDetail = async () => {
    if (state !== null && state?.invitationId) {
      try {
        let id: any = state.invitationId
        const response = await getInvitationDetails(id)
        if (isSuccess(response)) {
          let res = response?.data?.data?.invitation
          setInitialValues({
            ...initialValues,
            firstName: res.firstName,
            lastName: res.lastName,
            email: res.email,
            phone: res?.phone,
            hireDate: res?.hireDate,
          })
        } else {
          notify(response?.data?.message, 'error')
        }
      } catch (error) {
        console.error('getInviteDetail error', error)
      }
    }
  }

  /**
   * SignUpSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */
  const SignUpSchema = Yup.object().shape({
    firstName: Yup.string()
      .min(1, 'Too Short!')
      .max(50, 'Too Long!')
      .required('Required')
      .matches(onlyText, 'Enter Valid Name'),
    lastName: Yup.string()
      .min(1, 'Too Short!')
      .max(50, 'Too Long!')
      // .required('Required')
      .matches(onlyText, 'Enter Valid Name'),
    // username: Yup.string()
    //   .required('Required')
    //   .min(2, 'Too Short!')
    //   .max(50, 'Too Long!')
    //   .matches(userName, 'Enter Valid Username'),
    email: Yup.string().trimEmail().email('Invalid email').required('Required'),
    password: Yup.string()
      .required('No password provided.')
      .matches(
        isPasswordValid,
        'Password should be minimum eight characters, at least one uppercase letter, one lowercase letter, one number and one special character'
      ),
    confirmPassword: Yup.string()
      .oneOf([Yup.ref('password'), null], 'Passwords must match')
      .required('Required'),
  })

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async (submittedValues: InitialValues) => {
    try {
      setLoading(true)
      // if (state !== null && state?.invitationId) {
      //   let isAccepted = await onAcceptingInvite()
      //   if (!isAccepted) {
      //     return -1
      //   }
      // }
      submittedValues.email = submittedValues.email.trim()

      const response = await signup({
        signupDto: {
          ...submittedValues,
          hireDate: submittedValues.hireDate ? new Date(submittedValues.hireDate).toISOString() : undefined,
          preferredName: preferredName ? preferredName : undefined,
        },
        invitationResponseDto: {
          ...data,
          status: InvitationStatusEnum.Accepted,
        },
      })

      if (isSuccess(response)) {
        authenticate(
          response?.data?.data.access_token,
          response?.data?.data.refresh_token,
          response?.data?.data?.user.exp,
          response?.data?.data?.user?._id
        )
        await getCompanies(response?.data?.data?.user?._id)
        let inviteValue: any = localStorage.getItem('isInvited')
        // if (JSON.parse(inviteValue)) {
        //   navigate(profilePath)
        // } else {
        //   navigate(`/company`)
        // }
        navigate(profilePath)

        dispatch(setIsLoggedIn(true))
        setLoading(false)
        if (state !== null && state?.invitationId) {
          notify('Invitation accepted and account created successfully', 'success')
        } else {
          notify('Account Created Successfully', 'success')
        }
      } else {
        // notify(simplifyBackendError(response?.data?.data?.message), 'error')
        setLoading(false)
      }
    } catch (error) {
      console.error('SignUp handleSubmit error', error)
      setLoading(false)
    }
  }

  const onAcceptingInvite = async () => {
    try {
      const id: any = state?.invitationId
      setLoading(true)
      // const response = await acceptRejectInvitation({ ...data, status: 2 })
      // if (isSuccess(response)) {
      //   return true
      //   // notify('Accepted invitation successfully', 'success')
      //   // setLoading(false)
      // } else {
      //   notify(response?.data?.message, 'error')
      //   setLoading(false)
      //   return false
      // }
    } catch (error) {
      console.error('onAcceptingInvite error', error)
      setLoading(false)
      return false
    }
  }

  const onRejectingInvite = async (resetForm: any) => {
    try {
      setRejectLoading(true)
      // const response = await acceptRejectInvitation({ ...data, status: 3 })
      // if (isSuccess(response)) {
      //   notify('Invitation rejected successfully', 'success')
      //   setRejectLoading(false)
      //   resetForm()
      //   navigate(`/signup`, { replace: true })
      // } else {
      //   notify(response?.data?.message, 'error')
      //   setRejectLoading(false)
      // }
    } catch (error) {
      console.error('onDecliningInvite error', error)
      setRejectLoading(false)
    }
  }

  const getInviteDetail = async () => {
    try {
      let id: any = state?.invitationId
      const response = await getInvitationDetails(id)
      if (isSuccess(response)) {
        let res = response?.data?.data?.invitation
        setData({
          senderEmail: res.senderEmail,
          recipientEmail: res.email,
          company: res.company,
          status: res.status,
        })
        setPreferredName(res?.preferredName)
      } else {
        notify(response?.data?.message, 'error')
      }
    } catch (error) {
      console.error('getInviteDetail error', error)
    }
  }

  const getCompanies = async () => {
    let breakException = {}
    try {
      const response = await getAllCompanies()
      if (isSuccess(response)) {
        const allCompanies = response?.data?.data?.companiesData
        allCompanies.forEach((company: any) => {
          if (company.member.invited) {
            dispatch(setIsInvited(true))
            localStorage.setItem('isInvited', JSON.stringify(true))
            throw breakException
          }
        })
      } else {
        notify(response?.data?.message, 'error')
      }
    } catch (error) {
      if (error !== breakException) {
        console.error('getCompanies error', error)
      }
    }
  }

  useEffect(() => {
    getInvitationDetail()
  }, [])

  useEffect(() => {
    getInviteDetail()
  }, [])

  return (
    <Formik
      initialValues={initialValues}
      onSubmit={handleSubmit}
      validationSchema={SignUpSchema}
      validateOnChange={true}
      validateOnBlur={false}
      enableReinitialize={true}
    >
      {({ values, errors, touched, resetForm, setFieldValue, handleChange }) => {
        return (
          <Styled.SignupContainer className="top">
            <SharedStyled.FlexCol alignItems="center" gap="30px">
              <Logo src={nhrLogoSvg} alt="nhr logo" />
              <SharedStyled.SettingModalContentContainer>
                <Form className="form">
                  <SharedStyled.Content maxWidth="500px" width="100%" borderRadius="8px">
                    <SharedStyled.ContentHeader>
                      <HeaderCont>
                        <img src={CreateSvg} alt="modal icon" />
                        <SharedStyled.FlexCol>
                          <ModalHeader>Create Account</ModalHeader>
                        </SharedStyled.FlexCol>
                      </HeaderCont>
                    </SharedStyled.ContentHeader>
                    <SharedStyled.FlexCol gap="8px">
                      <SharedStyled.TwoInputDiv>
                        <InputWithValidation
                          labelName="First Name"
                          stateName="firstName"
                          twoInput={true}
                          error={touched.firstName && errors.firstName ? true : false}
                        />
                        <InputWithValidation
                          labelName="Last Name"
                          stateName="lastName"
                          twoInput={true}
                          error={touched.lastName && errors.lastName ? true : false}
                        />
                      </SharedStyled.TwoInputDiv>
                      {/* <InputWithValidation
                        labelName="Username"
                        stateName="username"
                        error={touched.username && errors.username ? true : false}
                      /> */}
                      <InputWithValidation
                        labelName="Email"
                        stateName="email"
                        error={touched.email && errors.email ? true : false}
                      />
                      <InputWithValidation
                        type="password"
                        labelName="Password"
                        stateName="password"
                        value={values.password}
                        error={touched.password && errors.password ? true : false}
                        showValidationCard={true}
                      />
                      <InputWithValidation
                        type="password"
                        labelName="Confirm Password"
                        stateName="confirmPassword"
                        value={values.confirmPassword}
                        error={touched.confirmPassword && errors.confirmPassword ? true : false}
                        showValidationCard={false}
                      />
                    </SharedStyled.FlexCol>
                    {!state?.invitationId ? (
                      <SharedStyled.ButtonContainer marginTop="20px">
                        <Button type="submit" isLoading={loading}>
                          Submit
                        </Button>
                      </SharedStyled.ButtonContainer>
                    ) : (
                      <SharedStyled.ButtonContainer marginTop="20px">
                        <Button type="submit" isLoading={loading}>
                          Signup to Accept
                        </Button>
                        {/* <Button
                          type="button"
                          color={colors.white}
                          bgColor={colors.error}
                          onClick={() => onRejectingInvite(resetForm)}
                          className="delete"
                          isLoading={rejectloading}
                        >
                          Reject Invitation
                        </Button> */}
                      </SharedStyled.ButtonContainer>
                    )}

                    <OtherOptionsDiv marginTop="16px">
                      <OtherOptionsText>
                        Already have an account ? <span onClick={() => navigate(signinPath)}>Sign In</span>
                      </OtherOptionsText>
                    </OtherOptionsDiv>
                  </SharedStyled.Content>
                </Form>
              </SharedStyled.SettingModalContentContainer>
            </SharedStyled.FlexCol>
          </Styled.SignupContainer>
        )
      }}
    </Formik>
  )
}

export default Signup
