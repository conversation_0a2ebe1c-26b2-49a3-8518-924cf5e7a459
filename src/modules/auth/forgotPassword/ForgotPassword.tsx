import { Form, Formik } from 'formik'
import { useState } from 'react'
import * as Yup from 'yup'
import { useNavigate } from 'react-router-dom'

import AvtSvg from '../../../assets/newIcons/avatar.svg'
import { forgotPassword } from '../../../logic/apis/auth'
import { signinPath } from '../../../logic/paths'
import But<PERSON> from '../../../shared/components/button/Button'
import { nhrLogoSvg } from '../../../shared/helpers/images'
import { isSuccess, notify } from '../../../shared/helpers/util'
import { InputWithValidation } from '../../../shared/inputWithValidation/InputWithValidation'
import * as SharedStyled from '../../../styles/styled'
import { ModalHeader } from '../../taxJurisdiction/taxJurisdictionModal/style'
import { HeaderCont, Logo, OtherOptionsDiv, OtherOptionsText } from '../signin/style'
import * as Styled from './style'
import '../../../shared/helpers/yupExtension'

/**
 * InitialValues is an interface declared here so that it can be used as a type for the useState hook
 */
interface InitialValues {
  email: string
}

/**
 *
 * @returns A ForgotPassword component with all the validations to its input fields
 */
const ForgotPassword = () => {
  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */
  const [initialValues, setInitialValues] = useState<InitialValues>({
    email: '',
  })

  /**
   * loading will be the loading state when performing the operations
   */
  const [loading, setLoading] = useState<boolean>(false)
  const navigate = useNavigate()

  /**
   * ForgotPasswordSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */
  const ForgotPasswordSchema = Yup.object().shape({
    email: Yup.string().trimEmail().email('Invalid email').required('Required'),
  })

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async (submittedValues: InitialValues, { resetForm }: any) => {
    setLoading(true)
    try {
      submittedValues.email = submittedValues.email.trim()
      const response = await forgotPassword(submittedValues)
      if (isSuccess(response)) {
        notify(`Sent reset link to ${submittedValues.email}`, 'success')
        setLoading(false)
        resetForm()
      } else {
        notify(response?.data?.message, 'error')
        setLoading(false)
      }
    } catch (error) {
      console.error('ForgotPassword handleSubmit error', error)
      setLoading(false)
    }
  }

  return (
    <Formik
      initialValues={initialValues}
      onSubmit={handleSubmit}
      validationSchema={ForgotPasswordSchema}
      validateOnChange={true}
      validateOnBlur={false}
    >
      {({ errors, touched }) => {
        return (
          <Styled.ForgotPasswordContainer className="top">
            <SharedStyled.FlexCol alignItems="center" gap="30px">
              <Logo src={'https://pieceworkpro.s3.us-west-1.amazonaws.com/Admin/pieceworkpro.svg'} alt="nhr logo" />
              <SharedStyled.SettingModalContentContainer>
                <Form className="form">
                  <SharedStyled.Content maxWidth="500px" width="100%" borderRadius="8px">
                    <SharedStyled.ContentHeader>
                      <HeaderCont>
                        <img src={AvtSvg} alt="modal icon" />
                        <SharedStyled.FlexCol>
                          <ModalHeader>Forgot Password</ModalHeader>
                        </SharedStyled.FlexCol>
                      </HeaderCont>
                    </SharedStyled.ContentHeader>

                    <InputWithValidation
                      labelName="Email"
                      stateName="email"
                      error={touched.email && errors.email ? true : false}
                    />
                    <SharedStyled.ButtonContainer marginTop="20px">
                      <Button type="submit" isLoading={loading}>
                        Send Reset Link
                      </Button>
                    </SharedStyled.ButtonContainer>
                    <OtherOptionsDiv marginTop="16px">
                      <OtherOptionsText>
                        <span onClick={() => navigate(signinPath)}>Back to Sign In</span>
                      </OtherOptionsText>
                    </OtherOptionsDiv>
                  </SharedStyled.Content>
                </Form>
              </SharedStyled.SettingModalContentContainer>
            </SharedStyled.FlexCol>
          </Styled.ForgotPasswordContainer>
        )
      }}
    </Formik>
  )
}

export default ForgotPassword
