import React, { useEffect, useState } from 'react'
import { Field, Form, Formik } from 'formik'
import * as Yup from 'yup'
import * as SharedStyled from '../../styles/styled'
import { CrossIcon } from '../../assets/icons/CrossIcon'
import UnitSvg from '../../assets/newIcons/unitModal.svg'
import { InputWithValidation } from '../../shared/inputWithValidation/InputWithValidation'
import { useParams } from 'react-router-dom'
import { useSelector } from 'react-redux'
import { lostOpportunity, unlostOpportunity } from '../../logic/apis/sales'
import { getDataFromLocalStorage, isSuccess, notify, startOfDate } from '../../shared/helpers/util'
import Button from '../../shared/components/button/Button'
import { SharedDateAndTime } from '../../shared/date/SharedDateAndTime'
import { lostContactLead, unlostContactLead } from '../../logic/apis/contact'
import { CrossContainer, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>HeaderContainer, StepModalContainer } from './style'
import CustomSelect from '../../shared/customSelect/CustomSelect'
import { IntendWidth } from '../contact/style'
import { SubOption } from './InvalidLeadModal'

interface I_LostModal {
  onClose: any
  onComplete: any

  setIsLost: any
  // fetchActivity: () => void
  // initFetchContact: () => Promise<void>
  lead: any
  activeLead: any
}

interface I_initialValues {
  reason: string
  date: string
  lostNote: string
}

export const lostReasons: Record<string, { subOptions?: SubOption[] }> = {
  'Too Expensive': {
    subOptions: [{ label: 'Explain', field: 'notes' }],
  },
  'Price Shopping': {
    subOptions: [{ label: 'Explain', field: 'notes' }],
  },
  'Went With Other Provider': {
    subOptions: [{ label: 'Who did they go with?', field: 'notes' }],
  },
  'Discuss With Partner': {
    subOptions: [{ label: 'Explain', field: 'notes' }],
  },
  'Wants to Wait': {
    subOptions: [{ label: "What's their timeframe?", field: 'notes' }],
  },
  'Ghosted/Unreachable': {
    subOptions: [{ label: 'How many times did you reach out?', field: 'notes' }],
  },
  Other: {
    subOptions: [{ label: 'Lost lead notes', field: 'notes' }],
  },
}

const LostLeadModal: React.FC<I_LostModal> = (props) => {
  const { onClose, onComplete, lead, activeLead } = props
  const initialValues: I_initialValues = {
    reason: '',
    date: new Date().toISOString(),
    lostNote: '',
  }

  const { contactId } = useParams()
  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company
  const [loading, setLoading] = useState(false)
  interface CheckpointDates {
    [key: string]: string
  }

  const lostSchema = Yup.object().shape({
    lostNote: !lead ? Yup.string().required('Required').min(2, 'Too Short!') : Yup.string().required('Required'),
    reason: !lead ? Yup.string().required('Required') : Yup.string().notRequired(),
  })

  const handleSubmit = async (values: typeof initialValues) => {
    setLoading(true)

    try {
      const response = await (!lead
        ? lostContactLead(
            {
              reason: values?.reason,
              notes: values?.lostNote?.trim(),
              date: new Date().toISOString(),
              memberId: currentMember._id!,
            },
            activeLead._id!
          )
        : unlostContactLead(
            {
              reason: values.lostNote?.trim(),
              date: new Date().toISOString(),
              memberId: currentMember._id!,
            },
            lead._id!
          ))
      if (isSuccess(response)) {
        onComplete()
        notify(`${lead ? 'Unlost' : 'Lost'} Lead!`, 'success')
        setLoading(false)
      } else throw new Error(response?.data?.message)
    } catch (err) {
      console.log(err)
    } finally {
      onClose()
      setLoading(false)
    }
  }

  return (
    <StepModalContainer>
      <Formik
        initialValues={initialValues}
        enableReinitialize={true}
        onSubmit={handleSubmit}
        validationSchema={lostSchema}
        validateOnChange={true}
        validateOnBlur={false}
      >
        {({ values, errors, touched, resetForm, setFieldValue, handleChange, handleSubmit }) => {
          useEffect(() => {
            if (values?.reason) {
              setFieldValue('lostNote', '')
            }
          }, [values?.reason])
          return (
            <>
              <ModalHeaderContainer>
                <SharedStyled.FlexRow>
                  <img src={UnitSvg} alt="modal icon" />
                  <SharedStyled.FlexCol>
                    <ModalHeader>{lead ? 'Unlost' : 'Lost'} Lead</ModalHeader>
                  </SharedStyled.FlexCol>
                </SharedStyled.FlexRow>
                <CrossContainer
                  onClick={() => {
                    resetForm()
                    onClose()
                  }}
                >
                  <CrossIcon />
                </CrossContainer>
              </ModalHeaderContainer>
              <SharedStyled.SettingModalContentContainer>
                <Form className="form">
                  <SharedStyled.Content maxWidth="706px" width="100%" disableBoxShadow={true} noPadding={true}>
                    {!lead ? (
                      <>
                        <SharedDateAndTime
                          value={values.date}
                          labelName="Date"
                          stateName="date"
                          setFieldValue={setFieldValue}
                          error={touched.date && errors.date ? true : false}
                        />

                        <CustomSelect
                          labelName="Lost Reason"
                          stateName="reason"
                          value={values?.reason || ''}
                          error={!!(touched?.reason && errors?.reason)}
                          setFieldValue={setFieldValue}
                          setValue={() => {}}
                          dropDownData={Object.keys(lostReasons)}
                          innerHeight="52px"
                          margin="10px 0 0 0"
                        />

                        {values?.reason &&
                          lostReasons[values.reason as keyof typeof lostReasons]?.subOptions?.some(
                            (option: SubOption) => option.field === 'notes'
                          ) && (
                            <IntendWidth>
                              <InputWithValidation
                                labelName={
                                  lostReasons[values.reason as keyof typeof lostReasons]?.subOptions?.find(
                                    (option: SubOption) => option.field === 'notes'
                                  )?.label || 'Notes*'
                                }
                                stateName="lostNote"
                                error={touched.lostNote && errors.lostNote ? true : false}
                              />
                            </IntendWidth>
                          )}
                      </>
                    ) : (
                      <InputWithValidation
                        labelName="Lost Reason Notes"
                        stateName="lostNote"
                        error={touched.lostNote && errors.lostNote ? true : false}
                      />
                    )}

                    <SharedStyled.ButtonContainer marginTop="20px">
                      <Button type="submit" maxWidth="150px" isLoading={loading}>
                        Submit
                      </Button>
                    </SharedStyled.ButtonContainer>
                  </SharedStyled.Content>
                </Form>
              </SharedStyled.SettingModalContentContainer>
            </>
          )
        }}
      </Formik>
    </StepModalContainer>
  )
}

export default LostLeadModal
