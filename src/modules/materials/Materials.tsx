import React, { useCallback, useEffect, useRef, useState } from 'react'
import * as SharedStyled from '../../styles/styled'
import { Table } from '../../shared/table/Table'
import { getCatergory<PERSON>pi, getMaterialsApi, getSubCatergory<PERSON><PERSON>, getUnitsApi } from '../../logic/apis/projects'
import { useNavigate, useParams } from 'react-router-dom'
import { useSelector } from 'react-redux'
import { getDataFromLocalStorage, getUnitSymbolFromId, isSuccess, notify } from '../../shared/helpers/util'
import { CustomModal } from '../../shared/customModal/CustomModal'
import MaterialsModal, { VENDORS, vendorDrop } from './components/materialsModal/MaterialsModal'
import CategoryModal from '../category/components/categoryModal/CategoryModal'
import SubcategoryModal from '../category/components/subcategoryModal/SubcategoryModal'
import { Button<PERSON>ont, SettingsCont } from '../units/style'
import Button from '../../shared/components/button/Button'
import TabBar from '../../shared/components/tabBar/TabBar'
import DeletedMaterials from '../deleted/deletedMaterials/DeletedMaterials'
import { MaterialsCont } from './components/materialsModal/style'
import CustomSelect from '../../shared/customSelect/CustomSelect'
import { StorageKey } from '../../shared/helpers/constants'
import { VendorEnum } from '../orderDetails/constant'

export const sortMaterials = (materials: any) => {
  return materials?.sort((a: any, b: any) => {
    // Compare by categoryName
    if (a.categoryName[0] < b.categoryName[0]) return -1
    if (a.categoryName[0] > b.categoryName[0]) return 1

    // If categoryName is the same, compare by subCategoryName
    if (a.subCategoryName[0] < b.subCategoryName[0]) return -1
    if (a.subCategoryName[0] > b.subCategoryName[0]) return 1

    // If subCategoryName is the same, compare by name
    return a.name.localeCompare(b.name)
  })
}

const Materials = () => {
  const columns = [
    {
      Header: 'Category',
      accessor: 'category',
      Cell: ({ row, value }: { row: any; value: string }) => {
        const projectTasks = row.original.projectTasks // Access packagesTasks from the row data
        const isEmpty = Array.isArray(projectTasks) && projectTasks.length === 0
        return <span style={{ fontWeight: isEmpty ? 'bold' : 'normal' }}>{value}</span>
      },
    },
    {
      Header: 'Subcategory',
      accessor: 'subcategory',
      Cell: ({ row, value }: { row: any; value: string }) => {
        const projectTasks = row.original.projectTasks // Access packagesTasks from the row data
        const isEmpty = Array.isArray(projectTasks) && projectTasks.length === 0
        return <span style={{ fontWeight: isEmpty ? 'bold' : 'normal' }}>{value}</span>
      },
    },
    {
      Header: 'Material Name',
      accessor: 'materialName',
      Cell: ({ row, value }: { row: any; value: string }) => {
        const projectTasks = row.original.projectTasks // Access packagesTasks from the row data
        const isEmpty = Array.isArray(projectTasks) && projectTasks.length === 0
        return <span style={{ fontWeight: isEmpty ? 'bold' : 'normal' }}>{value}</span>
      },
    },
    {
      Header: 'Unit',
      accessor: 'unitId',
      Cell: ({ row, value }: { row: any; value: string }) => {
        const projectTasks = row.original.projectTasks // Access packagesTasks from the row data
        const isEmpty = Array.isArray(projectTasks) && projectTasks.length === 0
        return (
          <span style={{ fontWeight: isEmpty ? 'bold' : 'normal' }}>
            {getUnitSymbolFromId(value, unitData).split(' ')[0]}
          </span>
        )
      },
      // Cell: ({ value }: { value: string }) => getUnitSymbolFromId(value, unitData).split(' ')[0],
    },
    {
      Header: () => <SharedStyled.TextAlign position={'center'}>Cost/Unit</SharedStyled.TextAlign>,
      accessor: 'cost',
      Cell: ({ row, value }: { row: any; value: number }) => {
        const projectTasks = row.original.projectTasks // Access packagesTasks from the row data
        const isEmpty = Array.isArray(projectTasks) && projectTasks.length === 0
        return <span style={{ fontWeight: isEmpty ? 'bold' : 'normal' }}>{formatNumber(value, 2)}</span>
      },
      // Cell: ({ value }: any) => (
      //   <SharedStyled.TextAlign position={'end'}>{formatNumber(value, 2)}</SharedStyled.TextAlign>
      // ),
      headerStyle: { textAlign: 'center' },
    },
    {
      Header: 'Vendor',
      accessor: 'vendor',
      Cell: ({ row, value }: { row: any; value: string }) => {
        const projectTasks = row.original.projectTasks // Access packagesTasks from the row data
        const isEmpty = Array.isArray(projectTasks) && projectTasks.length === 0
        return <span style={{ fontWeight: isEmpty ? 'bold' : 'normal' }}>{value}</span>
      },
    },
    {
      Header: 'Inventory',
      accessor: 'inventory',
      Cell: ({ row, value }: { row: any; value: string }) => {
        const projectTasks = row.original.projectTasks // Access packagesTasks from the row data
        const isEmpty = Array.isArray(projectTasks) && projectTasks.length === 0
        return <span style={{ fontWeight: isEmpty ? 'bold' : 'normal' }}>{value}</span>
      },
    },
  ]

  const navigate = useNavigate()
  const globalSelector = useSelector((state: any) => state)
  const { currentCompany } = globalSelector.company

  const [filterCategory, setFilterCategory] = useState({ name: '', _id: undefined })
  const [filterSubcategory, setFilterSubcategory] = useState({ name: '', _id: undefined })

  const [materials, setMaterials] = useState<any>([])
  const [inputData, setInputData] = useState<any>([])
  const [materialModal, setMaterialModal] = useState(false)

  const [categoryModal, setCategoryModal] = useState(false)
  const [subCategoryModal, setSubCategoryModal] = useState(false)
  const [lastLimit, setLastLimit] = useState(20)

  // modal data
  const [catData, setCatData] = useState<any>([])
  const [subCatData, setSubCatData] = useState<any>([])
  const [unitData, setUnitData] = useState<any>([])

  const fetchIdRef = useRef(0)
  const [pageCount, setPageCount] = useState<number>(10)
  const [data, setData] = useState<any[]>([])
  const [filteredData, setFilteredData] = useState<any[]>([])
  const [detailsUpdate, setDetailsUpdate] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')

  const isFetched = useRef(false)

  const loadmoreRef = useRef(null)
  const [loading, setLoading] = useState<boolean>(false)

  useEffect(() => {
    initFetch()
  }, [])

  useEffect(() => {
    let filteredArray = data

    if (filterCategory?.name !== 'All' && filterCategory?.name) {
      filteredArray = filteredArray?.filter((itm) => itm?.category === filterCategory?.name)
    }

    if (filterSubcategory?.name !== 'All' && filterSubcategory?.name) {
      filteredArray = filteredArray?.filter((itm) => itm?.subcategory === filterSubcategory?.name)
    }

    if (searchTerm) {
      const searchLower = searchTerm?.toLowerCase()

      filteredArray = filteredArray?.filter((itm) => {
        const fieldsToSearch = [
          itm.category,
          itm.subcategory,
          itm.materialName,
          itm.unitSymbol,
          itm.cost,
          itm.vendor,
          itm.inventory,
        ]

        return fieldsToSearch.some((field) => field && String(field)?.toLowerCase().includes(searchLower))
      })
    }

    setFilteredData(filteredArray)
  }, [searchTerm, filterCategory?.name, filterSubcategory?.name, data])

  const fetchData = useCallback(
    async ({ search }: { search?: string }) => {
      if (search) {
        setSearchTerm(search!)
      }

      try {
        if (!isFetched?.current) {
          let receivedData: any = []

          setLoading(true)
          const currentCompany: any = getDataFromLocalStorage('currentCompany')

          const clientResponse = await getMaterialsApi({
            deleted: false,
            // subCategoryId:
            //   filterSubcategory?.name !== 'All' && filterSubcategory?.name ? filterSubcategory?._id : undefined,
            // categoryId: filterCategory?.name !== 'All' && filterCategory?.name ? filterCategory?._id : undefined,
          })

          if (isSuccess(clientResponse)) {
            const { material } = clientResponse?.data?.data

            material.forEach((item: any) => {
              receivedData.push({
                ...item,
                category: getObjFromId(catData, item.categoryId).name,
                subcategory: getObjFromId(subCatData, item.subCategoryId).name,
                materialName: item.name,
                unit: getObjFromId(unitData, item.unitId).name,
                unitSymbol: getObjFromId(unitData, item.unitId).symbol,
                vendor: vendorDrop[item.vendor - 1],
                inventory: item.inv ? 'Yes' : 'No',
              })
            })

            // receivedData.push(...receivedData)
          } else {
            notify(clientResponse?.data?.message, 'error')
          }

          // Set the loading state
          // setLoading(true)

          // We'll even set a delay to simulate a server here
          // setTimeout(() => {
          // Only update the data if this is the latest fetch

          // setData(receivedData.slice(startRow, endRow))

          const responseData = sortMaterials(receivedData)

          setData(responseData)

          isFetched.current = true
        } else {
          setSearchTerm(search!)
        }
      } catch (error) {
        console.error('TeamTable fetchData error', error)
      } finally {
        setLoading(false)
      }
    },
    [
      detailsUpdate,
      catData.length,
      subCatData.length,
      unitData.length,
      // filterCategory, filterSubcategory
    ]
  )

  const formatNumber = (number: number, decimalPlaces: number) => {
    return new Intl.NumberFormat(undefined, {
      minimumFractionDigits: decimalPlaces,
      maximumFractionDigits: decimalPlaces,
    }).format(number)
  }

  const initFetch = async () => {
    fetchCategories()
    fetchSubCategories()
    fetchUnits()
  }

  const fetchCategories = async () => {
    try {
      const res = await getCatergoryApi({ deleted: false })
      if (isSuccess(res)) {
        setCatData(res.data.data.category)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('init fetch failed!', err)
    }
  }

  const fetchSubCategories = async () => {
    try {
      const res = await getSubCatergoryApi({ deleted: false })
      if (isSuccess(res)) {
        setSubCatData(res.data.data.subCategory)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('init fetch failed!', err)
    }
  }

  const fetchUnits = async () => {
    try {
      const res = await getUnitsApi({ deleted: false, limit: 1000 })
      if (isSuccess(res)) {
        setUnitData(res.data.data.unit)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('init fetch failed!', err)
    }
  }

  // const fetchMaterials = async () => {
  //   try {
  //     const res = await getMaterialsApi({ companyId: currentCompany._id, deleted: false })
  //     if (isSuccess(res)) {
  //       console.log({ initFetch: res })
  //       const { material } = res.data.data
  //       let tableData: any = []
  //       material.forEach((item: any) => {
  //         tableData.push({
  //           ...item,
  //           category: getObjFromId(catData, item.categoryId).name,
  //           subcategory: getObjFromId(subCatData, item.subCategoryId).name,
  //           materialName: item.name,
  //           unit: getObjFromId(unitData, item.unitId).name,
  //           vendor: vendorDrop[item.vendor - 1],
  //           inventory: item.inv ? 'Yes' : 'No',
  //         })
  //       })
  //       setMaterials(tableData)
  //     } else throw new Error(res?.data?.message)
  //   } catch (err) {
  //     console.log('init fetch failed!', err)
  //   }
  // }

  const getObjFromId = (obj: any[], id: string) => {
    let [res] = obj.filter((item: any) => {
      return item._id === id
    })
    return res ?? { name: '' }
  }

  return (
    <MaterialsCont gap="24px">
      <SharedStyled.FlexRow justifyContent="space-between">
        <SharedStyled.SectionTitle>Materials</SharedStyled.SectionTitle>
        <ButtonCont>
          <Button
            onClick={() => {
              setMaterialModal(true)
            }}
          >
            Add Material
          </Button>
        </ButtonCont>
      </SharedStyled.FlexRow>

      <SharedStyled.FlexRow alignItems="flex-start">
        <SharedStyled.FlexCol gap="24px">
          <TabBar
            className="end"
            tabs={[
              {
                title: 'Active',
                render: () => (
                  <>
                    {catData.length > 0 && subCatData.length > 0 && unitData.length > 0 ? (
                      <Table
                        columns={columns}
                        data={filteredData}
                        fetchData={fetchData}
                        noBorder
                        // filterLoading={loading}
                        loading={loading}
                        noOverflow
                        onRowClick={(val: any) => {
                          setInputData(val)
                          setMaterialModal(true)
                        }}
                        ref={loadmoreRef}
                        // isLoadMoreLoading={loading}
                        setLastLimit={setLastLimit}
                      />
                    ) : null}
                  </>
                ),
              },
              {
                title: 'Deleted',
                render: () => <DeletedMaterials />,
              },
            ]}
            filterComponent={
              <SharedStyled.FlexRow justifyContent="flex-end">
                <CustomSelect
                  labelName="Filter by category"
                  stateName=""
                  value={filterCategory?.name}
                  dropDownData={[...catData?.map(({ name }: { name: string }) => name), 'All']}
                  setValue={(val: string) => {
                    const extractValue = catData?.find((item: any) => item.name === val)
                    if (!extractValue) {
                      setFilterCategory({ name: 'All', _id: undefined })
                      return
                    }

                    setFilterSubcategory({ name: 'All', _id: undefined })
                    setFilterCategory(extractValue)
                  }}
                  setFieldValue={() => {}}
                  innerHeight="50px"
                  margin="10px 0 0 0"
                  maxWidth="200px"
                />
                <CustomSelect
                  labelName="Filter by subcategory"
                  stateName=""
                  value={filterSubcategory?.name}
                  dropDownData={[
                    ...subCatData
                      ?.filter((itm: { categoryId: string }) =>
                        filterCategory?._id ? itm?.categoryId === filterCategory?._id : true
                      )
                      ?.map(({ name }: { name: string }) => name),
                    'All',
                  ]}
                  setValue={(val: string) => {
                    const extractValue = subCatData?.find((item: any) => item.name === val)
                    if (!extractValue) {
                      setFilterSubcategory({ name: 'All', _id: undefined })
                      return
                    }

                    setFilterSubcategory(extractValue)
                  }}
                  setFieldValue={() => {}}
                  innerHeight="50px"
                  margin="10px 0 0 0"
                  maxWidth="220px"
                />
              </SharedStyled.FlexRow>
            }
          />
        </SharedStyled.FlexCol>
      </SharedStyled.FlexRow>

      <CustomModal show={materialModal}>
        <MaterialsModal
          onClose={() => {
            setMaterialModal(false)
            setInputData(null)
          }}
          catData={catData}
          subCatData={subCatData}
          unitData={unitData}
          onAddCategoryClick={() => {
            setMaterialModal(false)
            setCategoryModal(true)
            setInputData(null)
          }}
          onAddSubCategoryClick={() => {
            setMaterialModal(false)
            setSubCategoryModal(true)
            setInputData(null)
          }}
          onComplete={() => {
            isFetched.current = false
            fetchData({ search: '' })
            setInputData(null)
          }}
          inputData={inputData}
        />
      </CustomModal>
      <CustomModal show={categoryModal}>
        <CategoryModal
          onClose={() => {
            setCategoryModal(false)
          }}
          onComplete={() => {
            setCategoryModal(false)
            fetchCategories()
          }}
        />
      </CustomModal>
      <CustomModal show={subCategoryModal}>
        <SubcategoryModal
          categories={catData}
          onClose={() => {
            setSubCategoryModal(false)
          }}
          onComplete={() => {
            setSubCategoryModal(false)
            fetchSubCategories()
          }}
        />
      </CustomModal>
    </MaterialsCont>
  )
}

export default Materials
