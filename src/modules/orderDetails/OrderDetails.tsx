import React, { useEffect, useRef, useState } from 'react'
import * as Styled from './styles'
import * as SharedStyled from '../../styles/styled'
import { Field, Formik, Form } from 'formik'
import CustomSelect from '../../shared/customSelect/CustomSelect'
import { useNavigate, useParams } from 'react-router-dom'
import { useSelector } from 'react-redux'
import {
  AddMaterialToOrder,
  createOrder,
  getMultipleProject,
  getOptionApi,
  getOrder,
  getOrderById,
  getPackageApi,
  getPriceIdByProject,
  getProjectTypes,
  getUnitsApi,
  permDeleteOrder,
  projectUpdateOrder,
  updateMaterialOrder,
} from '../../logic/apis/projects'
import {
  convertKeyToStr,
  convertStrToKey,
  formatNumberToCommaS,
  formatPhoneNumber,
  generateUUID,
  getDataFromLocalStorage,
  getNameFrom_Id,
  getSymbolFromId,
  getUnitSymbolFromId,
  hasValues,
  isSuccess,
  notify,
  round1,
  roundToNearestTenth,
  sortByOrder,
  toPascalCase,
} from '../../shared/helpers/util'
import { getOpportunityById, updateActivity } from '../../logic/apis/sales'
import { colors } from '../../styles/theme'
import Button from '../../shared/components/button/Button'
import ReactMarkdown from 'react-markdown'
import Toggle from '../../shared/toggle/Toggle'
import { ColorMapping, copyColorsToColor2, InitialValues, VendorEnum } from './constant'
import { object } from 'yup'
import AutoComplete from '../../shared/autoComplete/AutoComplete'
import { ContentBlockTypeEnum, StorageKey } from '../../shared/helpers/constants'
import PrintContract from './components/PrintContract'
import ReactDOMServer from 'react-dom/server'
import { getAllContent, getAllContractsByType } from '../../logic/apis/company'
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd'
import { CustomModal } from '../../shared/customModal/CustomModal'
import AddNewMaterialForOrder, { filterUnits } from './components/AddNewMaterialForOrder'
import { DeleteIcon } from '../../assets/icons/DeleteIcon'
import { RevokeIcon } from '../../assets/icons/RevokeIcon'
import { SLoader } from '../../shared/components/loader/Loader'
import { ContentData } from '../contractsSetting/ContractsSetting'
import { AddIcon } from '../../assets/icons/AddIcon'

// export const renderColors = (colors: any, selectedProjectType: any) => {
//   console.log({ colors, selectedProjectType })
//   return (
//     colors &&
//     Object?.entries(colors)?.map(([key, value]: any, index: number) => {
//       const [colorId, colorName] = key?.split('@')
//       return (
//         <SharedStyled.FlexRow margin={'5px 0 0 30px'} key={index}>
//           <SharedStyled.Text fontSize="14px">
//             {selectedProjectType?.priceColor?.find((itm: { _id: string }) => itm?._id === colorId)?.name ?? colorName}:{' '}
//             {value}
//           </SharedStyled.Text>
//         </SharedStyled.FlexRow>
//       )
//     })
//   )
// }

export const renderColors = (colors: any, selectedProjectType: any) => {
  if (!colors || !selectedProjectType?.priceColor) return null

  return selectedProjectType.priceColor.map((colorCategory: any, index: number) => {
    const key = `${colorCategory._id}@${colorCategory.name}`
    const selectedColor = colors[key]

    return (
      <SharedStyled.FlexRow margin={'5px 0 0 30px'} key={index}>
        <SharedStyled.Text fontSize="14px">
          {colorCategory.name}: {selectedColor || 'N/A'}
        </SharedStyled.Text>
      </SharedStyled.FlexRow>
    )
  })
}

export function sortTasks(tasks: any) {
  return tasks?.sort((a, b) => {
    const groupA = a.group ?? ''
    const groupB = b.group ?? ''
    const orderA = a.order ?? 0
    const orderB = b.order ?? 0

    if (groupA === '' && groupB !== '') {
      return 1
    }
    if (groupA !== '' && groupB === '') {
      return -1
    }
    if (groupA === groupB) {
      return orderA - orderB
    }
    return groupA.localeCompare(groupB)
  })
}

export function sortInventoryMats(mats: any) {
  return mats?.sort((a, b) => {
    if (a?.ProjectType === '' && b?.ProjectType !== '') {
      return -1
    }
    if (a?.ProjectType !== '' && b?.ProjectType === '') {
      return 1
    }
    if (a?.ProjectType === b?.ProjectType) {
      return 0
    }
    return a?.ProjectType?.localeCompare(b?.ProjectType)
  })
}

export const renderCrew = (totalHours: number) => {
  return (
    <SharedStyled.FlexCol gap="5px" margin="10px 0 0 0">
      <SharedStyled.Text fontWeight="600" fontSize="14px">
        Crew Budget: {round1(totalHours)} hrs
      </SharedStyled.Text>

      <SharedStyled.Text margin="0 0 0 10px" fontSize="14px">
        {' '}
        3 on Crew: ~{round1(totalHours / 24)} days
      </SharedStyled.Text>
      <SharedStyled.Text margin="0 0 0 10px" fontSize="14px">
        {' '}
        4 on Crew: ~{round1(totalHours / 32)} days
      </SharedStyled.Text>
      <SharedStyled.Text margin="0 0 0 10px" fontSize="14px">
        {' '}
        5 on Crew: ~{round1(totalHours / 40)} days
      </SharedStyled.Text>
    </SharedStyled.FlexCol>
  )
}

const resetLocalStorageData = (orderId: string) => {
  localStorage.removeItem(`material1-${orderId}`)
  localStorage.removeItem(`material2-${orderId}`)
  localStorage.removeItem(`tempMaterial-${orderId}`)
}

const OrderDetails = () => {
  const [averagePitch, setAveragePitch] = useState(0)
  const [toggleColors, setToggleColors] = useState(false)
  const [loading, setloading] = useState(true)
  const [componentLoading, setComponentLoading] = useState('')
  const [toggleNotes, setToggleNotes] = useState(false)
  const [isSupplier, setIsSupplier] = useState(false)
  const [type, setType] = useState(false)
  const [contracts, setContracts] = useState([])
  const [unitData, setUnitData] = useState<any>([])
  const [isAddMode, setIsAddMode] = useState(false)
  const unitRef = useRef<any>(null)
  const [inventoryData, setInventoryData] = useState([])
  const [nonInventoryData, setNonInventoryData] = useState([])
  const [isRestored, setIsRestored] = useState(false)

  const { oppId, orderId } = useParams()
  const [contractOrder, setContractOrder] = useState<any>([])
  const [orderById, setOrderById] = useState<any>([])
  const [material1, setMaterial1] = useState<any>([])

  const [saveLoading, setSaveLoading] = useState(false)

  const [material2, setMaterial2] = useState<any>([])
  const [material3, setMaterial3] = useState<any>([])
  const [opportunity, setOpportunity] = useState<any>([])
  const [btnLoading, setBtnLoading] = useState(false)
  const [printSupplierFlag, setPrintSupplierFlag] = useState(false)
  const [printCrewSheet, setPrintCrewSheet] = useState(false)
  const [printLoading, setPrintLoading] = useState(true)
  const [thours, setThours] = useState(0)
  const [materialToggle, setMaterialToggle] = useState(false)
  const [toggleitem, setToggleItem] = useState<{ [key: string]: boolean }>({})
  const [finePrintContent, setFinePrintContent] = useState<ContentData>()
  const [projectTypes, setProjectTypes] = useState([])
  const [priceByProject, setPriceByProject] = useState<any>()
  const [showAddModal, setShowAddModal] = useState(false)

  const [toggleHeading, setToggleHeading] = useState<{ [key: string]: boolean }>({})
  const [editableName, setEditableName] = useState('')
  const [editableAmount, setEditableAmount] = useState('')
  const [handleLoading, setHandleLoading] = useState(false)

  const [editableUnit, setEditableUnit] = useState('')
  const [projectId, setProjectId] = useState('')
  const [editingCell, setEditingCell] = useState<{ id: string; field: string; droppableId?: string } | null>(null)
  const [tempMaterial, setTempMaterial] = useState<any>([])

  const globalSelector = useSelector((state: any) => state)
  const { positionDetails, currentMember, companySettingForAll } = globalSelector.company
  // manHourRate
  const navigate = useNavigate()
  const operationsFlag = location.pathname.includes('operations')
  const [showBreakdown, setShowBreakdown] = useState<{ [key: string]: boolean }>({})

  const position = positionDetails?.symbol
  const salesDataVisibleTo = ['Owner', 'GeneralManager', 'SalesManager']
  const [initialValues, setInitialValues] = useState<InitialValues>({
    oppId: '',
    projectId: '',
    projectPriceId: '',
    projectName: '',
    projectType: '',
    priceTotals: {},
    matList: [],
    workOrder: [],
    createdBy: '',
    isUpdate: true,
    orderId: orderId!,
    projects: [{}],
  })

  const material1Draft = getDataFromLocalStorage(`material1-${orderId}`)
  const material2Draft = getDataFromLocalStorage(`material2-${orderId}`)

  useEffect(() => {
    const restoreDrafts = () => {
      if (isRestored) return

      const tempMatData = getDataFromLocalStorage(`tempMaterial-${orderId}`)
      setTempMaterial(tempMatData)

      if (material1Draft?.length && inventoryData?.length) {
        const activeItem = material1Draft?.filter((item: any) => item?.isNew)?.[0]

        setIsSupplier(!activeItem?.inventory)
        setEditableName(activeItem?.nameEdit || '')
        setIsAddMode(true)
        setProjectId(activeItem?.projectId)
        setMaterial1(material1Draft)
        setIsRestored(true)
        return
      }

      if (material2Draft?.length && nonInventoryData?.length) {
        const activeItem = material2Draft?.filter((item: any) => item?.isNew)?.[0]
        setEditableName(activeItem?.nameEdit || '')
        setIsAddMode(true)
        setIsSupplier(!activeItem?.inventory)
        setProjectId(activeItem?.projectId)
        setMaterial2(material2Draft)
        setIsRestored(true)
      }
    }

    if (inventoryData?.length && nonInventoryData?.length) {
      restoreDrafts()
    }
  }, [material1Draft, material2Draft, inventoryData, nonInventoryData, isRestored])

  const materialTotal = Number(orderById?.priceTotals?.mTotal?.toFixed(2))
  const grossProfit = Number(
    (
      orderById?.priceTotals?.jobTotal -
      (orderById?.priceTotals?.lTotal + orderById?.priceTotals?.commission + materialTotal)
    )?.toFixed(2)
  )
  const jobCost = Number(
    (materialTotal + orderById?.priceTotals?.lTotal + orderById?.priceTotals?.commission)?.toFixed(2)
  )

  const newPriceTotalsData = [
    {
      label: 'Job Subtotal',
      value: orderById?.priceTotals?.jobTotal?.toFixed(2),
      percent: ((orderById?.priceTotals?.jobTotal / orderById?.priceTotals?.jobTotal) * 100)?.toFixed(1),
    },
    {
      label: 'Total Materials',
      value: materialTotal,

      percent: ((materialTotal / orderById?.priceTotals?.jobTotal) * 100)?.toFixed(1),
      clickable: true,
    },
    {
      label: 'Actual Revenue',
      value: orderById?.priceTotals?.actRev?.toFixed(2),
      percent: ((orderById?.priceTotals?.actRev / orderById?.priceTotals?.jobTotal) * 100)?.toFixed(1),
    },
    {
      label: 'Total Labor',
      value: orderById?.priceTotals?.lTotal?.toFixed(2),
      percent: ((orderById?.priceTotals?.lTotal / orderById?.priceTotals?.jobTotal) * 100)?.toFixed(1),
      clickable: true,
    },
    {
      label: 'Commission',
      value: orderById?.priceTotals?.commission?.toFixed(2),
      percent: ((orderById?.priceTotals?.commission / orderById?.priceTotals?.jobTotal) * 100)?.toFixed(1),
    },
    {
      label: 'Job Cost',
      value: jobCost,
      percent: ((jobCost / orderById?.priceTotals?.jobTotal) * 100)?.toFixed(1),
    },
    {
      label: 'Gross Profit',
      value: grossProfit,
      percent: ((grossProfit / orderById?.priceTotals?.jobTotal) * 100)?.toFixed(1),
      clickable: true,
    },

    {
      label: 'Sales Tax',
      value: orderById?.priceTotals?.salesTax?.toFixed(2),
      percent: ((orderById?.priceTotals?.salesTax / orderById?.priceTotals?.jobTotal) * 100)?.toFixed(1),
    },
    {
      label: 'Gross Profit of Act Rev',
      value: '-',
      percent: ((grossProfit / orderById?.priceTotals?.actRev) * 100)?.toFixed(1),
      clickable: false,
    },
    {
      label: 'Labor of Act Rev',
      value: '-',
      percent: ((orderById?.priceTotals?.lTotal / orderById?.priceTotals?.actRev) * 100)?.toFixed(1),
      clickable: false,
    },
  ]

  const materialsBreakdown = [
    {
      label: 'Materials',
      value: orderById?.priceTotals?.matCost?.toFixed(2),
    },
    {
      label: 'Mat Tax',
      value: orderById?.priceTotals?.matTax?.toFixed(2),
    },
    {
      label: 'Mat Markup',
      value: orderById?.priceTotals?.mMarkup?.toFixed(2),
    },
    {
      label: 'Permit',
      value: orderById?.priceTotals?.permit?.toFixed(2),
    },
    {
      label: 'Asbestos Test',
      value: orderById?.priceTotals?.asbTest?.toFixed(2),
    },
  ]

  const laborBreakdown = [
    {
      label: 'Labor Subtotal',
      value: orderById?.priceTotals?.lSubtotal?.toFixed(2),
    },
    {
      label: 'Labor Burden',
      value: orderById?.priceTotals?.lBurden?.toFixed(2),
    },
  ]

  const laborSubtotalBreakdown = [
    {
      label: 'Raw Labor',
      value: orderById?.priceTotals?.laborCost?.toFixed(2),
    },
    {
      label: 'Travel Fee',
      value: orderById?.priceTotals?.travelFee?.toFixed(2),
    },
  ]

  const profilBreakdown = [
    {
      label: 'Overhead',
      value: orderById?.priceTotals?.overhead?.toFixed(2),
    },
    {
      label: 'Profit',
      value: orderById?.priceTotals?.profit?.toFixed(2),
    },
    {
      label: 'Upsell',
      value: orderById?.priceTotals?.upsell?.toFixed(2),
    },
    {
      label: 'Finance Modifier',
      value: (orderById?.priceTotals?.financeTotal - orderById?.priceTotals?.cashTotal)?.toFixed(2),
    },
    {
      label: 'Discounts',
      value: orderById?.priceTotals?.discount?.toFixed(2),
    },
    {
      label: 'Commission Discount',
      value: orderById?.priceTotals?.discountComm?.toFixed(2) ?? 0,
    },
  ]

  // useEffect(() => {
  //   window?.scrollTo(0, 0)
  // }, [])

  const contentRef = useRef<HTMLDivElement | null>(null)

  console.log({ priceByProject })
  const handlePrintPage = () => {
    // Render the PdfContent component as a static HTML string
    const contentHtml = ReactDOMServer.renderToStaticMarkup(
      <PrintContract
        order={orderById}
        projectTypes={projectTypes}
        opportunity={opportunity}
        companySettingForAll={companySettingForAll}
        priceByProject={priceByProject}
        contracts={contracts}
        finePrintContent={finePrintContent}
      />
    )
    // Open a new window or tab
    const printWindow = window.open('', '_blank')

    if (printWindow) {
      // Create HTML content for the new tab, including CSS for styling
      printWindow.document.write(`
        <html>
          <head>
            <title>Print Preview</title>
            <style>
           
            @page { /*To show custom page count*/
              size: auto;
      
              @bottom-right  {
                content: "Page " counter(page) " of " counter(pages);
                font-size:10px;
                }
      }

              body {
                font-family: Arial, sans-serif;
                margin: 20px;
                padding: 0;
                -webkit-print-color-adjust: exact;
              }
              .flex-box {
                display: flex;
                justify-content: space-between;
                // align-items: center;
              }
              .grid-box {
                display: grid;
                grid-template-columns: repeat(2, 38%) 24%; /* Added a semicolon here */
              }
              
              .flex-box img {
                width: 300px; /* Adjust logo size if needed */
              }
              .text-end {
                text-align: end;
              }
              .text-start {
                text-align: start;
              }
              .text-center {
                text-align: center;
              }
              .text-des{
                margin: 20px 0;
                padding: 10px;
                background-color: #F4EEDA !important;
                color: #b89b57 !important;
              }
              .cost-summary {
                margin-bottom: 20px;
                span{
                  font-size:14px;
                }
            }
            .cost-summary div {
                display: flex;
                justify-content: flex-end; /* Aligns items to the right */
               
            }
            .grid-box-sign {
              display: grid;
              grid-template-columns: repeat(2, 1fr);
              gap: 50px; /* Adds spacing between sections */
              margin-top: 10px;
            }
            
            .signature-section {
              display: flex;
              flex-direction: column;
            }
            .fine-print{
              p{
                margin:0
              }
              .ql-align-center {
                text-align: center;
              }
            
              .ql-align-right {
                text-align: right;
              }
            
              .ql-align-justify {
                text-align: justify;
              }
            }
            .line {
              border-bottom: 1px solid #000;
              width: 100%;
              height:20px

            }
            
            .full-width {
              min-width: 200px; /* Ensures uniform line width */
            }
            
            .label {
              font-size: 12px;
              margin-top:2px;
              margin-bottom: 10px;
            }
            
            .flex-col {
              display: flex;
              align-items: center;
              flex-direction:column;
              gap: 15px; /* Creates space between name and line */
            }
            
            .name {
              min-width: 130px; /* Allocates space for names */
              text-align: left;
            }
            .border{
              border:1px solid black;
            }
            .price {
              width: 180px; /* Fixed width for labels */
              text-align: right; /* Align labels to the right */
          }
          .color {
            width: 150px; /* Fixed width for labels */
            text-transform:capitalize;
            // text-align: right; /* Align labels to the right */
        }
              h1 {
                font-size: 24px;
                margin-bottom: 10px;
              }
              p {
                font-size: 18px;
                margin-bottom: 5px;
              }

              hr{
                color:gray;

              }
              .no-margin{
                margin:0;
              }
              li{
                font-weight:bold;
                font-size:14px;
                margin-left:25px;
              }
              .container {

              }
            </style>
          </head>
          <body>
            <div class="container">
              ${contentHtml}
            </div>
            <script>
              // Trigger the print dialog once the content is loaded
              window.onload = function() {
                window.print();
              };
            </script>
          </body>
        </html>
      `)

      // Close the document to complete rendering
      printWindow.document.close()
    }
  }

  useEffect(() => {
    if (orderById?.projectPriceId) {
      fetchPriceByPRoject()
    }
  }, [orderById])

  const fetchPriceByPRoject = async () => {
    try {
      const response: any = await getPriceIdByProject(orderById?.projectPriceId)
      const responseMultiple = await getMultipleProject(oppId!, orderById?.projects?.[0]?.type)
      if (isSuccess(response) && isSuccess(responseMultiple)) {
        const updatedPriceByProject: any = [response?.data?.data?.price, ...responseMultiple?.data?.data?.prices]
        setPriceByProject(updatedPriceByProject)
        setPrintLoading(false)
      }
    } catch (error) {
      console.log(error)
    }
  }

  useEffect(() => {
    if (orderById?.projects?.[0]?.type && Object.entries(opportunity)?.length && contracts?.length) {
      fetchAllContent(orderById?.projects?.[0]?.type)
    }
  }, [orderById?.projects, opportunity, contracts])

  const fetchAllContent = async (projectType: string) => {
    try {
      const res = await getAllContent({ type: ContentBlockTypeEnum.contract }, false)
      if (isSuccess(res)) {
        const { contents } = res?.data?.data
        const selectedContract = contracts?.find((v) => v.projectType === projectType)
        const finePrintId = selectedContract?.finePrint?.[opportunity.state]
        const defaultContent = contents?.find((v) => v._id === finePrintId) || {}
        setFinePrintContent(defaultContent)
      }
    } catch (error) {
      console.log({ error })
    }
  }

  useEffect(() => {
    if (hasValues(orderById)) {
      fetchContractForProjectType()
    }
  }, [orderById])

  const fetchContractForProjectType = async () => {
    try {
      const projectType = orderById?.projects?.map((project) => project.type).join(',') || ''
      const res = await getAllContractsByType(projectType)
      if (isSuccess(res)) {
        const { contracts } = res?.data?.data
        setContracts(contracts?.filter((v) => v?.isDefault))
      }
    } catch (error) {
      console.log({ error })
    }
  }

  const fetchUnitsData = async () => {
    try {
      const res = await getUnitsApi({ deleted: false, limit: 1000 })
      if (isSuccess(res)) {
        setUnitData(res.data.data.unit)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('init fetch failed!', err)
    }
  }

  useEffect(() => {
    fetchOrderById()
    initFetchOpportunity()
    fetchUnitsData()
  }, [])

  useEffect(() => {
    initFetchProjectType()
  }, [loading])
  const initFetchProjectType = async () => {
    try {
      const res = await getProjectTypes({ deleted: false })
      if (isSuccess(res)) {
        const { projectType } = res.data.data
        const project: any = projectType.find((project: any) => project._id === orderById?.projectType)
        setType(project?.typeReplacement)

        setProjectTypes(projectType)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('Failed init fetch', err)
    }
  }

  const fetchOrderById = async () => {
    try {
      // setloading(true)
      const res = await getOrderById(false, orderId!)

      if (isSuccess(res)) {
        const { Order, avgPitch, laborTime } = res?.data?.data

        setThours(laborTime)
        setOrderById(Order)
        setloading(false)
        var inventoryMats = []
        for (var i = 0; i < Order.matList.length; i++) {
          if (Order.matList[i].inventory === true) {
            inventoryMats.push(Order.matList[i])
          }
        }
        var mats1 = []
        for (var i = 0; i < Order.matList.length; i++) {
          if (Order.matList[i].inventory === false) {
            mats1.push(Order.matList[i])
          }
        }
        // var mats2 = []
        // for (var i = 0; i < Order.matList.length; i++) {
        //   if (Order.matList[i].inventory === false && Order.matList[i].vendor === VendorEnum.HomeDepot) {
        //     mats2.push(Order.matList[i])
        //   }
        // }
        inventoryMats = sortInventoryMats(inventoryMats)
        mats1 = sortInventoryMats(mats1)
        // mats2 = sortInventoryMats(mats2)
        // let hours = 0
        // for (let i = 0; i < Order?.workOrder?.length; i++) {
        //   hours += Order.workOrder[i].ttlHours
        // }
        // setTotalLaborTime(hours)

        setMaterial1(inventoryMats)
        setInventoryData(inventoryMats?.sort((a, b) => (a?.sequence || 0) - (b?.sequence || 0)))
        setMaterial2(mats1)
        setNonInventoryData(mats1?.sort((a, b) => (a?.sequence || 0) - (b?.sequence || 0)))

        // setMaterial3(mats2)
        setAveragePitch(avgPitch)
        console.log({ mats1 })
        setInitialValues((prevValues) => ({
          ...prevValues,
          priceTotals: Order?.priceTotals,
          projects: (Order?.projects || []).map((project: any) => ({
            ...project,
            colors2: project.colors, // Set the desired value for color2 here
          })),
        }))
      } else throw new Error(res?.data?.message)
    } catch (error) {
      console.log('order bu id fetch failed!', error)
      setloading(false)
    } finally {
      setComponentLoading('')
      setHandleLoading(false)
    }
  }

  const handleUpdateMaterialOrder = async (data: any[]) => {
    try {
      const response = await updateMaterialOrder(data, orderId!)
      if (isSuccess(response)) {
        notify('Order Material Sequence updated successfully.', 'success')
      } else throw new Error(response?.data?.message)
    } catch (err) {
      console.log('INIT FETCH ERR Order Material Sequance', err)
    }
  }

  const initFetchOpportunity = async () => {
    try {
      const response = await getOpportunityById({
        deleted: false,
        opportunityId: oppId!,
      })
      if (isSuccess(response)) {
        const { opportunity } = response?.data?.data
        setOpportunity(opportunity)
      } else throw new Error(response?.data?.message)
    } catch (err) {
      console.log('INIT FETCH ERR', err)
    }
  }

  const onDelete = async () => {
    setBtnLoading(true)
    try {
      const confirmed = window.confirm('Are you sure? This is permanent and cannot be undone.')
      if (confirmed) {
        const res = await permDeleteOrder({ id: orderId! })
        if (isSuccess(res)) {
          await updateActivity({
            id: oppId!,
            memberId: currentMember._id!,
            body: `deleted Order for ${orderById?.projects?.[0]?.name || ''} project`,
            currDate: new Date().toISOString(),
          })

          notify('Order deleted successfully!', 'success')
          navigate(`/${operationsFlag ? 'operations' : 'sales'}/opportunity/${oppId}`)
        } else throw new Error(res?.data?.message)
      }
    } catch (err) {
      console.log('Order delete error', err)
    } finally {
      setBtnLoading(false)
    }
  }

  const setToggle = (type: string) => {
    setToggleItem((prevState) => ({
      ...prevState,
      [type]: !prevState[type],
    }))
  }

  const handleOrderUpdate = async (
    projectId: string,
    colorsParam: ColorMapping,
    notesParam: string,
    originalMatList?: boolean
  ) => {
    try {
      const colors = Object.entries(colorsParam).length ? colorsParam : undefined
      const notes = notesParam !== '' ? notesParam : undefined

      const res = await projectUpdateOrder({
        orderId,
        projectId,
        colors,
        notes,
        originalMatList,
      })
      if (isSuccess(res)) {
        notify('Order updated successfully!', 'success')
        fetchOrderById()
        // setToggleNotes(false)
        // setToggleColors(false)
      } else throw new Error(res?.data?.message)
    } catch (error) {
      notify('Failed to update order!', 'error')
      console.log('Submit error', error)
    }
  }

  console.log({ material1, material2 })

  const handleDragEnd = async (result: any) => {
    const { source, destination } = result

    // If dropped outside any table, return
    if (!destination) return

    // Parse droppableId to identify the table and projectId
    const parseDroppableId = (id: string) => {
      const firstDashIndex = id.indexOf('-')
      if (firstDashIndex === -1) {
        console.error('Droppable ID is not in the correct format:', id)
        return { table: '', projectId: '' }
      }
      const table = id.substring(0, firstDashIndex)
      const projectId = id.substring(firstDashIndex + 1)
      return { table, projectId }
    }

    const { table: sourceTable, projectId: sourceProjectId } = parseDroppableId(source.droppableId)
    const { table: destinationTable, projectId: destinationProjectId } = parseDroppableId(destination.droppableId)

    console.log('Source:', { sourceTable, sourceProjectId })
    console.log('Destination:', { destinationTable, destinationProjectId })
    // Get source and destination items
    const sourceItems =
      sourceTable === 'material1'
        ? material1
            .filter((item) => item.projectId === sourceProjectId)
            ?.sort((a, b) => (a?.sequence || 0) - (b?.sequence || 0))
        : material2
            .filter((item) => item.projectId === sourceProjectId)
            ?.sort((a, b) => (a?.sequence || 0) - (b?.sequence || 0))

    const destinationItems =
      destinationTable === 'material1'
        ? material1
            .filter((item) => item.projectId === destinationProjectId)
            ?.sort((a, b) => (a?.sequence || 0) - (b?.sequence || 0))
        : material2
            .filter((item) => item.projectId === destinationProjectId)
            ?.sort((a, b) => (a?.sequence || 0) - (b?.sequence || 0))

    // If source items are invalid
    if (sourceItems.length === 0) {
      console.error('No items in source:', sourceItems)
      return
    }

    // Remove the dragged item from source
    const [movedItem] = sourceItems.splice(source.index, 1)

    if (!movedItem) {
      console.error('Moved item is undefined:', source.index, sourceItems)
      return
    }

    // Reordering within the same table
    if (sourceTable === destinationTable && sourceProjectId === destinationProjectId) {
      sourceItems?.splice(destination.index, 0, movedItem)
      console.log('Reordering within the same table', sourceItems, movedItem)

      // Reset the sequence for all items in sourceItems
      const resetSequenceItems = sourceItems.map((item, index) => ({
        ...item,
        sequence: index + 1, // Reset sequence based on the new order
      }))
      // console.log({ source, destination, sourceItems, destinationItems, resetSequenceItems, movedItem })

      const updatedMaterial =
        sourceTable === 'material1'
          ? material1.filter((item) => item.projectId !== sourceProjectId).concat(resetSequenceItems)
          : material2.filter((item) => item.projectId !== sourceProjectId).concat(resetSequenceItems)
      console.log('updatedMaterial', updatedMaterial)

      if (sourceTable === 'material1') {
        setMaterial1(updatedMaterial)
      } else {
        setMaterial2(updatedMaterial)
      }
      if (updatedMaterial?.length) {
        let concatenatedMaterials
        if (sourceTable === 'material1') {
          const updatedMaterial1Mapped = updatedMaterial.map(
            ({ _id, sequence, projectId }: { _id: string; sequence: number; projectId: string }, index: number) => ({
              _id,
              sequence,
              projectId,
              inventory: true,
            })
          )
          concatenatedMaterials = [
            ...updatedMaterial1Mapped,
            // ...material2.map(({ _id }: { _id: string }, index: number) => ({
            //   _id,
            //   inventory: false,
            //   vendor: VendorEnum.Convoy,
            // })),
          ]
        } else {
          const updatedMaterial2Mapped = updatedMaterial.map(
            ({ _id, sequence, projectId }: { _id: string; sequence: number; projectId: string }, index: number) => ({
              _id,
              inventory: false,
              sequence,
              projectId,
              vendor: VendorEnum.Convoy,
            })
          )
          concatenatedMaterials = [
            ...updatedMaterial2Mapped,
            // ...material1.map(({ _id }: { _id: string }, index: number) => ({
            //   _id,
            //   inventory: true,
            //   sequence: index + 1,
            // })),
          ]
        }
        console.log({ concatenatedMaterials })
        await handleUpdateMaterialOrder(concatenatedMaterials)
      }
      return
    }

    // Moving between tables
    console.log('Moving between different tables')
    const resetSequenceItems = sourceItems.map((item, index) => ({
      ...item,
      sequence: index + 1, // Reset sequence based on the new order
    }))
    movedItem.inventory = destinationTable === 'material1'
    destinationItems.splice(destination.index, 0, movedItem)

    const resetDestinationItems = destinationItems.map((item, index) => ({
      ...item,
      sequence: index + 1, // Reset sequence based on the new order
    }))

    const updatedMaterial1 =
      sourceTable === 'material1'
        ? material1.filter((item) => item.projectId !== sourceProjectId).concat(resetSequenceItems)
        : material1.filter((item) => item.projectId !== destinationProjectId).concat(resetDestinationItems)

    const updatedMaterial2 =
      sourceTable === 'material2'
        ? material2.filter((item) => item.projectId !== sourceProjectId).concat(resetSequenceItems)
        : material2.filter((item) => item.projectId !== destinationProjectId).concat(resetDestinationItems)
    setMaterial1(updatedMaterial1)
    setMaterial2(updatedMaterial2)

    const updatedMaterial1Mapped = updatedMaterial1.map(
      ({ _id, sequence, projectId }: { _id: string; sequence: number; projectId: string }, index: number) => ({
        _id,
        sequence,
        projectId,
        inventory: true,
      })
    )

    const updatedMaterial2Mapped = updatedMaterial2.map(
      ({ _id, sequence, projectId }: { _id: string; sequence: number; projectId: string }, index: number) => ({
        _id,
        sequence,
        projectId,
        inventory: false,
        vendor: VendorEnum.Convoy,
      })
    )

    const concatenatedMaterials = [...updatedMaterial1Mapped, ...updatedMaterial2Mapped]
    console.log({
      source,
      destination,
      sourceItems,
      destinationItems,
      resetSequenceItems,
      movedItem,
      updatedMaterial1,
      updatedMaterial2,
      updatedMaterial1Mapped,
      updatedMaterial2Mapped,
    })

    console.log({ concatenatedMaterials })
    await handleUpdateMaterialOrder(concatenatedMaterials)
  }

  console.log({ material1, material2 })
  const materialTable = (materialsProps: any, originalMatList?: boolean, printFlag?: boolean) => {
    const originalMatListFlag = !!originalMatList
    return (
      <Styled.BoxSpacing margin={'0 0 14px 40px'}>
        <Styled.BoxGap width="80%">
          <Styled.BoxGap width="100%" display="flex" border={true}>
            <Styled.BoxGap className="three-table-div1" width={`${materialToggle ? '60%' : '80%'}`}>
              <SharedStyled.Text fontSize="14px">
                <b>Material</b>
              </SharedStyled.Text>
            </Styled.BoxGap>
            <Styled.BoxGap textAlign="center" className="three-table-div2" width="10%">
              <SharedStyled.Text fontSize="14px">
                <b>Amt</b>
              </SharedStyled.Text>
            </Styled.BoxGap>
            <Styled.BoxGap textAlign="center" className="three-table-div3" width="10%">
              <SharedStyled.Text fontSize="14px">
                <b>Unit</b>
              </SharedStyled.Text>
            </Styled.BoxGap>
            {materialToggle && (
              <>
                <Styled.BoxGap textAlign="center" className="three-table-div2" width="10%">
                  <SharedStyled.Text fontSize="14px">
                    <b>Price</b>
                  </SharedStyled.Text>
                </Styled.BoxGap>
                <Styled.BoxGap textAlign="center" className="three-table-div3" width="10%">
                  <SharedStyled.Text fontSize="14px">
                    <b>Total</b>
                  </SharedStyled.Text>
                </Styled.BoxGap>
              </>
            )}
          </Styled.BoxGap>
        </Styled.BoxGap>
        {materialsProps &&
          materialsProps
            ?.filter((v) => !(!!v.deleted && printFlag))
            ?.filter(
              ({ name, unit, amount }: { name: string; unit: string; amount: number }) =>
                !originalMatListFlag || (name && unit && amount != null)
            )
            // ?.filter((v) => {
            //   if (!!v.deleted && printFlag) return false
            //   else return true
            // })
            // ?.filter(({ name, unit, amount }: { name: string; unit: string; amount: number }) => {
            //   if (!originalMatListFlag) return true // Skip the filter if the condition is false
            //   return name && unit && amount != null // Apply the filter logic
            // })
            ?.sort((a, b) => (a?.sequence || 0) - (b?.sequence || 0))
            ?.map(
              (
                {
                  name,
                  unit,
                  amount,
                  cost,
                  nameEdit,
                  unitEdit,
                  amountEdit,
                  deleted,
                }: {
                  name: string
                  unit: string
                  amount: number
                  cost: number
                  nameEdit: string
                  unitEdit: string
                  amountEdit: number
                  deleted: boolean
                },
                index: number
              ) => {
                return (
                  <Styled.BoxGap width="80%" key={index}>
                    <Styled.BoxGap width="100%" display="flex" border={true}>
                      <Styled.BoxGap className="three-table-div1" width={`${materialToggle ? '60%' : '80%'}`}>
                        <SharedStyled.Text
                          textDecoration={
                            (deleted !== undefined ? deleted : false) && !originalMatListFlag ? 'line-through' : 'unset'
                          }
                          fontSize="14px"
                        >
                          {!originalMatListFlag ? nameEdit || name : name}
                        </SharedStyled.Text>
                      </Styled.BoxGap>
                      <Styled.BoxGap textAlign="right" className="three-table-div2" width="10%">
                        <SharedStyled.Text
                          textDecoration={
                            (deleted !== undefined ? deleted : false) && !originalMatListFlag ? 'line-through' : 'unset'
                          }
                          fontSize="14px"
                        >
                          {printSupplierFlag
                            ? Math?.ceil(roundToNearestTenth(!originalMatListFlag ? amountEdit || amount : amount))
                            : roundToNearestTenth(!originalMatListFlag ? amountEdit || amount : amount)?.toFixed(1)}
                          {/* <SharedStyled.Text fontSize="14px">{roundToNearestTenth(amount)?.toFixed(1)}</SharedStyled.Text> */}
                        </SharedStyled.Text>
                      </Styled.BoxGap>
                      <Styled.BoxGap textAlign="center" className="three-table-div3" width="10%">
                        <SharedStyled.Text
                          textDecoration={
                            (deleted !== undefined ? deleted : false) && !originalMatListFlag ? 'line-through' : 'unset'
                          }
                          fontSize="14px"
                        >
                          {!originalMatListFlag ? unitEdit || unit : unit}
                        </SharedStyled.Text>
                      </Styled.BoxGap>
                      {materialToggle && (
                        <>
                          <Styled.BoxGap textAlign="right" className="three-table-div2" width="10%">
                            <SharedStyled.Text
                              textDecoration={
                                (deleted !== undefined ? deleted : false) && !originalMatListFlag
                                  ? 'line-through'
                                  : 'unset'
                              }
                              fontSize="14px"
                            >
                              ${formatNumberToCommaS(cost / amount) || '--'}
                            </SharedStyled.Text>
                          </Styled.BoxGap>
                          <Styled.BoxGap textAlign="right" className="three-table-div3" width="10%">
                            <SharedStyled.Text
                              textDecoration={
                                (deleted !== undefined ? deleted : false) && !originalMatListFlag
                                  ? 'line-through'
                                  : 'unset'
                              }
                              fontSize="14px"
                            >
                              ${formatNumberToCommaS(cost) || '--'}
                            </SharedStyled.Text>
                          </Styled.BoxGap>
                        </>
                      )}
                    </Styled.BoxGap>
                  </Styled.BoxGap>
                )
              }
            )}
      </Styled.BoxSpacing>
    )
  }

  const handleOutSideClickMaterial = async (
    data: [
      { _id: string; projectId: string; nameEdit?: string; amountEdit?: number; unitEdit?: string; deleted?: boolean }
    ]
  ) => {
    try {
      setHandleLoading(true)
      const res = await updateMaterialOrder(data, orderId!)
      if (isSuccess(res)) {
        notify('Material updated successfully', 'success')
        fetchOrderById()
        // setEditableName('')
        // setEditableAmount(0)
        // setEditableUnit('')

        return true
      } else {
        return false
      }
    } catch (error) {
      console.log('updateMaterialOrder error', error)
    }
  }

  // const handleAddMaterialClick = async (isInventory?: boolean) => {
  //   const obj = isInventory
  //     ? {
  //         ...material1?.[0],
  //       }
  //     : {
  //         ...material2?.[0],
  //       }
  //   const matList = { _id: generateUUID(), ...obj }

  //   delete matList.isNew

  //   try {
  //     const res = await AddMaterialToOrder({ matList }, orderId!)
  //     if (isSuccess(res)) {
  //       notify('Created new Material', 'success')
  //       setIsAddMode(false)
  //       setProjectId('')
  //       fetchOrderById()
  //       setEditableName('')
  //     } else throw new Error(res?.data?.message)
  //   } catch (err: any) {
  //     notify(err?.message ?? 'Failed to create new Material', 'error')
  //   }
  // }

  const handleMaterialSave = async () => {
    try {
      setSaveLoading(true)
      const promises = tempMaterial?.map((matList: any) => AddMaterialToOrder({ matList }, orderId!))
      const results = await Promise.all(promises)

      if (isSuccess(results?.[0])) {
        const finalData = isSupplier ? [...tempMaterial, ...nonInventoryData] : [...tempMaterial, ...inventoryData]
        const finalDataWithSequence = finalData.map(({ inventory, projectId, _id }, index) => ({
          inventory,
          projectId,
          _id,
          sequence: index + 1,
        }))

        const response = await updateMaterialOrder(finalDataWithSequence, orderId!)

        if (isSuccess(response)) {
          notify('Created new Material', 'success')
          setIsAddMode(false)
          setProjectId('')
          fetchOrderById()
          setTempMaterial([])
          resetLocalStorageData(orderId!)
          // setEditableName('')
          // setEditableAmount(0)
          // setEditableUnit('')
        }
      } else throw new Error(results?.[0]?.data?.message)
    } catch (err: any) {
      notify(err?.message ?? 'Failed to create new Material', 'error')
    } finally {
      setSaveLoading(false)
    }
  }

  const handleAddMaterialClick = async (isInventory?: boolean) => {
    const obj = isInventory
      ? {
          ...material1?.[tempMaterial?.length],
        }
      : {
          ...material2?.[tempMaterial?.length],
        }
    const matList = { _id: generateUUID(), ...obj }

    // =========================================  =========================================

    delete matList?.isNew

    const data = [...tempMaterial, matList]

    const mergedData = isInventory ? material1 : material2
    const sorted = mergedData?.sort((a, b) => (a?.sequence || 0) - (b?.sequence || 0))

    // Create new item template
    const newItemTemplate = {
      nameEdit: '',
      unitEdit: '',
      amountEdit: 0,
      isNew: true,
      inventory: isInventory,
      projectId: projectId,
    }

    setTempMaterial(data)
    localStorage.setItem(`tempMaterial-${orderId}`, JSON.stringify(data))

    // Restructure finalData to put edited item first, then new item, then remaining items
    const finalData = [
      ...data, // Last edited item
      newItemTemplate, // Empty editable row
      ...sorted.slice(data?.length), // Remaining items
    ]

    if (isInventory) {
      setMaterial1(finalData)
      localStorage.setItem(`material1-${orderId}`, JSON.stringify(finalData))
    } else {
      setMaterial2(finalData)
      localStorage.setItem(`material2-${orderId}`, JSON.stringify(finalData))
    }

    setEditableName('')
    setEditableAmount(0)
    setEditableUnit('')

    const firstInput = document.querySelector('.material-input input') as HTMLInputElement
    if (firstInput) {
      firstInput?.focus()
    }
  }

  const toggleCount = (type: string, isvisible: boolean) => {
    setToggleHeading(() => ({
      [type]: isvisible,
    }))
  }

  const materialTableDragAndDrop = (materialsProps: any, droppableId: string) => {
    // Function to handle cell editing

    const isInventory = droppableId?.includes('material1')

    const startEditing = (id: string, field: string, value: any) => {
      setEditingCell({ id, field, droppableId })
      setHandleLoading(true)

      switch (field) {
        case 'name':
          setEditableName(value)
          break
        case 'amount':
          setEditableAmount(Number(value?.toFixed(2)))
          break
        case 'unit':
          setEditableUnit(value)
          break
      }
    }

    // Function to save changes when finished editing
    const finishEditing = (id: string, projectId: string, field: string, isNew: boolean, isInventory: boolean) => {
      // setComponentLoading(`${id}+${field}+${projectId}`)

      const mergedData = isInventory ? material1 : material2
      const sorted = mergedData?.sort((a, b) => (a?.sequence || 0) - (b?.sequence || 0))
      const currentItem = mergedData?.find((item: any) => item?._id === id)

      // Create update object based on the field being edited
      let updateData = {}
      let valueChanged = false
      switch (field) {
        case 'name':
          if (editableName !== '') {
            if (
              (currentItem?.nameEdit && currentItem?.nameEdit !== editableName) ||
              (currentItem?.name && currentItem?.name !== editableName)
            ) {
              valueChanged = true
            }

            updateData = { _id: id, projectId, nameEdit: editableName }
            if (isNew) {
              const newValues = sorted
              newValues[tempMaterial?.length].nameEdit = editableName
              localStorage.setItem(
                isInventory ? `material1-${orderId}` : `material2-${orderId}`,
                JSON.stringify(newValues)
              )
              if (isInventory) {
                setMaterial1(newValues)
              } else {
                setMaterial2(newValues)
              }
            }
          }
          break
        case 'amount':
          if (editableAmount !== 0) {
            if (
              (currentItem?.amountEdit && currentItem?.amountEdit !== editableAmount) ||
              (currentItem?.amount && currentItem?.amount !== editableAmount)
            ) {
              valueChanged = true
            }
            updateData = { _id: id, projectId, amountEdit: editableAmount }
            if (isNew) {
              const newValues = sorted
              newValues[tempMaterial?.length].amountEdit = editableAmount
              localStorage.setItem(
                isInventory ? `material1-${orderId}` : `material2-${orderId}`,
                JSON.stringify(newValues)
              )
              if (isInventory) {
                setMaterial1(newValues)
              } else {
                setMaterial2(newValues)
              }
            }
          }
          break
        case 'unit':
          if (editableUnit !== '') {
            if (
              (currentItem?.unitEdit && currentItem?.unitEdit !== editableUnit) ||
              (currentItem?.unit && currentItem?.unit !== editableUnit)
            ) {
              valueChanged = true
            }
            updateData = { _id: id, projectId, unitEdit: editableUnit }

            if (isNew) {
              const newValues = sorted
              newValues[tempMaterial?.length].unitEdit = editableUnit
              localStorage.setItem(
                isInventory ? `material1-${orderId}` : `material2-${orderId}`,
                JSON.stringify(newValues)
              )
              if (isInventory) {
                setMaterial1(newValues)
              } else {
                setMaterial2(newValues)
              }
            }
          }
          break
      }

      // Only update if there's valid data
      if (Object.keys(updateData).length > 0 && valueChanged) {
        !isNew && handleOutSideClickMaterial([updateData])
      }
      !valueChanged && setHandleLoading(false)

      !isNew && setEditingCell(null)
    }

    // Get all editable fields in order for keyboard navigation
    const getOrderedFields = (includePrice: boolean) => {
      const baseFields = ['name', 'amount', 'unit']
      if (includePrice) {
        return [...baseFields, 'price', 'total', 'action']
      } else {
        return [...baseFields, 'action']
      }
    }

    // Handle keyboard navigation (Excel-like behavior)
    const handleKeyDown = (
      e: React.KeyboardEvent,
      id: string,
      projectId: string,
      field: string,
      index: number,
      totalItems: number,
      isNew: boolean,
      droppableId: string
    ) => {
      const allFields = getOrderedFields(materialToggle)

      if (e.key === 'Enter' || e.key === 'Tab') {
        e.preventDefault()

        if (field !== 'action') {
          finishEditing(id, projectId, field, isNew, isInventory)
        }

        // Move to next cell based on tab direction
        const currentFieldIndex = allFields.indexOf(field)
        let nextField, nextRow

        // Move forward
        if (currentFieldIndex < allFields.length - 1) {
          nextField = allFields[currentFieldIndex + 1]
          nextRow = index
        } else if (index < totalItems - 1) {
          nextField = allFields[0]
          nextRow = index + 1
        }
        // }

        // Handle the next focus target
        if (nextField && nextRow !== undefined) {
          const nextItem = materialsProps[nextRow]
          if (nextItem) {
            const deleted = nextItem.deleted !== undefined ? nextItem.deleted : false

            // For action column, we just need to set focus to the appropriate button
            if (nextField === 'action' && !isNew) {
              setEditingCell({ id: nextItem._id, field: 'action', droppableId })
              setTimeout(() => {
                const tableContainer = document.querySelector(`[data-droppable-id="${droppableId}"]`)
                const actionButton = tableContainer?.querySelector(
                  `[data-item-id="${nextItem._id}"] ${deleted ? '.restore' : '.delete'}`
                ) as HTMLElement
                if (actionButton) {
                  actionButton.focus()
                }
              }, 10)
            } else if (nextField === 'action' && isNew) {
              setEditingCell({ id: nextItem._id, field: 'action', droppableId })
              setTimeout(() => {
                const actionButton = document.getElementById(`add-btn`) as HTMLElement
                if (actionButton) {
                  actionButton.focus()
                }
              }, 10)
            }

            // For editable fields, start editing
            else if (['name', 'amount', 'unit'].includes(nextField)) {
              const value =
                nextField === 'name'
                  ? nextItem.nameEdit || nextItem.name
                  : nextField === 'amount'
                  ? nextItem.amountEdit || nextItem.amount
                  : nextItem.unitEdit || nextItem.unit

              if (nextField === 'unit') {
                setEditingCell({ id: nextItem._id, field: nextField, droppableId })
                setTimeout(() => {
                  // Find and focus the select element
                  const selectElement = unitRef?.current
                  if (selectElement) {
                    selectElement.focus()
                  }
                }, 10)
              } else {
                // setTimeout(() => {
                startEditing(nextItem._id, nextField, value)
                // }, 10)
              }
            }
          }
        }
      } else if (e.key === 'Escape') {
        setEditingCell(null)
      }
    }

    return (
      <Droppable droppableId={droppableId}>
        {(provided) => (
          <Styled.BoxSpacing
            margin={'0 0 14px 40px'}
            {...provided.droppableProps}
            ref={provided.innerRef}
            className="material-table"
            data-droppable-id={droppableId}
          >
            <Styled.BoxGap width="80%">
              <Styled.BoxGap width="100%" display="flex" border={true}>
                <Styled.BoxGap
                  margin="0 0 0 15px"
                  className="three-table-div1"
                  width={`${materialToggle ? '50%' : '70%'}`}
                >
                  <SharedStyled.Text fontSize="14px">
                    <b>Material</b>
                  </SharedStyled.Text>
                </Styled.BoxGap>
                <Styled.BoxGap textAlign="center" className="three-table-div2" width="10%">
                  <SharedStyled.Text fontSize="14px">
                    <b>Amt</b>
                  </SharedStyled.Text>
                </Styled.BoxGap>
                <Styled.BoxGap textAlign="center" className="three-table-div3" width="10%">
                  <SharedStyled.Text fontSize="14px">
                    <b>Unit</b>
                  </SharedStyled.Text>
                </Styled.BoxGap>
                {materialToggle && (
                  <>
                    <Styled.BoxGap textAlign="center" className="three-table-div2" width="10%">
                      <SharedStyled.Text fontSize="14px">
                        <b>Price</b>
                      </SharedStyled.Text>
                    </Styled.BoxGap>
                    <Styled.BoxGap textAlign="center" className="three-table-div3" width="10%">
                      <SharedStyled.Text fontSize="14px">
                        <b>Total</b>
                      </SharedStyled.Text>
                    </Styled.BoxGap>
                  </>
                )}
                <Styled.BoxGap textAlign="center" className="three-table-div3" width="10%">
                  <SharedStyled.Text fontSize="14px">
                    <b>Action</b>
                  </SharedStyled.Text>
                </Styled.BoxGap>
              </Styled.BoxGap>
            </Styled.BoxGap>

            {materialsProps &&
              materialsProps
                ?.sort((a, b) => (a?.sequence || 0) - (b?.sequence || 0))
                ?.map(
                  (
                    {
                      _id,
                      projectId,
                      name,
                      nameEdit,
                      unit,
                      unitEdit,
                      amount,
                      amountEdit,
                      cost,
                      deleted,
                      isNew,
                    }: {
                      _id: string
                      projectId: string
                      name: string
                      nameEdit: string
                      unit: string
                      unitEdit: string
                      amount: number
                      amountEdit: number
                      cost: number
                      deleted: boolean
                      isNew?: boolean
                    },
                    index: number
                  ) => {
                    return (
                      <Draggable key={`${_id}`} draggableId={`${_id}`} index={index}>
                        {(provided) => (
                          <Styled.HoverBox>
                            <Styled.BoxGap
                              width="80%"
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              data-item-id={_id}
                            >
                              <Styled.BoxGap
                                width="100%"
                                display="flex"
                                border={true}
                                isDraft={
                                  Array.isArray(tempMaterial) &&
                                  !!tempMaterial?.filter((item: any) => item._id === _id)?.length
                                }
                                className={isNew && isAddMode ? 'editing-row' : isAddMode ? 'drag-row' : ''}
                              >
                                <Styled.BoxGap className="three-table-div1" width={`${materialToggle ? '50%' : '70%'}`}>
                                  <SharedStyled.Text margin="auto" fontSize="14px">
                                    <SharedStyled.FlexBox alignItems="center" gap="5px">
                                      {isNew ? null : (
                                        // <div className="hover-svg" />
                                        <div className="hover-svg" {...provided.dragHandleProps}>
                                          <svg width="24" height="24" viewBox="0 0 24 24">
                                            <path d="M7 10h2v2H7v-2zm0 4h2v2H7v-2zm4-4h2v2h-2v-2zm0 4h2v2h-2v-2zm4-4h2v2h-2v-2zm0 4h2v2h-2v-2z" />
                                          </svg>
                                        </div>
                                      )}
                                      <div className="material-input">
                                        {isNew ||
                                        (editingCell &&
                                          ((editingCell.id === _id && editingCell.droppableId === droppableId) ||
                                            isNew) &&
                                          editingCell.field === 'name') ? (
                                          <input
                                            type="text"
                                            // style={{ width: '325px' }}
                                            value={editableName}
                                            onChange={(e) => setEditableName(e.target.value)}
                                            onBlur={() => finishEditing(_id, projectId, 'name', isNew, isInventory)}
                                            onKeyDown={(e) =>
                                              handleKeyDown(
                                                e,
                                                _id,
                                                projectId,
                                                'name',
                                                index,
                                                materialsProps.length,
                                                isNew!,
                                                droppableId
                                              )
                                            }
                                            autoFocus
                                          />
                                        ) : componentLoading === `${_id}+name+${projectId}` ? (
                                          <SLoader height={15} width={325} />
                                        ) : (
                                          <SharedStyled.Text
                                            fontSize="14px"
                                            textDecoration={
                                              (deleted !== undefined ? deleted : false) ? 'line-through' : 'unset'
                                            }
                                            onClick={() => startEditing(_id, 'name', nameEdit || name)}
                                            className="editable-cell name-cell"
                                            tabIndex={0}
                                            onKeyDown={(e) => {
                                              if (e.key === 'Enter' || e.key === ' ') {
                                                e.preventDefault()
                                                startEditing(_id, 'name', nameEdit || name)
                                              } else {
                                                handleKeyDown(
                                                  e,
                                                  _id,
                                                  projectId,
                                                  'name',
                                                  index,
                                                  materialsProps.length,
                                                  false,
                                                  droppableId
                                                )
                                              }
                                            }}
                                          >
                                            {nameEdit || name || <>&nbsp;</>}
                                          </SharedStyled.Text>
                                        )}
                                      </div>
                                    </SharedStyled.FlexBox>
                                  </SharedStyled.Text>
                                </Styled.BoxGap>
                                <Styled.BoxGap
                                  margin="auto 0 auto 0"
                                  textAlign="right"
                                  className="three-table-div2"
                                  width="10%"
                                >
                                  <div className="material-input">
                                    {editingCell &&
                                    ((editingCell.id === _id && editingCell.droppableId === droppableId) || isNew) &&
                                    editingCell.field === 'amount' ? (
                                      <input
                                        type="number"
                                        value={editableAmount}
                                        onChange={(e) => setEditableAmount(Number(e.target.value))}
                                        onBlur={() => finishEditing(_id, projectId, 'amount', isNew, isInventory)}
                                        onKeyDown={(e) =>
                                          handleKeyDown(
                                            e,
                                            _id,
                                            projectId,
                                            'amount',
                                            index,
                                            materialsProps.length,
                                            isNew!,
                                            droppableId
                                          )
                                        }
                                        autoFocus
                                      />
                                    ) : componentLoading === `${_id}+amount+${projectId}` ? (
                                      <SLoader height={15} width={35} />
                                    ) : (
                                      <SharedStyled.Text
                                        fontSize="14px"
                                        textDecoration={
                                          (deleted !== undefined ? deleted : false) ? 'line-through' : 'unset'
                                        }
                                        onClick={() => startEditing(_id, 'amount', amountEdit ?? amount)}
                                        className="editable-cell amount-cell"
                                        tabIndex={0}
                                        onKeyDown={(e) => {
                                          if (e.key === 'Enter' || e.key === ' ') {
                                            e.preventDefault()
                                            startEditing(_id, 'amount', amountEdit ?? amount)
                                          } else {
                                            handleKeyDown(
                                              e,
                                              _id,
                                              projectId,
                                              'amount',
                                              index,
                                              materialsProps.length,
                                              false,
                                              droppableId
                                            )
                                          }
                                        }}
                                      >
                                        {amountEdit || amount ? (
                                          roundToNearestTenth(amountEdit || amount)?.toFixed(1)
                                        ) : (
                                          <>&nbsp;</>
                                        )}
                                      </SharedStyled.Text>
                                    )}
                                  </div>
                                </Styled.BoxGap>
                                <Styled.BoxGap
                                  margin="auto 0 auto 0"
                                  textAlign="center"
                                  className="three-table-div3"
                                  width="10%"
                                >
                                  <div className="material-input">
                                    {editingCell &&
                                    ((editingCell.id === _id && editingCell.droppableId === droppableId) || isNew) &&
                                    editingCell.field === 'unit' ? (
                                      <div>
                                        <CustomSelect
                                          dropDownData={filterUnits(unitData)}
                                          setValue={(val: string) => {
                                            const unitVal = val?.split(' ')?.[0]
                                            if (isNew) {
                                              setEditableUnit(unitVal)
                                              const mergedData = isInventory ? material1 : material2
                                              const sorted = mergedData?.sort(
                                                (a, b) => (a?.sequence || 0) - (b?.sequence || 0)
                                              )

                                              sorted[tempMaterial?.length].unitEdit = unitVal
                                              if (isInventory) {
                                                setMaterial1(sorted)
                                              } else {
                                                setMaterial2(sorted)
                                              }

                                              finishEditing(_id, projectId, 'unit', isNew, isInventory)
                                            } else {
                                              setEditableUnit(unitVal)
                                            }
                                          }}
                                          stateName="unitEdit"
                                          value={
                                            isNew
                                              ? (isInventory ? material1 : material2)?.sort(
                                                  (a, b) => (a?.sequence || 0) - (b?.sequence || 0)
                                                )?.[tempMaterial?.length]?.unitEdit || ''
                                              : filterUnits(unitData)?.filter(
                                                  (itm: any) => itm?.split(' ')?.[0] === (editableUnit || unitEdit)
                                                )?.[0] ||
                                                filterUnits(unitData)?.filter(
                                                  (itm: any) => itm?.split(' ')?.[0] === unit
                                                )?.[0]
                                          }
                                          showInitialValue
                                          labelName=""
                                          autoFocus
                                          innerHeight="24px"
                                          ref={unitRef}
                                          onBlur={() => finishEditing(_id, projectId, 'unit', isNew, isInventory)}
                                          onKeyDown={(e) => {
                                            if (e.key === 'Tab' || e.key === 'Enter') {
                                              e.preventDefault()
                                              finishEditing(_id, projectId, 'unit', isNew, isInventory)

                                              handleKeyDown(
                                                e,
                                                _id,
                                                projectId,
                                                'unit',
                                                index,
                                                materialsProps.length,
                                                isNew!,
                                                droppableId
                                              )
                                            } else if (e.key === 'Escape') {
                                              e.preventDefault()
                                              setEditingCell(null)
                                            }
                                          }}
                                        />
                                      </div>
                                    ) : componentLoading === `${_id}+unit+${projectId}` ? (
                                      <SLoader height={15} width={35} margin="0 0 0 5px" />
                                    ) : (
                                      <div>
                                        {/* <CustomSelect
                                          dropDownData={filterUnits(unitData)}
                                          setValue={(val: string) => {
                                            const unitVal = val?.split(' ')?.[0]
                                            if (isNew) {
                                              const values = [...material1]
                                              values[0].unitEdit = unitVal
                                              setMaterial1(values)
                                            } else {
                                              setEditableUnit(unitVal)
                                              finishEditing(_id, projectId, 'unit', isNew)
                                            }
                                          }}
                                          stateName="unitEdit"
                                          value={
                                            isNew
                                              ? material1?.[0]?.unitEdit || ''
                                              : filterUnits(unitData)?.filter((itm: any) =>
                                                  itm?.includes(unitEdit)
                                                )?.[0] ||
                                                filterUnits(unitData)?.filter((itm: any) => itm?.includes(unit))?.[0]
                                          }
                                          showInitialValue
                                          labelName=""
                                          innerHeight="24px"
                                          onBlur={() => finishEditing(_id, projectId, 'unit', isNew)}
                                          onKeyDown={(e) => {
                                            if (e.key === 'Tab' || e.key === 'Enter') {
                                              e.preventDefault()
                                              finishEditing(_id, projectId, 'unit', isNew)
                                              // Use the main navigation system for consistent behavior
                                              handleKeyDown(
                                                e,
                                                _id,
                                                projectId,
                                                'unit',
                                                index,
                                                materialsProps.length,
                                                isNew
                                              )
                                            } else if (e.key === 'Escape') {
                                              e.preventDefault()
                                              setEditingCell(null)
                                            }
                                          }}
                                        /> */}

                                        <SharedStyled.Text
                                          fontSize="14px"
                                          textDecoration={
                                            (deleted !== undefined ? deleted : false) ? 'line-through' : 'unset'
                                          }
                                          onClick={() => startEditing(_id, 'unit', unitEdit || unit)}
                                          className="editable-cell unit-cell"
                                          tabIndex={0}
                                          onKeyDown={(e) => {
                                            if (e.key === 'Enter' || e.key === ' ') {
                                              e.preventDefault()
                                              startEditing(_id, 'unit', unitEdit || unit)
                                            } else {
                                              handleKeyDown(
                                                e,
                                                _id,
                                                projectId,
                                                'unit',
                                                index,
                                                materialsProps.length,
                                                false,
                                                droppableId
                                              )
                                            }
                                          }}
                                        >
                                          {unitEdit || unit || <>&nbsp;</>}
                                        </SharedStyled.Text>
                                      </div>
                                    )}
                                  </div>
                                </Styled.BoxGap>
                                {materialToggle && (
                                  <>
                                    <Styled.BoxGap
                                      margin="auto 0 auto 0"
                                      textAlign="right"
                                      className="three-table-div2"
                                      width="10%"
                                    >
                                      <SharedStyled.Text
                                        fontSize="14px"
                                        textDecoration={
                                          (deleted !== undefined ? deleted : false) ? 'line-through' : 'unset'
                                        }
                                        className="price-cell"
                                        tabIndex={0}
                                        onKeyDown={(e) =>
                                          handleKeyDown(
                                            e,
                                            _id,
                                            projectId,
                                            'price',
                                            index,
                                            materialsProps.length,
                                            false,
                                            droppableId
                                          )
                                        }
                                      >
                                        ${formatNumberToCommaS(cost / amount) || '--'}
                                      </SharedStyled.Text>
                                    </Styled.BoxGap>
                                    <Styled.BoxGap
                                      margin="auto 0 auto 0"
                                      textAlign="right"
                                      className="three-table-div3"
                                      width="10%"
                                    >
                                      <SharedStyled.Text
                                        fontSize="14px"
                                        textDecoration={
                                          (deleted !== undefined ? deleted : false) ? 'line-through' : 'unset'
                                        }
                                        className="total-cell"
                                        tabIndex={0}
                                        onKeyDown={(e) =>
                                          handleKeyDown(
                                            e,
                                            _id,
                                            projectId,
                                            'total',
                                            index,
                                            materialsProps.length,
                                            false,
                                            droppableId
                                          )
                                        }
                                      >
                                        ${formatNumberToCommaS(cost) || '--'}
                                      </SharedStyled.Text>
                                    </Styled.BoxGap>
                                  </>
                                )}
                                <Styled.BoxGap margin="auto auto 0 auto">
                                  {(deleted !== undefined ? !deleted : true) ? (
                                    <>
                                      {isNew ? (
                                        <SharedStyled.FlexRow>
                                          <Button
                                            padding="0"
                                            width="24px"
                                            height="24px"
                                            id="add-btn"
                                            style={{
                                              marginBottom: '2px',
                                            }}
                                            onClick={() => {
                                              // setEditingCell(null)
                                              setEditableName('')

                                              handleAddMaterialClick(isInventory)
                                            }}
                                            tabIndex={
                                              editingCell &&
                                              (editingCell.id === _id || isNew) &&
                                              editingCell.field === 'action'
                                                ? 0
                                                : -1
                                            }
                                            onKeyDown={(e) => {
                                              if (e.key === 'Enter' || e.key === ' ') {
                                                e.preventDefault()
                                                setEditingCell(null)
                                                setEditableName('')
                                                handleAddMaterialClick(isInventory)
                                              } else if (e.key === 'Tab') {
                                                e.preventDefault()
                                                // Move to the next row if available
                                                // const nextBtn = document.getElementById('delete-btn') as HTMLElement
                                                // if (nextBtn) {
                                                //   e.preventDefault()
                                                //   nextBtn.focus()
                                                // }
                                              }
                                            }}
                                          >
                                            +
                                          </Button>

                                          {/* <Styled.ActionIconContainer id="add-btn">
                                            <AddIcon />
                                          </Styled.ActionIconContainer> */}

                                          {/* <Styled.ActionIconContainer
                                            className="delete"
                                            id="delete-btn"
                                            onClick={() => {
                                              setEditableName('')
                                              setEditingCell(null)
                                              setIsAddMode(false)
                                              if (isInventory) {
                                                const val = material1?.filter((_itm: any, index: number) => index)
                                                setMaterial1(val)
                                              } else {
                                                const val = material2?.filter((_itm: any, index: number) => index)
                                                setMaterial2(val)
                                              }
                                            }}
                                            tabIndex={
                                              editingCell &&
                                              (editingCell.id === _id || isNew) &&
                                              editingCell.field === 'action'
                                                ? 0
                                                : -1
                                            }
                                            onKeyDown={(e) => {
                                              if (e.key === 'Enter' || e.key === ' ') {
                                                e.preventDefault()
                                                setEditableName('')
                                                setEditingCell(null)
                                                setIsAddMode(false)
                                                if (isInventory) {
                                                  const val = material1?.filter((_itm: any, index: number) => index)
                                                  setMaterial1(val)
                                                } else {
                                                  const val = material2?.filter((_itm: any, index: number) => index)
                                                  setMaterial2(val)
                                                }
                                              } else if (e.key === 'Tab') {
                                                e.preventDefault()
                                              }
                                            }}
                                          >
                                            <DeleteIcon />
                                          </Styled.ActionIconContainer>  */}
                                        </SharedStyled.FlexRow>
                                      ) : (
                                        <Styled.ActionIconContainer
                                          className="delete"
                                          onClick={() =>
                                            handleOutSideClickMaterial([{ _id, projectId, deleted: true }])
                                          }
                                          tabIndex={
                                            editingCell &&
                                            editingCell.id === _id &&
                                            editingCell.droppableId === droppableId &&
                                            editingCell.field === 'action'
                                              ? 0
                                              : -1
                                          }
                                          onKeyDown={(e) => {
                                            if (e.key === 'Enter' || e.key === ' ') {
                                              e.preventDefault()
                                              handleOutSideClickMaterial([{ _id, projectId, deleted: true }])
                                            } else if (e.key === 'Tab') {
                                              // Move to the next row if available
                                              const nextIndex = index + 1
                                              if (nextIndex < materialsProps.length) {
                                                e.preventDefault()
                                                const nextItem = materialsProps[nextIndex]
                                                setTimeout(() => {
                                                  startEditing(nextItem._id, 'name', nextItem.nameEdit || nextItem.name)
                                                }, 10)
                                              }
                                            } else {
                                              handleKeyDown(
                                                e,
                                                _id,
                                                projectId,
                                                'action',
                                                index,
                                                materialsProps.length,
                                                false,
                                                droppableId
                                              )
                                            }
                                          }}
                                        >
                                          <DeleteIcon />
                                        </Styled.ActionIconContainer>
                                      )}
                                    </>
                                  ) : (
                                    <Styled.ActionIconContainer
                                      className="restore"
                                      onClick={() => handleOutSideClickMaterial([{ _id, projectId, deleted: false }])}
                                      tabIndex={
                                        editingCell &&
                                        editingCell.id === _id &&
                                        editingCell.droppableId === droppableId &&
                                        editingCell.field === 'action'
                                          ? 0
                                          : -1
                                      }
                                      onKeyDown={(e) => {
                                        if (e.key === 'Enter' || e.key === ' ') {
                                          e.preventDefault()
                                          handleOutSideClickMaterial([{ _id, projectId, deleted: false }])
                                        } else if (e.key === 'Tab') {
                                          // Move to the next row if available
                                          const nextIndex = index + 1
                                          if (nextIndex < materialsProps.length) {
                                            e.preventDefault()
                                            const nextItem = materialsProps[nextIndex]
                                            setTimeout(() => {
                                              startEditing(nextItem._id, 'name', nextItem.nameEdit || nextItem.name)
                                            }, 10)
                                          }
                                        } else {
                                          handleKeyDown(
                                            e,
                                            _id,
                                            projectId,
                                            'action',
                                            index,
                                            materialsProps.length,
                                            false,
                                            droppableId
                                          )
                                        }
                                      }}
                                    >
                                      <RevokeIcon />
                                    </Styled.ActionIconContainer>
                                  )}
                                </Styled.BoxGap>
                              </Styled.BoxGap>
                            </Styled.BoxGap>
                          </Styled.HoverBox>
                        )}
                      </Draggable>
                    )
                  }
                )}
            {provided.placeholder}
          </Styled.BoxSpacing>
        )}
      </Droppable>
    )
  }

  return loading ? (
    <>
      <SharedStyled.Skeleton custWidth="100%" custHeight={'51px'} />
      <SharedStyled.Skeleton custWidth="100%" custHeight="50%" custMarginTop="10px" />
      <SharedStyled.Skeleton custWidth="100%" custHeight="50%" custMarginTop="10px" />
    </>
  ) : (
    <Styled.PrintContainer>
      <Formik
        initialValues={initialValues}
        onSubmit={() => {}}
        enableReinitialize={true}
        validateOnChange={true}
        validateOnBlur={false}
      >
        {({ values, setFieldValue }) => {
          // useEffect(() => {
          //   const filteredObject = Object?.fromEntries(
          //     Object?.entries(values?.colors ?? [])?.filter(([key, value]) => {
          //       return selectedProjectType?.priceColor?.some((item) => `${item?._id}@${item?.name}` === key)
          //     })
          //   )
          //   setFieldValue('colors2', filteredObject)
          // }, [selectedProjectType?.priceColor])
          console.log('values=>>>', values)

          return (
            <Form className="form">
              <SharedStyled.FlexRow justifyContent="space-between" margin="0 0 8px 0">
                <SharedStyled.SectionTitle as={'p'} className="noPrint">
                  Contract Details
                </SharedStyled.SectionTitle>

                <SharedStyled.FlexBox width="max-content" margin={'0 0 10px 0'} gap="15px" alignItems="center">
                  {/* <SharedStyled.Button
                    onClick={() => {
                      const printContent = document.querySelector('#print-div-crew-sheet')
                      if (printContent) {
                        const clone = printContent.cloneNode(true)
                        document.body.innerHTML = ''
                        document.body.appendChild(clone)
                        window.print()
                        window.location.reload()
                      }
                    }}
                    maxWidth="200px"
                    type="button"
                  >
                    Print Crew Sheet
                  </SharedStyled.Button> */}

                  <Button
                    disabled={!(!printLoading && !loading)}
                    onClick={handlePrintPage}
                    width="max-content"
                    type="button"
                  >
                    Print Agreement
                  </Button>
                  <Button
                    onClick={() => {
                      setPrintCrewSheet(true)
                      setTimeout(() => {
                        const printContent = document.querySelector('#print-div-crew-sheet')
                        if (printContent) {
                          const clone = printContent.cloneNode(true)
                          document.body.innerHTML = ''
                          document.body.appendChild(clone)
                          window.print()
                          window.location.reload()
                        }
                      }, 300)
                    }}
                    width="max-content"
                    type="button"
                  >
                    Print Crew Sheet
                  </Button>
                  <Button
                    width="max-content"
                    onClick={() => {
                      setPrintSupplierFlag(true)
                      setTimeout(() => {
                        const printContent = document.querySelector('#print-div')
                        if (printContent) {
                          const clone = printContent.cloneNode(true)
                          document.body.innerHTML = ''
                          document.body.appendChild(clone)
                          window.print()
                          window.location.reload()
                        }
                      }, 300)
                    }}
                    maxWidth="200px"
                    type="button"
                  >
                    Print Supplier Order
                  </Button>
                </SharedStyled.FlexBox>
              </SharedStyled.FlexRow>

              <SharedStyled.FlexRow margin="0 0 8px 0" className="print-hide">
                <SharedStyled.Text font-weight={'500'} fontSize="18px">
                  Price: {orderById?.priceTotals?.jobTotal?.toFixed(2)}
                </SharedStyled.Text>
              </SharedStyled.FlexRow>

              <div id="print-div-crew-sheet">
                <span className="print-hide">
                  <SharedStyled.FlexRow margin="0 0 8px 0">
                    <SharedStyled.Text font-weight={'500'} fontSize="21px">
                      PO#: {opportunity?.PO}-{opportunity?.num}
                    </SharedStyled.Text>
                  </SharedStyled.FlexRow>

                  <>
                    <SharedStyled.FlexBox width="50%" justifyContent="space-between">
                      <div>
                        <SharedStyled.Text fontSize="14px" className="capitalize">
                          {opportunity?.contact?.firstName} {opportunity?.contact?.lastName}
                          {/* {opportunity?.firstName} {opportunity?.lastName} */}
                          <br />
                          {opportunity?.street}
                          <br />
                          {opportunity?.city}, {opportunity?.state} {opportunity?.zip}
                        </SharedStyled.Text>
                      </div>
                      <div>
                        <SharedStyled.Text fontSize="14px">
                          {(opportunity?.contact?.phone && formatPhoneNumber(opportunity?.contact?.phone, '')) || 'N/A'}
                          <br />
                          {/* {(opportunity?.contact?.cPhone2 &&
                        formatPhoneNumber(opportunity?.contact?.cPhone2, '')) ||
                        'N/A'} */}
                          <br />
                          {opportunity?.duration > 0
                            ? opportunity?.duration === 1
                              ? `${opportunity?.duration} minute away`
                              : `${opportunity?.duration} minutes away`
                            : ''}
                        </SharedStyled.Text>
                      </div>
                    </SharedStyled.FlexBox>
                    {opportunity?.paymentType && (
                      <div className="margin-text">
                        <SharedStyled.Text fontWeight="700" fontSize="18px">
                          Payment Type: {opportunity?.paymentType}
                        </SharedStyled.Text>
                      </div>
                    )}
                  </>

                  <SharedStyled.HorizontalDivider margin={'10px 0'} />

                  {/* <div>
                    <SharedStyled.Text fontSize="24px">Work Order</SharedStyled.Text>
                  </div> */}
                </span>

                {values?.projects?.map((project: any, index: number) => {
                  const projectTypeData = projectTypes?.find((itm: { _id: string }) => itm?._id === project?.type)

                  const filteredMaterial1 = material1.filter((v) => v?.projectId === project?.projectId)
                  const filteredMaterial2 = material2.filter((v) => v?.projectId === project?.projectId)
                  // const filteredMaterial3 = material3?.filter((v) => v?.projectId === project?.projectId)

                  // const totalLaborCost = values?.projects.reduce((acc, p) => {
                  //   return acc + (p?.projectId !== project?.projectId && p?.priceTotals?.laborCost || 0)
                  // }, 0)
                  // console.log({totalLaborCost},project.name)
                  return (
                    <>
                      <div style={{ pageBreakBefore: 'always' }}></div>
                      {printCrewSheet ? (
                        <>
                          <SharedStyled.FlexRow margin="0 0 8px 0">
                            <SharedStyled.Text font-weight={'500'} fontSize="21px">
                              PO#: {opportunity?.PO}-{opportunity?.num}
                            </SharedStyled.Text>
                          </SharedStyled.FlexRow>

                          <>
                            <SharedStyled.FlexBox width="50%" justifyContent="space-between">
                              <div>
                                <SharedStyled.Text fontSize="14px" className="capitalize">
                                  {opportunity?.contact?.firstName} {opportunity?.contact?.lastName}
                                  {/* {opportunity?.firstName} {opportunity?.lastName} */}
                                  <br />
                                  {opportunity?.street}
                                  <br />
                                  {opportunity?.city}, {opportunity?.state} {opportunity?.zip}
                                </SharedStyled.Text>
                              </div>
                              <div>
                                <SharedStyled.Text fontSize="14px">
                                  {(opportunity?.contact?.phone &&
                                    formatPhoneNumber(opportunity?.contact?.phone, '')) ||
                                    'N/A'}
                                  <br />

                                  <br />
                                  {opportunity?.duration > 0
                                    ? opportunity?.duration === 1
                                      ? `${opportunity?.duration} minute away`
                                      : `${opportunity?.duration} minutes away`
                                    : ''}
                                </SharedStyled.Text>
                              </div>
                            </SharedStyled.FlexBox>
                            <SharedStyled.HorizontalDivider margin={'10px 0'} />
                          </>
                        </>
                      ) : null}

                      <SharedStyled.FlexCol gap="8px" margin="6px 0 14px 10px">
                        <SharedStyled.Text
                          textDecoration="underline"
                          textTransform="capitalize"
                          fontWeight="700"
                          fontSize="24px"
                        >
                          {`${index + 1}) ${project?.name}`}
                        </SharedStyled.Text>
                        <>
                          <Styled.BoxGap display="flex" margin="0 0 -5px 20px">
                            <SharedStyled.Text fontWeight="600" fontSize="14px">
                              Notes:
                            </SharedStyled.Text>
                            <Styled.EditButton
                              type="button"
                              onClick={() => setToggle(project?.name + 'notes')}
                              className="print-hide"
                            >
                              Edit Notes
                            </Styled.EditButton>
                          </Styled.BoxGap>
                          {componentLoading === `projects.${index}.notes` ? (
                            <>
                              <SLoader height={35} width={50} isPercent />
                            </>
                          ) : (
                            <>
                              {toggleitem[project?.name + 'notes'] ? (
                                <SharedStyled.FlexCol gap="8px" margin="0 0 0 25px">
                                  <Styled.TextArea
                                    component="textarea"
                                    as={Field}
                                    value={values?.projects?.[index]?.notes}
                                    name={`projects.${index}.notes`}
                                    margintop="5px"
                                    height="42px"
                                    margin="0 0 0 0"
                                    // placeHolder="ie. Remove existing shingle,flashing, and underlayment; then install new ice shield, flashing, and shingle to match"
                                    error={false}
                                  />
                                  <Button
                                    maxWidth="200px"
                                    type="button"
                                    onClick={() => {
                                      handleOrderUpdate(project?.projectId, {}, values?.projects?.[index]?.notes)
                                      setComponentLoading(`projects.${index}.notes`)
                                    }}
                                  >
                                    Save Notes
                                  </Button>
                                </SharedStyled.FlexCol>
                              ) : (
                                <SharedStyled.Text margin="0 0 0 30px">
                                  <Styled.PrintNotesContainer>
                                    <ReactMarkdown>
                                      {values?.projects?.[index]?.notes?.replace(/\n/g, '  \n')}
                                    </ReactMarkdown>
                                  </Styled.PrintNotesContainer>
                                  {/* <SharedStyled.Text fontSize="14px">{orderById?.notes}</SharedStyled.Text> */}
                                </SharedStyled.Text>
                              )}
                            </>
                          )}
                        </>

                        <>
                          <Styled.BoxGap display="flex" margin="0px 0 -8px 20px">
                            <SharedStyled.Text fontWeight="600" fontSize="14px">
                              Colors:
                            </SharedStyled.Text>
                            <Styled.EditButton
                              type="button"
                              onClick={() => setToggle(project?.name + 'colors')}
                              className="print-hide"
                            >
                              Edit
                            </Styled.EditButton>
                          </Styled.BoxGap>
                          {componentLoading === `projects.${index}.colors` ? (
                            <>
                              <SLoader height={25} width={50} isPercent />
                              <SLoader height={25} width={50} isPercent />
                              <SLoader height={25} width={50} isPercent />
                            </>
                          ) : (
                            <>
                              {toggleitem[project?.name + 'colors'] ? (
                                <>
                                  <Styled.CustomStyledDrop>
                                    {projectTypeData?.priceColor?.map((v: any, projIndex: number) => {
                                      const colorValue =
                                        Object.entries(values?.projects?.[index]?.colors || {}).find(([key, value]) => {
                                          const [colorId] = key.split('@')
                                          return colorId === v._id
                                        })?.[1] || ''
                                      const localColorValue =
                                        Object.entries(values?.projects?.[index]?.colors2 || {}).find(
                                          ([key, value]) => {
                                            const [colorId] = key.split('@')
                                            return colorId === v._id
                                          }
                                        )?.[1] || ''
                                      console.log({ localColorValue }, colorValue)
                                      return (
                                        <SharedStyled.FlexBox
                                          key={v._id}
                                          margin={'0 0 0 25px'}
                                          alignItems="center"
                                          // className="grid"
                                        >
                                          {/* <SharedStyled.Text className="regular" fontSize="14px">
                                    {v.name}:
                                  </SharedStyled.Text> */}
                                          <AutoComplete
                                            labelName={v.name}
                                            error={false}
                                            value={
                                              // localColorValue ? localColorValue?.toLowerCase() : colorValue?.toLowerCase()
                                              localColorValue || ''
                                            }
                                            options={v?.colors?.map((item: string) => item)}
                                            // setValue={() => {}}
                                            setFieldValue={setFieldValue}
                                            // innerHeight="40px"
                                            // margin="10px 0 0 0"
                                            stateName={`projects.${index}.colors2.${v._id}@${v.name}`}
                                            // maxWidth="250px"
                                            // fontSize="14px"
                                            className="colorDropdown"
                                          />
                                        </SharedStyled.FlexBox>
                                      )
                                    })}
                                    <SharedStyled.FlexRow margin="14px 0 0 20px">
                                      <Button
                                        maxWidth="200px"
                                        type="button"
                                        onClick={() => {
                                          handleOrderUpdate(project?.projectId, values?.projects?.[index]?.colors2, '')
                                          setComponentLoading(`projects.${index}.colors`)
                                        }}
                                      >
                                        Save Color Changes
                                      </Button>
                                    </SharedStyled.FlexRow>
                                  </Styled.CustomStyledDrop>
                                </>
                              ) : (
                                <Styled.BoxGap style={{ gap: 0 }}>
                                  {renderColors(values?.projects?.[index]?.colors, projectTypeData)}
                                </Styled.BoxGap>
                              )}
                            </>
                          )}
                        </>

                        <div>
                          <SharedStyled.Text margin="20px 0 0 10px" fontWeight="600" fontSize="18px">
                            Work Order
                          </SharedStyled.Text>
                        </div>

                        <Styled.BoxGap margin="0 0 0 20px">
                          {values?.projects?.[index]?.avgPitch ? (
                            <SharedStyled.Text fontSize="14px" margin="10px 0 0 0px">
                              Avg Pitch: {values?.projects?.[index]?.avgPitch}/12
                            </SharedStyled.Text>
                          ) : (
                            ''
                          )}
                          {values?.projects?.[index]?.pitch ? (
                            <SharedStyled.Text fontSize="14px" margin="10px 0 0 0px">
                              Pitch: {values?.projects?.[index]?.pitch}/12
                            </SharedStyled.Text>
                          ) : (
                            ''
                          )}
                          {renderCrew(
                            (project?.laborTime || 0) +
                              (project?.priceTotals?.travelFee / orderById?.priceTotals?.travelHrlyRate || 0) || 0
                          )}
                        </Styled.BoxGap>
                      </SharedStyled.FlexCol>

                      <Styled.BoxGap key={project.type} margin="0 0 20px 40px">
                        <Styled.BoxGap width="80%">
                          <Styled.BoxGap width="100%" display="flex" border={true}>
                            <Styled.BoxGap className="three-table-div1" width="80%">
                              <SharedStyled.Text fontSize="14px">
                                <b>Task</b>
                              </SharedStyled.Text>
                            </Styled.BoxGap>
                            <Styled.BoxGap textAlign="center" className="three-table-div2" width="10%">
                              <SharedStyled.Text fontSize="14px">
                                <b>Amt</b>
                              </SharedStyled.Text>
                            </Styled.BoxGap>
                            <Styled.BoxGap textAlign="center" className="three-table-div3" width="10%">
                              <SharedStyled.Text fontSize="14px">
                                <b>Unit</b>
                              </SharedStyled.Text>
                            </Styled.BoxGap>
                          </Styled.BoxGap>
                        </Styled.BoxGap>

                        {orderById &&
                          sortTasks(project?.workOrder)
                            ?.filter((v) => v.title !== 'Travel')
                            ?.map(
                              (
                                {
                                  taskName,
                                  taskUnit,
                                  rawValue,
                                }: { taskName: string; taskUnit: string; rawValue: number },
                                orderIndex: number
                              ) => {
                                return (
                                  <Styled.BoxGap width="80%" key={orderIndex}>
                                    <Styled.BoxGap width="100%" display="flex" border={true}>
                                      <Styled.BoxGap className="three-table-div1" width="80%">
                                        <SharedStyled.Text fontSize="14px">{taskName}</SharedStyled.Text>
                                      </Styled.BoxGap>
                                      <Styled.BoxGap textAlign="right" className="three-table-div2" width="10%">
                                        <SharedStyled.Text fontSize="14px">
                                          {roundToNearestTenth(rawValue)?.toFixed(1)}
                                        </SharedStyled.Text>
                                      </Styled.BoxGap>
                                      <Styled.BoxGap textAlign="center" className="three-table-div3" width="10%">
                                        <SharedStyled.Text fontSize="14px">{taskUnit}</SharedStyled.Text>
                                      </Styled.BoxGap>
                                    </Styled.BoxGap>
                                  </Styled.BoxGap>
                                )
                              }
                            )}
                      </Styled.BoxGap>

                      {
                        <>
                          <SharedStyled.FlexCol gap="8px" margin="0 0 0 20px">
                            <span className="print-hide">
                              <SharedStyled.FlexBox justifyContent="space-between" alignItems="center">
                                <SharedStyled.Text fontWeight="600" fontSize="18px">
                                  Materials: $
                                  {formatNumberToCommaS(
                                    (project?.priceTotals?.matCost || 0) +
                                      (project?.priceTotals?.matTax || 0) +
                                      (project?.priceTotals?.mMarkup || 0) +
                                      (project?.priceTotals?.permit || 0) +
                                      (project?.priceTotals?.asbTest || 0)
                                  )}
                                </SharedStyled.Text>
                                <Toggle
                                  title=""
                                  className="print-hide"
                                  customStyles={{ margin: '16px' }}
                                  isToggled={materialToggle}
                                  onToggle={() => {
                                    setMaterialToggle((prev) => !prev)
                                  }}
                                />
                              </SharedStyled.FlexBox>
                            </span>

                            <span className="print-hide">
                              <Toggle
                                title="Show Original Material List"
                                customStyles={{ fontWeight: '600', fontSize: '14px', marginLeft: '10px' }}
                                isToggled={!!project?.originalMatList}
                                onToggle={() => {
                                  handleOrderUpdate(project?.projectId, {}, '', !project?.originalMatList)
                                  setFieldValue(`projects.[${index}].originalMatList`, !project?.originalMatList)
                                }}
                                className="text"
                              />
                            </span>

                            <SharedStyled.Text fontWeight="600" margin="0 0 10px 10px" fontSize="14px">
                              Bring From Shop{' '}
                              {!!project?.originalMatList ? null : (
                                <SharedStyled.FlexRow
                                  style={{
                                    display: 'inline-block',
                                  }}
                                  gap="20px"
                                >
                                  {isAddMode && !isSupplier && project?.projectId === projectId ? (
                                    <Button
                                      onClick={() => {
                                        const confirm = window.confirm(
                                          'Are you sure you want to cancel? All your changes will be lost.'
                                        )

                                        if (confirm) {
                                          resetLocalStorageData(orderId!)
                                          window.location.reload()
                                        }
                                      }}
                                      padding="0"
                                      width="30px"
                                      height="30px"
                                      style={{
                                        marginRight: '10px',
                                      }}
                                      bgColor="red"
                                    >
                                      X
                                    </Button>
                                  ) : (
                                    <Button
                                      onClick={() => {
                                        // setShowAddModal(true)
                                        setIsSupplier(false)
                                        setProjectId(project?.projectId)
                                        setIsAddMode(true)

                                        setEditableName('')
                                        setEditableAmount('')
                                        setEditableUnit('')

                                        setMaterial1([
                                          {
                                            nameEdit: '',
                                            unitEdit: '',
                                            amountEdit: 0,
                                            isNew: true,
                                            inventory: true,
                                            projectId: project?.projectId,
                                          },

                                          ...material1,
                                        ])
                                      }}
                                      padding="0"
                                      width="30px"
                                      height="30px"
                                      disabled={isAddMode || handleLoading}
                                      style={{
                                        marginRight: '10px',
                                      }}
                                    >
                                      +
                                    </Button>
                                  )}
                                  {tempMaterial?.length && !isSupplier ? (
                                    <Button
                                      bgColor={'green'}
                                      onClick={handleMaterialSave}
                                      padding="0"
                                      width="30px"
                                      height="30px"
                                      disabled={saveLoading}
                                    >
                                      &#10003;
                                    </Button>
                                  ) : null}
                                </SharedStyled.FlexRow>
                              )}
                            </SharedStyled.Text>
                          </SharedStyled.FlexCol>

                          <DragDropContext onDragEnd={handleDragEnd}>
                            {/* {materialTableDragAndDrop(
                              material1?.filter((v) => {
                                if (index === 0) {
                                  // If index is 0, match ProjectType as an empty string
                                  return v?.ProjectType === ''
                                } else if (v?.projectId === project?.projectId) {
                                  // If ProjectType matches the type of the first project, match projectId
                                  return v?.projectId === project?.projectId
                                } else {
                                  // Otherwise, match ProjectType with the project's type
                                  return v?.ProjectType === project?.type
                                }
                              }),
                              'material1'
                            )} */}
                            {project.originalMatList
                              ? materialTable(filteredMaterial1, project.originalMatList, printCrewSheet)
                              : printCrewSheet
                              ? materialTable(filteredMaterial1, project.originalMatList, printCrewSheet)
                              : materialTableDragAndDrop(filteredMaterial1, `material1-${project.projectId}`)}
                            <SharedStyled.FlexCol gap="8px" margin="0 0 0 20px">
                              <SharedStyled.Text fontWeight="600" margin="0 0 10px 10px" fontSize="14px">
                                Order from Supplier{' '}
                                {!!project?.originalMatList ? null : (
                                  <SharedStyled.FlexRow
                                    style={{
                                      display: 'inline-block',
                                    }}
                                    gap="20px"
                                  >
                                    {isAddMode && isSupplier && project?.projectId === projectId ? (
                                      <Button
                                        onClick={() => {
                                          const confirm = window.confirm(
                                            'Are you sure you want to cancel? All your changes will be lost.'
                                          )

                                          if (confirm) {
                                            resetLocalStorageData(orderId!)
                                            window.location.reload()
                                          }
                                        }}
                                        padding="0"
                                        width="30px"
                                        height="30px"
                                        style={{
                                          marginRight: '10px',
                                        }}
                                        bgColor="red"
                                      >
                                        X
                                      </Button>
                                    ) : (
                                      <Button
                                        onClick={() => {
                                          // setShowAddModal(true)
                                          setIsSupplier(true)
                                          setProjectId(project?.projectId)
                                          setIsAddMode(true)

                                          setEditableName('')
                                          setEditableAmount('')
                                          setEditableUnit('')

                                          setMaterial2([
                                            {
                                              nameEdit: '',
                                              unitEdit: '',
                                              amountEdit: 0,
                                              isNew: true,
                                              inventory: false,
                                              projectId: project?.projectId,
                                            },

                                            ...material2,
                                          ])
                                        }}
                                        padding="0"
                                        width="30px"
                                        height="30px"
                                        disabled={isAddMode || handleLoading}
                                        style={{
                                          marginRight: '10px',
                                        }}
                                      >
                                        +
                                      </Button>
                                    )}
                                    {tempMaterial?.length && isSupplier ? (
                                      <Button
                                        bgColor={'green'}
                                        onClick={handleMaterialSave}
                                        padding="0"
                                        width="30px"
                                        height="30px"
                                        disabled={saveLoading}
                                      >
                                        &#10003;
                                      </Button>
                                    ) : null}
                                  </SharedStyled.FlexRow>
                                )}
                              </SharedStyled.Text>
                            </SharedStyled.FlexCol>
                            {project.originalMatList
                              ? materialTable(filteredMaterial2, project.originalMatList, printCrewSheet)
                              : printCrewSheet
                              ? materialTable(filteredMaterial2, project.originalMatList, printCrewSheet)
                              : materialTableDragAndDrop(filteredMaterial2, `material2-${project.projectId}`)}
                          </DragDropContext>

                          {/* <span className="print-hide">
                            <SharedStyled.FlexRow margin="0 0 0 20px">
                              <SharedStyled.Text fontWeight="600" margin="0 0 10px 10px" fontSize="14px">
                                Order from Home Depot
                              </SharedStyled.Text>
                            </SharedStyled.FlexRow>

                            {materialTable(filteredMaterial3)}
                          </span> */}
                        </>
                      }
                    </>
                  )
                })}

                {
                  <>
                    {/* <span className="print-hide">
                      <SharedStyled.FlexCol gap="8px" margin="0 0 0 20px">
                        <SharedStyled.FlexBox justifyContent="space-between" alignItems="center">
                          <SharedStyled.Text fontWeight="600" fontSize="18px">
                            Materials: ${formatNumberToCommaS(orderById?.priceTotals?.mTotal)}
                          </SharedStyled.Text>
                          <Toggle
                            title=""
                            className="print-hide"
                            customStyles={{ margin: '16px' }}
                            isToggled={materialToggle}
                            onToggle={() => {
                              setMaterialToggle((prev) => !prev)
                            }}
                          />
                        </SharedStyled.FlexBox>
                        <SharedStyled.Text fontWeight="600" margin="0 0 0 10px" fontSize="14px">
                          Bring From Shop
                        </SharedStyled.Text>
                      </SharedStyled.FlexCol>
                      <Styled.BoxSpacing margin="0 0 0 10px">
                        {renderColors(
                          values?.projects?.[0]?.colors,
                          projectTypes?.find((itm: { _id: string }) => itm?._id === values?.projects?.[0]?.type)
                        )}
                      </Styled.BoxSpacing>
                    </span> */}

                    {/* <br /> */}
                    {/* <DragDropContext onDragEnd={handleDragEnd}> */}
                    {/* <span className="print-hide">{materialTableDragAndDrop(material1, 'material1')}</span> */}

                    <Styled.BoxSpacing id="print-div" margin="0 0 14px 0">
                      {/* <span className="print-hide">
                        <SharedStyled.FlexRow margin="14px 0 8px 10px">
                          <SharedStyled.Text fontWeight="600" fontSize="18px">
                            PO#: {opportunity?.PO}-{opportunity?.num}
                          </SharedStyled.Text>
                        </SharedStyled.FlexRow>
                      </span> */}
                      {values?.projects?.map((project1: any, index1: number) => {
                        const filteredMaterial2 = material2.filter((v) => v?.projectId === project1?.projectId)
                        return (
                          <>
                            {printSupplierFlag && (
                              <>
                                <div style={{ pageBreakBefore: 'always' }}></div>
                                <SharedStyled.Text fontWeight="600" margin="0 0 10px 10px" fontSize="14px">
                                  Order from Supplier
                                </SharedStyled.Text>

                                {renderColors(
                                  project1?.colors,
                                  projectTypes?.find((itm: { _id: string }) => itm?._id === project1?.type)
                                )}

                                <div>
                                  <SharedStyled.FlexCol gap="5px" margin="10px 0 10px 10px">
                                    <SharedStyled.Text>
                                      <b>Project Address</b>
                                    </SharedStyled.Text>
                                    <SharedStyled.Text margin="0 0 0 20px" fontSize="14px">
                                      {opportunity?.contact?.firstName} {opportunity?.contact?.lastName}
                                    </SharedStyled.Text>
                                    <SharedStyled.Text margin="0 0 0 20px" fontSize="14px">
                                      {opportunity?.street}
                                    </SharedStyled.Text>
                                    <SharedStyled.Text margin="0 0 0 20px" fontSize="14px">
                                      {opportunity?.city}, {opportunity?.state} {opportunity?.zip}
                                    </SharedStyled.Text>
                                  </SharedStyled.FlexCol>
                                </div>

                                <SharedStyled.Text
                                  margin="0 0 0 10px"
                                  textTransform="capitalize"
                                  fontWeight="700"
                                  fontSize="14px"
                                >
                                  {`Project Name: ${project1?.name}`}
                                </SharedStyled.Text>

                                <SharedStyled.FlexRow margin="14px 0 8px 40px">
                                  <SharedStyled.Text fontWeight="600" fontSize="18px">
                                    PO#: {opportunity?.PO}-{opportunity?.num}
                                  </SharedStyled.Text>
                                </SharedStyled.FlexRow>
                                {/* {materialTable(
                                  material2?.filter((v) =>
                                    // (index === 0 ? v?.ProjectType === '' : project?.type === v?.ProjectType)
                                    {
                                      if (index1 === 0) {
                                        // If index is 0, match ProjectType as an empty string
                                        return v?.ProjectType === ''
                                      } else if (v?.projectId === project1?.projectId) {
                                        // If ProjectType matches the type of the first project, match projectId
                                        return v?.projectId === project1?.projectId
                                      } else {
                                        // Otherwise, match ProjectType with the project's type
                                        return v?.ProjectType === project1?.type
                                      }
                                    }
                                  )
                                )} */}
                                {materialTable(filteredMaterial2, project1.originalMatList, printSupplierFlag)}
                                {/* {materialTable(filteredMaterial2)} */}
                              </>
                            )}
                          </>
                        )
                      })}
                      {/* <span className="print-hide">
                          {materialTableDragAndDrop(material2, 'material2')}
                        </span> */}
                    </Styled.BoxSpacing>
                    {/* </DragDropContext> */}
                  </>
                }
              </div>

              {salesDataVisibleTo?.includes(position) ? (
                <>
                  <SharedStyled.Text fontWeight="700" fontSize="24px" margin="0 0 0 10px">
                    Labor: $
                    {formatNumberToCommaS(orderById?.priceTotals?.laborCost + orderById?.priceTotals?.travelFee)}
                  </SharedStyled.Text>
                  {values?.projects?.map((project: any, index: number) => {
                    return (
                      <>
                        <SharedStyled.FlexCol gap="8px" margin={'10px 0 0 0'}>
                          <SharedStyled.FlexCol margin={'0 0 0 20px'}>
                            <SharedStyled.Text
                              textDecoration="underline"
                              textTransform="capitalize"
                              fontWeight="700"
                              fontSize="18px"
                            >
                              {`${index + 1}) ${project?.name}`}
                            </SharedStyled.Text>
                            <SharedStyled.Text textTransform="capitalize" fontSize="14px" margin="5px 0 5px 20px">
                              {getNameFrom_Id(project?.type, projectTypes) || ''}
                            </SharedStyled.Text>
                            <SharedStyled.Text fontSize="14px">
                              <span className="arrow" onClick={() => setToggle(project?.name + index + 'labor')}>
                                {toggleitem[project?.name + index + 'labor'] ? (
                                  <span>&#x25BC;</span>
                                ) : (
                                  <span>&#x25B6;</span>
                                )}
                                &nbsp; Budget: $
                                {formatNumberToCommaS(
                                  project?.priceTotals?.laborCost + project?.priceTotals?.travelFee
                                ) || '--'}{' '}
                                |{' '}
                                {(
                                  project?.laborTime +
                                  (project?.priceTotals?.travelFee / orderById?.priceTotals?.travelHrlyRate || 0)
                                )?.toFixed(2) || '--'}{' '}
                                hrs
                              </span>
                            </SharedStyled.Text>
                          </SharedStyled.FlexCol>
                        </SharedStyled.FlexCol>

                        {toggleitem[project?.name + index + 'labor'] && (
                          <Styled.BoxSpacing margin="0 0 0 40px">
                            <Styled.BoxGap width="80%">
                              <Styled.BoxGap width="100%" display="flex" border={true}>
                                <Styled.BoxGap className="four-table-div2" width="10%">
                                  <SharedStyled.Text fontSize="14px">
                                    <b>Worker</b>
                                  </SharedStyled.Text>
                                </Styled.BoxGap>
                                <Styled.BoxGap className="four-table-div3" width="10%">
                                  <SharedStyled.Text fontSize="14px">
                                    <b>Hrs</b>
                                  </SharedStyled.Text>
                                </Styled.BoxGap>
                                <Styled.BoxGap className="four-table-div1" width="60%">
                                  <SharedStyled.Text fontSize="14px">
                                    <b>Task</b>
                                  </SharedStyled.Text>
                                </Styled.BoxGap>
                                <Styled.BoxGap textAlign="center" className="four-table-div4" width="10%">
                                  <SharedStyled.Text fontSize="14px">
                                    <b>Cost</b>
                                  </SharedStyled.Text>
                                </Styled.BoxGap>
                              </Styled.BoxGap>
                            </Styled.BoxGap>

                            {orderById &&
                              sortTasks(project?.workOrder)?.map(
                                (
                                  {
                                    title,
                                    task,
                                    ttlHours,
                                    cost,
                                  }: { title: string; task: string; ttlHours: number; cost: string },
                                  index: number
                                ) => {
                                  return (
                                    <Styled.BoxGap width="80%" key={index}>
                                      <Styled.BoxGap width="100%" display="flex" border={true}>
                                        <Styled.BoxGap className="four-table-div2" width="10%">
                                          <SharedStyled.Text fontSize="14px">{title}</SharedStyled.Text>
                                        </Styled.BoxGap>
                                        <Styled.BoxGap className="four-table-div3" width="10%">
                                          <SharedStyled.Text fontSize="14px">{ttlHours?.toFixed(2)}</SharedStyled.Text>
                                        </Styled.BoxGap>
                                        <Styled.BoxGap className="four-table-div1" width="60%">
                                          <SharedStyled.Text fontSize="14px">{task}</SharedStyled.Text>
                                        </Styled.BoxGap>
                                        <Styled.BoxGap textAlign="right" className="four-table-div4" width="10%">
                                          <SharedStyled.Text fontSize="14px">
                                            ${formatNumberToCommaS(cost)}
                                          </SharedStyled.Text>
                                        </Styled.BoxGap>
                                      </Styled.BoxGap>
                                    </Styled.BoxGap>
                                  )
                                }
                              )}
                            <Styled.BoxGap width="80%" key={index}>
                              <Styled.BoxGap width="100%" display="flex" border={true}>
                                <Styled.BoxGap className="four-table-div2" width="10%">
                                  <SharedStyled.Text fontSize="14px">Travel</SharedStyled.Text>
                                </Styled.BoxGap>
                                <Styled.BoxGap className="four-table-div3" width="10%">
                                  <SharedStyled.Text fontSize="14px">
                                    {(
                                      project?.priceTotals?.travelFee / (orderById?.priceTotals?.travelHrlyRate || 1)
                                    )?.toFixed(2)}
                                    {/* {project?.priceTotals?.lttHours?.toFixed(2)} */}
                                  </SharedStyled.Text>
                                </Styled.BoxGap>
                                <Styled.BoxGap className="four-table-div1" width="60%">
                                  <SharedStyled.Text fontSize="14px">Travel Time</SharedStyled.Text>
                                </Styled.BoxGap>
                                <Styled.BoxGap textAlign="right" className="four-table-div4" width="10%">
                                  <SharedStyled.Text fontSize="14px">
                                    ${formatNumberToCommaS(project?.priceTotals?.travelFee)}
                                  </SharedStyled.Text>
                                </Styled.BoxGap>
                              </Styled.BoxGap>
                            </Styled.BoxGap>
                          </Styled.BoxSpacing>
                        )}
                      </>
                    )
                  })}
                </>
              ) : null}

              {/* ========================================= New ========================================= */}
              {/* salesDataVisibleTo */}
              <Styled.NewOrderWrapper>
                {
                  // (positionDetails?.symbol === 'Owner' ||
                  // positionDetails?.symbol === 'GeneralManager' ||
                  // positionDetails?.symbol === 'SalesManager')
                  salesDataVisibleTo?.includes(position) &&
                    newPriceTotalsData?.map((priceData, idx) => (
                      <Styled.NewOrderContainer key={idx}>
                        <div
                          className={priceData?.clickable ? 'pointer' : ''}
                          onClick={() =>
                            setShowBreakdown((prev) => ({
                              ...prev,
                              [priceData?.label]: !prev[priceData?.label],
                            }))
                          }
                        >
                          <p>{priceData?.label}</p>
                          <p>${priceData?.value}</p>
                          {priceData?.percent ? <p>{priceData?.percent}%</p> : null}
                        </div>
                      </Styled.NewOrderContainer>
                    ))
                }

                <Styled.BreakdownWrap>
                  {!!showBreakdown['Total Materials'] && (
                    <>
                      <Styled.BreakdownCont>
                        <span>
                          <b>Total Materials:</b>
                        </span>
                        <span>
                          <b>
                            $
                            {(
                              orderById?.priceTotals?.matCost +
                              orderById?.priceTotals?.mMarkup +
                              orderById?.priceTotals?.permit +
                              orderById?.priceTotals?.asbTest +
                              orderById?.priceTotals?.matTax
                            )?.toFixed(2)}
                          </b>
                        </span>
                      </Styled.BreakdownCont>
                      <ul>
                        {materialsBreakdown?.map((mat) => (
                          <li key={mat?.label}>
                            <Styled.BreakdownCont>
                              <span>{mat?.label}:</span>
                              <span>${mat?.value}</span>
                            </Styled.BreakdownCont>
                          </li>
                        ))}
                      </ul>
                    </>
                  )}
                  {!!showBreakdown['Total Labor'] && (
                    <>
                      <Styled.BreakdownCont>
                        <span>
                          <b>Total Labor:</b>
                        </span>
                        <span>
                          <b>${orderById?.priceTotals?.lTotal?.toFixed(2)}</b>
                        </span>
                      </Styled.BreakdownCont>
                      <ul>
                        {laborBreakdown?.map((mat) => (
                          <li key={mat?.label}>
                            <Styled.BreakdownCont>
                              <span>{mat?.label}:</span>
                              <span style={{ paddingLeft: '78px' }}>${mat?.value}</span>

                              {mat?.label === 'Labor Subtotal' ? (
                                <ul>
                                  {laborSubtotalBreakdown?.map((mat) => (
                                    <li key={mat?.label}>
                                      <Styled.BreakdownCont>
                                        <span>{mat?.label}:</span>
                                        <span>${mat?.value}</span>
                                      </Styled.BreakdownCont>
                                    </li>
                                  ))}
                                </ul>
                              ) : null}
                            </Styled.BreakdownCont>
                          </li>
                        ))}
                      </ul>
                    </>
                  )}

                  {!!showBreakdown['Gross Profit'] && (
                    <>
                      <Styled.BreakdownCont>
                        <span>
                          <b>Gross Profit:</b>
                        </span>
                        <span>
                          <b>${grossProfit}</b>
                        </span>
                      </Styled.BreakdownCont>
                      <ul>
                        {profilBreakdown?.map((mat) => (
                          <li key={mat?.label}>
                            <Styled.BreakdownCont>
                              <span>{mat?.label}:</span>
                              <span>${mat?.value}</span>
                            </Styled.BreakdownCont>
                          </li>
                        ))}
                      </ul>
                    </>
                  )}
                </Styled.BreakdownWrap>
              </Styled.NewOrderWrapper>

              {/* ========================================= New ========================================= */}

              <SharedStyled.FlexBox
                width="100%"
                margin="20px 0"
                padding="10px 0"
                gap="15px"
                justifyContent="space-between"
                alignItems="center"
              >
                <SharedStyled.FlexBox width="100%" marginTop="20px" gap="15px" alignItems="center">
                  <Button
                    onClick={() => {
                      navigate(`/${operationsFlag ? 'operations' : 'sales'}/opportunity/${oppId}`)
                    }}
                    maxWidth="200px"
                    type="button"
                  >
                    Back
                  </Button>
                </SharedStyled.FlexBox>
                {
                  <Button
                    disabled={operationsFlag}
                    tooltip={!operationsFlag ? '' : `Can’t delete an Order that is in Operations`}
                    tooltipWidth={'180px'}
                    maxWidth="200px"
                    type="button"
                    onClick={onDelete}
                    className="delete"
                    isLoading={btnLoading}
                  >
                    Delete Contract
                  </Button>
                }
              </SharedStyled.FlexBox>
            </Form>
          )
        }}
      </Formik>
      <CustomModal show={showAddModal}>
        <AddNewMaterialForOrder
          onClose={() => {
            setShowAddModal(false)
            setProjectId('')
          }}
          onComplete={() => {
            setShowAddModal(false)
            setProjectId('')
            fetchOrderById()
          }}
          isSupplier={isSupplier}
          orderId={orderId!}
          projectId={projectId}
        />
      </CustomModal>
    </Styled.PrintContainer>
  )
}

export default OrderDetails
