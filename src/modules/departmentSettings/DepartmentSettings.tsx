import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate, useParams } from 'react-router-dom'

import { DeleteIcon } from '../../assets/icons/DeleteIcon'
import { EditIcon } from '../../assets/icons/EditIcon'
import { getDepartments } from '../../logic/apis/department'
import { CustomModal } from '../../shared/customModal/CustomModal'
import { getDataFromLocalStorage, isSuccess, notify } from '../../shared/helpers/util'
import { Table } from '../../shared/table/Table'
import * as SharedStyled from '../../styles/styled'
import { ConfirmationPopUp } from './components/confirmationPopup/ConfirmationPopUp'
import { CreateDepartmentPopUp } from './components/createDepartmentPopUp/CreateDepartmentPopUp'
import { EditDepartmentPopUp } from './components/editDepartmentPopUp/EditDepartmentPopUp'
import * as Styled from './style'
import DeletedDepartmentSettings from './components/deletedDepartmentSettings/DeletedDepartmentSettings'
import TabBar from '../../shared/components/tabBar/TabBar'
import { ButtonCont, SettingsCont } from '../units/style'
import Button from '../../shared/components/button/Button'
import { StorageKey } from '../../shared/helpers/constants'

const DepartmentSettings = () => {
  interface I_Data {
    departmentName: string
    description: string
  }

  const [loading, setLoading] = useState<boolean>(false)
  const [showConfirmationPopUp, setShowConfirmationPopUp] = useState<boolean>(false)
  const [showCreateDepartmentPopUp, setShowCreateDepartmentPopUp] = useState<boolean>(false)
  const [showEditDepartmentPopUp, setShowEditDepartmentPopUp] = useState<boolean>(false)
  const [departmentData, setDepartmentData] = useState<any>({})
  const [detailsUpdate, setDetailsUpdate] = useState(false)
  // const [pageCount, setPageCount] = useState<number>(10)
  const [data, setData] = useState<I_Data[]>([])
  const fetchIdRef = useRef(0)

  const navigate = useNavigate()

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company
  const loadmoreRef = useRef(null)

  const fetchData = useCallback(
    async ({ pageSize, pageIndex }: any) => {
      try {
        // This will get called when the table needs new data
        setLoading(true)
        let receivedData: any = []
        let currentCompanyData: any = localStorage.getItem('currentCompany')

        const departmentResponse = await getDepartments({ deleted: false, skip: pageIndex, limit: pageSize }, false)

        if (isSuccess(departmentResponse)) {
          let statusRes = departmentResponse?.data?.data?.department
          statusRes.forEach((res: any, index: number) => {
            receivedData.push({
              departmentName: res?.name,
              description: res?.description || '-',
              action: (
                <>
                  <SharedStyled.FlexBox width="100%" alignItems="center" gap="10px">
                    <Styled.IconContainer
                      className="delete"
                      onClick={() => {
                        setDepartmentData({ departmentName: res?.name, description: res?.description, id: res?._id })
                        setShowConfirmationPopUp(true)
                      }}
                    >
                      <DeleteIcon />
                    </Styled.IconContainer>
                    <Styled.IconContainer
                      className="edit"
                      onClick={() => {
                        setDepartmentData({ departmentName: res?.name, description: res?.description, id: res?._id })
                        setShowEditDepartmentPopUp(true)
                      }}
                    >
                      <EditIcon />
                    </Styled.IconContainer>
                  </SharedStyled.FlexBox>
                </>
              ),
            })
          })
        } else {
          notify(departmentResponse?.data?.message, 'error')
        }

        // Give this fetch an ID
        const fetchId = ++fetchIdRef.current

        // Set the loading state
        // setLoading(true)

        // We'll even set a delay to simulate a server here
        // setTimeout(() => {
        // Only update the data if this is the latest fetch
        if (fetchId === fetchIdRef.current) {
          const startRow = pageSize * pageIndex
          const endRow = startRow + pageSize
          setData(receivedData.slice(startRow, endRow))

          // Your server could send back total page count.
          // For now we'll just fake it, too
          // setPageCount(Math.ceil(receivedData.length / pageSize))
          // setLoading(false)
        }
        // }, 1000)
      } catch (error) {
        console.error('TeamTable fetchData error', error)
      } finally {
        setLoading(false)
      }
    },
    [detailsUpdate]
  )

  const columns: any = useMemo(
    () => [
      {
        Header: 'Department Name',
        accessor: 'departmentName',
      },
      {
        Header: 'Description',
        accessor: 'description',
      },
      {
        Header: 'Action',
        accessor: 'action',
      },
    ],
    []
  )

  return (
    <SettingsCont gap="24px" className="half">
      <SharedStyled.FlexRow justifyContent="space-between">
        <SharedStyled.SectionTitle>Department</SharedStyled.SectionTitle>
        <ButtonCont>
          <Button
            onClick={() => {
              setShowCreateDepartmentPopUp(true)
            }}
          >
            Add Department
          </Button>
        </ButtonCont>
      </SharedStyled.FlexRow>

      <SharedStyled.FlexRow alignItems="flex-start">
        <SharedStyled.FlexCol gap="24px">
          <TabBar
            tabs={[
              {
                title: 'Active',
                render: () => (
                  <Table
                    columns={columns}
                    data={data}
                    loading={loading}
                    // pageCount={pageCount}
                    fetchData={fetchData}
                    noLink={true}
                    noSearch
                    ref={loadmoreRef}
                    isLoadMoreLoading={loading}
                  />
                ),
              },
              {
                title: 'Inactive',
                render: () => <DeletedDepartmentSettings />,
              },
            ]}
            filterComponent={<></>}
          />
        </SharedStyled.FlexCol>
      </SharedStyled.FlexRow>

      <CustomModal show={showConfirmationPopUp}>
        <ConfirmationPopUp
          setShowConfirmationPopUp={setShowConfirmationPopUp}
          setDetailsUpdate={setDetailsUpdate}
          header="Delete Department"
          departmentData={departmentData}
        />
      </CustomModal>
      <CustomModal show={showCreateDepartmentPopUp}>
        <CreateDepartmentPopUp
          setShowCreateDepartmentPopUp={setShowCreateDepartmentPopUp}
          setDetailsUpdate={setDetailsUpdate}
        />
      </CustomModal>
      <CustomModal show={showEditDepartmentPopUp}>
        <EditDepartmentPopUp
          setShowEditDepartmentPopUp={setShowEditDepartmentPopUp}
          setDetailsUpdate={setDetailsUpdate}
          departmentData={departmentData}
        />
      </CustomModal>
    </SettingsCont>
  )
}

export default DepartmentSettings
