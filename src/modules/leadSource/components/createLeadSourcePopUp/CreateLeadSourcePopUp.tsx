import { Form, Formik } from 'formik'
import { useEffect, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useParams } from 'react-router-dom'
import * as Yup from 'yup'

import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import { createLeadSource } from '../../../../logic/apis/leadSource'
import { getMarketingChannel } from '../../../../logic/apis/marketingChannel'
import { Dropdown } from '../../../../shared/dropDown/Dropdown'
import { getDataFromLocalStorage, isSuccess, notify } from '../../../../shared/helpers/util'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import * as SharedStyled from '../../../../styles/styled'
import * as Styled from './style'
import { ModalHeaderInfo } from '../../../units/components/newUnitModal/style'
import UnitSvg from '../../../../assets/newIcons/unitModal.svg'
import Button from '../../../../shared/components/button/Button'
import { StorageKey } from '../../../../shared/helpers/constants'
import CustomSelect from '../../../../shared/customSelect/CustomSelect'
import Toggle from '../../../../shared/toggle/Toggle'
import { SharedDate } from '../../../../shared/date/SharedDate'

/**
 * InitialValues is an interface declared here so that it can be used as a type for the useState hook
 */
interface InitialValues {
  leadSourceName: string
  description: string
  channel: string
  cost: string | number
  startDate?: string
  endDate?: string
}

/**
 * I_CreateLeadSourcePopUp is an interface which defines the props type which we receive from our parent component to this component
 */
interface I_CreateLeadSourcePopUp {
  setShowCreateLeadSourcePopUp: React.Dispatch<React.SetStateAction<boolean>>
  setDetailsUpdate: React.Dispatch<React.SetStateAction<boolean>>
  leadSourceData?: any
}

export const CreateLeadSourcePopUp = (props: I_CreateLeadSourcePopUp) => {
  const { setShowCreateLeadSourcePopUp, setDetailsUpdate, leadSourceData } = props
  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */
  const [initialValues, setInitialValues] = useState<InitialValues>({
    leadSourceName: '',
    description: '',
    channel: leadSourceData?.channel || '',
    cost: leadSourceData?.cost || '',
    startDate: leadSourceData?.startDate || '',
    endDate: leadSourceData?.endDate || '',
  })

  /**
   * loading will be the loading state when performing the operations
   */
  const [loading, setLoading] = useState<boolean>(false)

  const [channelData, setChannelData] = useState<any>([])
  const [channelIdData, setChannelIdData] = useState<any>({})

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company
  const [isMonthly, setIsMonthly] = useState(false)

  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [inputRef])
  /**
   * Destructuring the values from the props received
   */

  /**
   * LeadSourceCreationFormSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */
  const LeadSourceCreationFormSchema = Yup.object().shape({
    leadSourceName: Yup.string().trim().required('No department name provided.'),
    description: Yup.string().trim().required('No description provided.'),
    cost: Yup.string(),
    startDate: Yup.string().required('Required'),
    endDate: Yup.string(),
  })

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async (submittedValues: InitialValues, { resetForm }: any) => {
    try {
      const [startYear, startMonth] = submittedValues.startDate?.split('-') || []
      const [endYear, endMonth] = submittedValues.endDate?.split('-') || []
      // if (Object.keys(currentCompany).length > 0 && Object.keys(currentMember).length > 0) {
      setLoading(true)
      let dataObj = {
        name: submittedValues.leadSourceName,
        description: submittedValues.description,
        channelId: channelIdData[submittedValues.channel],
        createdBy: currentMember._id,
        startMonth: startMonth ? Number(startMonth) : null,
        startYear: startYear ? Number(startYear) : null,
        endMonth: Number(endMonth) || undefined,
        endYear: Number(endYear) || undefined,
        isMonthly: isMonthly,
        cost: submittedValues.cost ? Number(submittedValues.cost) : undefined,
      }

      let response = await createLeadSource(dataObj)
      if (isSuccess(response)) {
        notify('Lead Source Created Successfully', 'success')
        resetForm()
        setDetailsUpdate((prev) => !prev)
        setLoading(false)
        setShowCreateLeadSourcePopUp(false)
      } else {
        setLoading(false)
        notify(response?.data?.message, 'error')
      }
      // }
    } catch (error) {
      setLoading(false)
      console.error('Lead Source Creation error', error)
    }
  }

  const getAllChannelsDetails = async () => {
    try {
      // if (Object.keys(currentCompany).length > 0) {
      const channelResponse = await getMarketingChannel({}, false)
      if (isSuccess(channelResponse)) {
        let statusRes = channelResponse?.data?.data?.marketingChannel
        let channelObj: any = []
        let channelIdObj: any = {}
        statusRes.forEach((chan: any) => {
          channelObj.push(chan.name)
          channelIdObj = { ...channelIdObj, [chan.name]: chan._id }
        })
        setChannelData(channelObj)
        setChannelIdData(channelIdObj)
      } else {
        notify(channelResponse?.data?.message, 'error')
      }
      // }
    } catch (error) {
      console.error('getDetails error', error)
    }
  }

  useEffect(() => {
    getAllChannelsDetails()
  }, [])

  return (
    <Styled.CreateLeadSourcePopUpContainer>
      {' '}
      <Formik
        initialValues={initialValues}
        onSubmit={handleSubmit}
        validationSchema={LeadSourceCreationFormSchema}
        validateOnChange={true}
        validateOnBlur={false}
      >
        {/* <Styled.ResetpasswordContainer> */}
        {({ touched, errors, resetForm, values, setFieldValue }) => {
          return (
            <>
              <Styled.ModalHeaderContainer>
                <SharedStyled.FlexRow>
                  <img src={UnitSvg} alt="modal icon" />
                  <SharedStyled.FlexCol>
                    <Styled.ModalHeader>Create Lead Source</Styled.ModalHeader>
                  </SharedStyled.FlexCol>
                </SharedStyled.FlexRow>
                <Styled.CrossContainer
                  onClick={() => {
                    setShowCreateLeadSourcePopUp(false)
                    setLoading(false)
                    resetForm()
                  }}
                >
                  <CrossIcon />
                </Styled.CrossContainer>
              </Styled.ModalHeaderContainer>
              <SharedStyled.SettingModalContentContainer>
                <Form className="form">
                  <SharedStyled.Content maxWidth="706px" width="100%" disableBoxShadow={true} noPadding={true}>
                    <InputWithValidation
                      labelName="Name"
                      stateName="leadSourceName"
                      error={touched.leadSourceName && errors.leadSourceName ? true : false}
                      passRef={inputRef}
                    />
                    <InputWithValidation
                      labelName="Description"
                      stateName="description"
                      error={touched.description && errors.description ? true : false}
                    />
                    {/* <Dropdown
                      value={values.channel}
                      labelName="Channel"
                      stateName="channel"
                      dropDownData={channelData}
                      setFieldValue={setFieldValue}
                      error={touched.channel && errors.channel ? true : false}
                    /> */}

                    <CustomSelect
                      setValue={() => {}}
                      value={values.channel}
                      labelName="Channel"
                      stateName="channel"
                      dropDownData={channelData}
                      setFieldValue={setFieldValue}
                      error={touched.channel && errors.channel ? true : false}
                      margin="8px 0 0 0"
                    />

                    <SharedDate
                      value={values.startDate}
                      labelName="Start Date"
                      stateName="startDate"
                      type="month"
                      error={touched.startDate && errors.startDate ? true : false}
                      setFieldValue={setFieldValue}
                    />

                    <SharedDate
                      value={values.endDate}
                      labelName="End Date"
                      stateName="endDate"
                      type="month"
                      min={values.startDate}
                      disabled={!values.startDate}
                      error={touched.endDate && errors.endDate ? true : false}
                      setFieldValue={setFieldValue}
                    />

                    <SharedStyled.FlexCol margin="14px 0">
                      <h2>Marketing Spend</h2>

                      <SharedStyled.FlexRow gap="20px" width="max-content" margin="10px 0 0 0">
                        <Toggle
                          title="Once"
                          width="max-content"
                          isToggled={isMonthly}
                          onToggle={() => {
                            setIsMonthly((prev: boolean) => !prev)
                          }}
                        />
                        <p>Monthly</p>
                      </SharedStyled.FlexRow>
                    </SharedStyled.FlexCol>

                    <InputWithValidation
                      forceType="number"
                      labelName="Amount"
                      stateName="cost"
                      error={touched.cost && errors.cost ? true : false}
                    />

                    <SharedStyled.ButtonContainer marginTop="26px">
                      <Button type="submit" isLoading={loading}>
                        Create Lead Source
                      </Button>
                    </SharedStyled.ButtonContainer>
                  </SharedStyled.Content>
                </Form>
              </SharedStyled.SettingModalContentContainer>
            </>
          )
        }}
      </Formik>
    </Styled.CreateLeadSourcePopUpContainer>
  )
}
