import { useCallback, useMemo, useRef, useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { RevokeIcon } from '../../../../assets/icons/RevokeIcon'
import { getLeadSources } from '../../../../logic/apis/leadSource'
import { CustomModal } from '../../../../shared/customModal/CustomModal'
import { getDataFromLocalStorage, isSuccess, notify } from '../../../../shared/helpers/util'
import { Table } from '../../../../shared/table/Table'
import * as SharedStyled from '../../../../styles/styled'
import { ConfirmationPopUp } from '../confirmationPopup/ConfirmationPopUp'
import * as Styled from './style'
import { StorageKey } from '../../../../shared/helpers/constants'

const DeletedLeadSourceSettings = () => {
  interface I_Data {
    leadSourceName: string
    description: string
    channel: string
  }

  const [loading, setLoading] = useState<boolean>(false)

  const [showConfirmationPopUp, setShowConfirmationPopUp] = useState<boolean>(false)

  const [detailsUpdate, setDetailsUpdate] = useState(false)
  const [leadSourceData, setLeadSourceData] = useState<any>({})
  const [pageCount, setPageCount] = useState<number>(10)
  const [data, setData] = useState<I_Data[]>([])
  const fetchIdRef = useRef(0)

  const loadmoreRef = useRef(null)

  const navigate = useNavigate()

  const fetchData = useCallback(
    async ({ pageSize, pageIndex }: any) => {
      try {
        setLoading(true)
        // This will get called when the table needs new data

        let receivedData: any = []
        let currentCompanyData: any = localStorage.getItem('currentCompany')

        const leadSourceResponse = await getLeadSources({ skip: pageIndex, limit: pageSize }, true)

        if (isSuccess(leadSourceResponse)) {
          let statusRes = leadSourceResponse?.data?.data?.leadSource
          statusRes.forEach((res: any, index: number) => {
            receivedData.push({
              leadSourceName: res?.name || '-',
              description: res?.description || '-',
              channel: res?.channelName[0] || '-',
              action: (
                <>
                  <SharedStyled.FlexBox width="100%" alignItems="center" gap="10px">
                    <Styled.IconContainer
                      content="Revoke"
                      className="restore"
                      onClick={() => {
                        setLeadSourceData({
                          leadSourceName: res?.name || '-',
                          description: res?.description || '-',
                          channel: res?.channelName[0] || '-',
                          id: res?._id,
                        })
                        setShowConfirmationPopUp(true)
                      }}
                    >
                      <RevokeIcon />
                    </Styled.IconContainer>
                  </SharedStyled.FlexBox>
                </>
              ),
            })
          })
        } else {
          notify(leadSourceResponse?.data?.message, 'error')
        }
        // Give this fetch an ID
        const fetchId = ++fetchIdRef.current

        // Set the loading state
        // setLoading(true)

        // We'll even set a delay to simulate a server here
        // setTimeout(() => {
        // Only update the data if this is the latest fetch
        if (fetchId === fetchIdRef.current) {
          const startRow = pageSize * pageIndex
          const endRow = startRow + pageSize
          setData(receivedData.slice(startRow, endRow))

          // Your server could send back total page count.
          // For now we'll just fake it, too
          // setPageCount(Math.ceil(receivedData.length / pageSize))
          // setLoading(false)
        }
        // }, 1000)
      } catch (error) {
        console.error('TeamTable fetchData error', error)
      } finally {
        setLoading(false)
      }
    },
    [detailsUpdate]
  )

  const columns: any = useMemo(
    () => [
      {
        Header: 'Source',
        accessor: 'leadSourceName',
      },
      {
        Header: 'Description',
        accessor: 'description',
      },
      {
        Header: 'Channel',
        accessor: 'channel',
      },
      {
        Header: 'Action',
        accessor: 'action',
      },
    ],
    []
  )

  return (
    <>
      {' '}
      <>
        <Table
          columns={columns}
          data={data}
          loading={loading}
          // pageCount={pageCount}
          fetchData={fetchData}
          noLink={true}
          ref={loadmoreRef}
          isLoadMoreLoading={loading}
        />

        <CustomModal show={showConfirmationPopUp}>
          <ConfirmationPopUp
            setShowConfirmationPopUp={setShowConfirmationPopUp}
            setDetailsUpdate={setDetailsUpdate}
            header="Restore Lead Source"
            leadSourceData={leadSourceData}
          />
        </CustomModal>
      </>
    </>
  )
}

export default DeletedLeadSourceSettings
