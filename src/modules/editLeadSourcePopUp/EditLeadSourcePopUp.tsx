import { Form, Formik } from 'formik'
import { useEffect, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useParams } from 'react-router-dom'
import * as Yup from 'yup'

import { CrossIcon } from '../../assets/icons/CrossIcon'
import { getLeadSourceById, updateLeadSource } from '../../logic/apis/leadSource'
import { getMarketingChannel } from '../../logic/apis/marketingChannel'
import { Dropdown } from '../../shared/dropDown/Dropdown'
import { getDataFromLocalStorage, isSuccess, notify } from '../../shared/helpers/util'
import { InputWithValidation } from '../../shared/inputWithValidation/InputWithValidation'
import * as SharedStyled from '../../styles/styled'
import * as Styled from './style'
import UnitSvg from '../../assets/newIcons/unitModal.svg'
import { ModalHeaderInfo } from '../../modules/units/components/newUnitModal/style'
import Button from '../../shared/components/button/Button'
import { StorageKey } from '../../shared/helpers/constants'
import { SharedDate } from '../../shared/date/SharedDate'
import Toggle from '../../shared/toggle/Toggle'
import useFetch from '../../logic/apis/useFetch'
import dayjs from 'dayjs'
import { SLoader } from '../../shared/components/loader/Loader'
import CustomSelect from '../../shared/customSelect/CustomSelect'

/**
 * InitialValues is an interface declared here so that it can be used as a type for the useState hook
 */
interface InitialValues {
  leadSourceName: string
  description: string
  channel: string
  cost: string | number
  startDate?: string
  endDate?: string
}

/**
 * I_CreateDepartmentPopUp is an interface which defines the props type which we receive from our parent component to this component
 */
interface I_EditDepartmentPopUp {
  setShowEditLeadSourcePopUp: React.Dispatch<React.SetStateAction<boolean>>
  setDetailsUpdate: React.Dispatch<React.SetStateAction<boolean>>
  leadSourceData: any
  onDeleteClick?: () => void
}

export const EditLeadSourcePopUp = (props: I_EditDepartmentPopUp) => {
  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */

  /**
   * loading will be the loading state when performing the operations
   */
  const [loading, setLoading] = useState<boolean>(false)

  const [channelData, setChannelData] = useState<any>([])
  const [channelIdData, setChannelIdData] = useState<any>({})
  const [isMonthly, setIsMonthly] = useState(false)

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company

  const { data, loading: leadSrcLoading } = useFetch({
    fetchFn: () => leadSourceData?.id && getLeadSourceById(leadSourceData?.id),
  })

  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [inputRef])
  /**
   * Destructuring the values from the props received
   */
  const { setShowEditLeadSourcePopUp, setDetailsUpdate, leadSourceData, onDeleteClick } = props

  const [initialValues, setInitialValues] = useState<InitialValues>({
    leadSourceName: '',
    description: '',
    channel: '',
    cost: '',
    startDate: '',
    endDate: '',
  })

  useEffect(() => {
    if (data?.leadSource?._id) {
      setInitialValues({
        ...initialValues,
        cost: data?.leadSource?.cost || '',
        startDate: data?.leadSource?.startYear
          ? dayjs(`${data?.leadSource?.startYear}-${data?.leadSource?.startMonth}`).format('YYYY-MM')
          : '',
        endDate: data?.leadSource?.endYear
          ? dayjs(`${data?.leadSource?.endYear}-${data?.leadSource?.endMonth}`).format('YYYY-MM')
          : '',
      })
      setIsMonthly(data?.leadSource?.isMonthly)
    }
  }, [data?.leadSource])

  /**
   * LeadSourceEditFormSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */
  const LeadSourceEditFormSchema = Yup.object().shape({
    leadSourceName: Yup.string().trim().required('No leadSourceName name provided.'),
    description: Yup.string().trim().required('No description provided.'),
    cost: Yup.string(),
    startDate: Yup.string().required('Required'),
    endDate: Yup.string(),
  })

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async (submittedValues: InitialValues, { resetForm }: any) => {
    try {
      // if (Object.keys(currentCompany).length > 0 && Object.keys(currentMember).length > 0) {

      const [startYear, startMonth] = submittedValues.startDate?.split('-') || []
      const [endYear, endMonth] = submittedValues.endDate?.split('-') || []

      setLoading(true)
      let dataObj = {
        leadSourceId: leadSourceData.id,
        name: submittedValues.leadSourceName,
        channelId: channelIdData[submittedValues.channel],
        description: submittedValues.description,
        createdBy: currentMember._id,
        startMonth: startMonth ? Number(startMonth) : null,
        startYear: startYear ? Number(startYear) : null,
        endMonth: endMonth ? Number(endMonth) : null,
        endYear: endYear ? Number(endYear) : null,
        isMonthly: isMonthly,
        cost: submittedValues.cost ? Number(submittedValues.cost) : 0,
      }

      let response = await updateLeadSource(dataObj)
      if (isSuccess(response)) {
        notify('Lead Source Edited Successfully', 'success')
        resetForm()
        setDetailsUpdate((prev) => !prev)
        setLoading(false)
        setShowEditLeadSourcePopUp(false)
      } else {
        setLoading(false)
        notify(response?.data?.message, 'error')
      }
      // }
    } catch (error) {
      setLoading(false)
      console.error('Lead Source Creation error', error)
    }
  }

  const setInitialValuesFunc = () => {
    try {
      setInitialValues({
        ...initialValues,
        leadSourceName: leadSourceData.leadSourceName,
        description: leadSourceData.description,
        channel: leadSourceData.channel,
      })
    } catch (error) {
      console.log('Edit Lead Source setInitialValues error', error)
    }
  }

  const getAllChannelsDetails = async () => {
    try {
      // if (Object.keys(currentCompany).length > 0) {
      const channelResponse = await getMarketingChannel({}, false)

      if (isSuccess(channelResponse)) {
        let statusRes = channelResponse?.data?.data?.marketingChannel
        let channelObj: any = []
        let channelIdObj: any = {}
        statusRes.forEach((chan: any) => {
          channelObj.push(chan.name)
          channelIdObj = { ...channelIdObj, [chan.name]: chan._id }
        })
        setChannelData(channelObj)
        setChannelIdData(channelIdObj)
      } else {
        notify(channelResponse?.data?.message, 'error')
      }
      // }
    } catch (error) {
      console.error('getAllChannelsDetails error', error)
    }
  }

  useEffect(() => {
    getAllChannelsDetails()
  }, [currentCompany])

  useEffect(() => {
    setInitialValuesFunc()
  }, [leadSourceData])

  return (
    <Styled.EditLeadSourcePopUpContainer>
      {' '}
      <Formik
        initialValues={initialValues}
        onSubmit={handleSubmit}
        validationSchema={LeadSourceEditFormSchema}
        validateOnChange={true}
        validateOnBlur={false}
        enableReinitialize={true}
      >
        {({ touched, errors, resetForm, values, setFieldValue }) => {
          return (
            <>
              <Styled.ModalHeaderContainer>
                <SharedStyled.FlexRow>
                  <img src={UnitSvg} alt="modal icon" />
                  <SharedStyled.FlexCol>
                    <Styled.ModalHeader>Edit Lead Source</Styled.ModalHeader>
                  </SharedStyled.FlexCol>
                </SharedStyled.FlexRow>

                <Styled.CrossContainer
                  onClick={() => {
                    setShowEditLeadSourcePopUp(false)
                    setLoading(false)
                    resetForm()
                  }}
                >
                  <CrossIcon />
                </Styled.CrossContainer>
              </Styled.ModalHeaderContainer>

              <SharedStyled.SettingModalContentContainer>
                <Form className="form">
                  {leadSrcLoading ? (
                    <>
                      <SharedStyled.FlexCol gap="30px">
                        <SLoader height={60} />
                        <SLoader height={60} />
                        <SLoader height={60} />
                        <SLoader height={60} />
                      </SharedStyled.FlexCol>
                    </>
                  ) : (
                    <SharedStyled.Content maxWidth="706px" width="100%" disableBoxShadow={true} noPadding={true}>
                      <InputWithValidation
                        labelName="Lead Source Name"
                        stateName="leadSourceName"
                        error={touched.leadSourceName && errors.leadSourceName ? true : false}
                        passRef={inputRef}
                      />

                      <InputWithValidation
                        labelName="Description"
                        stateName="description"
                        error={touched.description && errors.description ? true : false}
                      />
                      {/* <Dropdown
                        value={values.channel}
                        labelName="Channel"
                        stateName="channel"
                        dropDownData={channelData}
                        setFieldValue={setFieldValue}
                        error={touched.channel && errors.channel ? true : false}
                      /> */}

                      <CustomSelect
                        setValue={() => {}}
                        value={values.channel}
                        labelName="Channel"
                        stateName="channel"
                        dropDownData={channelData}
                        setFieldValue={setFieldValue}
                        error={touched.channel && errors.channel ? true : false}
                        margin="8px 0 0 0"
                      />

                      <SharedDate
                        value={values.startDate}
                        labelName="Start Date"
                        stateName="startDate"
                        type="month"
                        error={touched.startDate && errors.startDate ? true : false}
                        setFieldValue={setFieldValue}
                      />

                      <SharedDate
                        value={values.endDate}
                        labelName="End Date"
                        stateName="endDate"
                        type="month"
                        min={values.startDate}
                        disabled={!values.startDate}
                        error={touched.endDate && errors.endDate ? true : false}
                        setFieldValue={setFieldValue}
                      />

                      <SharedStyled.FlexCol margin="14px 0">
                        <h2>Marketing Spend</h2>

                        <SharedStyled.FlexRow gap="20px" width="max-content" margin="10px 0 0 0">
                          <Toggle
                            title="Once"
                            width="max-content"
                            isToggled={isMonthly}
                            onToggle={() => {
                              setIsMonthly((prev: boolean) => !prev)
                            }}
                          />
                          <p>Monthly</p>
                        </SharedStyled.FlexRow>
                      </SharedStyled.FlexCol>

                      <InputWithValidation
                        forceType="number"
                        labelName="Amount"
                        stateName="cost"
                        error={touched.cost && errors.cost ? true : false}
                      />
                      <SharedStyled.ButtonContainer marginTop="26px">
                        <Button type="button" className="delete" onClick={onDeleteClick}>
                          Delete Lead Source
                        </Button>
                        <Button type="submit" isLoading={loading}>
                          Save Changes
                        </Button>
                      </SharedStyled.ButtonContainer>
                    </SharedStyled.Content>
                  )}
                </Form>
              </SharedStyled.SettingModalContentContainer>
            </>
          )
        }}
      </Formik>
    </Styled.EditLeadSourcePopUpContainer>
  )
}
