import { useCallback, useEffect, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate, useParams } from 'react-router-dom'

import { getUnitsApi } from '../../logic/apis/projects'
import Button from '../../shared/components/button/Button'
import TabBar from '../../shared/components/tabBar/TabBar'
import { CustomModal } from '../../shared/customModal/CustomModal'
import { getDataFromLocalStorage, isSuccess, notify } from '../../shared/helpers/util'
import { Table } from '../../shared/table/Table'
import * as SharedStyled from '../../styles/styled'
import DeletedUnits from '../deleted/deletedUnits/DeletedUnits'
import NewUnitModal from './components/newUnitModal/NewUnitModal'
import { ButtonCont, SettingsCont } from './style'

const sortOptions = [
  {
    label: 'Newest',
    value: 'newest',
  },
  {
    label: 'Name',
    value: 'name',
  },
]

export interface IFullUnit {
  _id: string
  name: string
  symbol: string
  createdBy: string
}

export const PROJECT_TYPE: any = {
  1: 're-roof',
  2: 'roof-repair',
}

const Units = () => {
  const [newUnitsModal, setNewUnitsModal] = useState(false)
  const [editUnitVals, setEditUnitVals] = useState<IFullUnit | null>(null)
  const [unitTableValues, setUnitTableValues] = useState([])
  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company
  const loadmoreRef = useRef(null)

  const [loading, setLoading] = useState<boolean>(false)
  const fetchIdRef = useRef(0)
  // const [pageCount, setPageCount] = useState<number>(10)
  const [data, setData] = useState<any[]>([])
  const [detailsUpdate, setDetailsUpdate] = useState(false)
  const columns = [
    {
      Header: 'Unit Name',
      accessor: 'name',
    },
    {
      Header: 'Symbol',
      accessor: 'symbol',
    },
  ]

  const fetchData = useCallback(
    async ({ pageSize, pageIndex }) => {
      try {
        // This will get called when the table needs new data
        setLoading(true)
        let receivedData: any = []
        const currentCompany: any = getDataFromLocalStorage('currentCompany')

        const clientResponse = await getUnitsApi({ deleted: false, limit: pageSize })

        if (isSuccess(clientResponse)) {
          let statusRes = clientResponse?.data?.data?.unit
          const tableData = statusRes.reduce((prev: any, cur: any) => {
            return [
              ...prev,
              {
                ...cur,
                name: cur.name,
                symbol: cur.symbol,
              },
            ]
          }, [])

          receivedData.push(...tableData)
        } else {
          notify(clientResponse?.data?.message, 'error')
        }

        // Give this fetch an ID
        const fetchId = ++fetchIdRef.current

        // Set the loading state
        // setLoading(true)

        // We'll even set a delay to simulate a server here
        // setTimeout(() => {
        // Only update the data if this is the latest fetch
        if (fetchId === fetchIdRef.current) {
          const startRow = pageSize * pageIndex
          const endRow = startRow + pageSize
          setData(receivedData.slice(startRow, endRow))

          // Your server could send back total page count.
          // For now we'll just fake it, too
          // setPageCount(Math.ceil(receivedData.length / pageSize))
          // setLoading(false)
        }
        // }, 1000)
      } catch (error) {
        console.error('TeamTable fetchData error', error)
      } finally {
        setLoading(false)
      }
    },
    [detailsUpdate]
  )

  {
    /* useEffect(() => {
    if (currentCompany && currentCompany._id) fetchUnitsData()
  }, [currentCompany])

  const fetchUnitsData = async (limit?: number) => {
    try {
      setLoading(true)
      const res = await getUnitsApi({ companyId: currentCompany._id, deleted: false, limit })
      if (isSuccess(res)) {
        console.log({ res: res.data.data.unit })
        const { unit } = res.data.data
        const tableData = unit.reduce((prev: any, cur: any) => {
          return [
            ...prev,
            {
              ...cur,
              name: cur.name,
              symbol: cur.symbol,
            },
          ]
        }, [])
        setUnitTableValues(tableData)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('Failed init fetch', err)
    } finally {
      setLoading(false)
    }
  }*/
  }

  return (
    <SettingsCont gap="24px" className="half">
      <SharedStyled.FlexRow justifyContent="space-between">
        <SharedStyled.SectionTitle>Units</SharedStyled.SectionTitle>
        <ButtonCont>
          <Button
            onClick={() => {
              setNewUnitsModal(true)
            }}
          >
            Add Units
          </Button>
        </ButtonCont>
      </SharedStyled.FlexRow>

      <SharedStyled.FlexRow alignItems="flex-start">
        <SharedStyled.FlexCol gap="24px">
          <TabBar
            tabs={[
              {
                title: 'Active',
                render: () => (
                  <Table
                    noOverflow
                    columns={columns}
                    data={data}
                    loading={loading}
                    fetchData={fetchData}
                    noSearch
                    minWidth=""
                    noBorder
                    onRowClick={(vals) => {
                      setNewUnitsModal(true)
                      setEditUnitVals(vals)
                    }}
                    ref={loadmoreRef}
                    isLoadMoreLoading={loading}
                  />
                ),
              },
              {
                title: 'Inactive',
                render: () => <DeletedUnits />,
              },
            ]}
            filterComponent={<></>}
          />
        </SharedStyled.FlexCol>
      </SharedStyled.FlexRow>

      <CustomModal show={newUnitsModal}>
        <NewUnitModal
          onClose={() => {
            setNewUnitsModal(false)
            setEditUnitVals(null)
          }}
          onComplete={() => {
            fetchData({ pageIndex: 0, pageSize: 20 })
          }}
          isEditing={!!editUnitVals}
          inputData={editUnitVals}
        />
      </CustomModal>
    </SettingsCont>
  )
}

export default Units
