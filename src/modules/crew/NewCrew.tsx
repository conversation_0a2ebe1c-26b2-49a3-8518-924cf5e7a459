import { useState, useRef, useCallback, useMemo, useEffect } from 'react'
import { useParams } from 'react-router-dom'

import * as SharedStyled from '../../styles/styled'
import TabBar from '../../shared/components/tabBar/TabBar'
import { StorageKey, WORK_TYPE1 } from '../../shared/helpers/constants'
import { getDataFromLocalStorage, isSuccess, notify } from '../../shared/helpers/util'
import { FlexRow, SectionTitle, FlexCol } from '../../styles/styled'
import { ButtonCont, SettingsCont } from '../units/style'
import NestedTable from '../../shared/table/NestedTable'
import { getCompanyCrews } from '../../logic/apis/crew'
import { colors } from '../../styles/theme'
import { useAppSelector } from '../../logic/redux/reduxHook'
import { CustomModal } from '../../shared/customModal/CustomModal'
import { CrewManagementForm } from './components/crewManagementForm/CrewManagementForm'
import Button from '../../shared/components/button/Button'
import ProfileInfo from '../../shared/components/profileInfo/ProfileInfo'
import RetiredCrew from './RetiredCrew'
import { IconContainer } from './components/CrewMember/style'
import { DoubleArrowUpIcon } from '../../assets/icons/DoubleArrowUpIcon'
import EditIcon from '../../assets/newIcons/edit.svg'
import RemoveIcon from '../../assets/newIcons/delete.svg'
import { RoundButton } from '../../shared/components/button/style'
import { EditCrewForm } from './components/CrewMember/components/editCrewForm/EditCrewForm'
import { CrewPopup } from './components/CrewMember/components/crewPopup/CrewPopup'

const crewColumns = [
  { header: 'Name', field: '', component: 'component' },
  { header: 'Manager', field: 'managerName' },
  { header: 'Type', field: 'type' },
  { header: 'Foreman', field: 'foremanName', empty: '---------' },
  {
    header: 'Actions',
    field: 'actions',
  },
]

const sortOptions = [
  {
    label: 'Newest',
    value: 'newest',
  },
  {
    label: 'Name',
    value: 'name',
  },
]

const NewCrew = () => {
  const [loading, setLoading] = useState<boolean>(false)
  const [showEditCrewForm, setShowEditCrewForm] = useState<boolean>(false)
  const [detailsUpdate, setDetailsUpdate] = useState<boolean>(false)
  const [startDate, setStartDate] = useState<any>('')
  const [crewId, setCrewId] = useState('')

  const [showAddCrewForm, setShowAddCrewForm] = useState<boolean>(false)
  const [showRetiredCrew, setShowRetiredCrew] = useState<boolean>(false)
  const [showRetiredCrewButton, setShowRetiredCrewButton] = useState<boolean>(false)
  const [crewDetails, setCrewDetails] = useState<any>({})
  const [showConfirmationPopUp, setShowConfirmationPopUp] = useState<boolean>(false)
  const [update, setUpdate] = useState<boolean>(false)
  const [pageCount, setPageCount] = useState<number>(10)
  const [data, setData] = useState<any>([])
  const [noData, setNoData] = useState(false)
  const fetchIdRef = useRef(0)

  const [selectedRow, setSelectedRow] = useState<string[]>([])
  const [crewData, setCrewData] = useState([])

  const [showRetireCrewForm, setShowRetireCrewForm] = useState<boolean>(false)
  const [currentCrewMemberId, setCurrentCrewMemberId] = useState('')
  const [currentMemberId, setCurrentMemberId] = useState('')

  const globalSelector = useAppSelector((state: any) => state)
  const { currentCompany, position } = globalSelector.company
  const { justInvited } = globalSelector.invitation

  const triggerRefetch = () => {
    refetchCrew?.()
  }

  const fetchData = useCallback(
    async ({ pageSize, pageIndex }: any) => {
      try {
        // This will get called when the table needs new data

        let receivedData: any = []
        let receivedData1: any = []

        let currentCompanyData: any = localStorage.getItem('currentCompany')

        const statusResponse = await getCompanyCrews({ retired: false, deleted: false })

        const statusResponse1 = await getCompanyCrews({ retired: true, deleted: false })
        if (isSuccess(statusResponse) && isSuccess(statusResponse1)) {
          let statusRes = statusResponse?.data?.data?.crew
          let statusRes1 = statusResponse1?.data?.data?.crew
          statusRes.forEach((res: any, index: number) => {
            receivedData.push({
              crewName: res.name,
              type: WORK_TYPE1[res.workType],
              manager: res.managerName,
              foreman: res.foremanName ? res.foremanName : '-',
              managerId: res.managerId,
              crewId: res._id,
              link: true,
            })
          })
          if (statusRes1.length > 0) {
            setShowRetiredCrewButton(true)
          }
          statusRes1.forEach((res: any, index: number) => {
            receivedData1.push({
              crewName: res.name,
              type: WORK_TYPE1[res.workType],
              manager: res.managerName,
              foreman: res.retireDate ? (
                <SharedStyled.FlexBox width="100%" alignItems="center" gap="5px">
                  <>
                    Retired on:
                    {new Date(res.retireDate.slice(0, 10)).toLocaleDateString('en-us', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'short',
                      day: 'numeric',
                    })}
                  </>
                  <SharedStyled.Button
                    maxWidth="90px"
                    mediaFontSize="12px"
                    maxHeight="28px"
                    bgColor={colors.warning}
                    onClick={() => {
                      setCrewDetails({
                        name: res?.name,
                        id: res?._id,
                      })
                      setShowConfirmationPopUp(true)
                    }}
                  >
                    Reactivate
                  </SharedStyled.Button>
                </SharedStyled.FlexBox>
              ) : (
                '-'
              ),
              managerId: res.managerId,
              crewId: res._id,
              retired: true,
              link: false,
            })
          })
        } else {
          notify(statusResponse?.data?.message, 'error')
          notify(statusResponse1?.data?.message, 'error')
        }

        if (showRetiredCrew) {
          receivedData = [...receivedData, ...receivedData1]
          if (noData) {
            setNoData(false)
          }
        } else {
          if (receivedData.length === 0) {
            setNoData(true)
          }
        }

        // Give this fetch an ID
        const fetchId = ++fetchIdRef.current

        // Set the loading state
        setLoading(true)

        // We'll even set a delay to simulate a server here
        // setTimeout(() => {
        // Only update the data if this is the latest fetch
        if (fetchId === fetchIdRef.current) {
          const startRow = pageSize * pageIndex
          const endRow = startRow + pageSize
          setData(receivedData.slice(startRow, endRow))

          // Your server could send back total page count.
          // For now we'll just fake it, too
          setPageCount(Math.ceil(receivedData.length / pageSize))
          setLoading(false)
        }
        // }, 1000)
      } catch (error) {
        console.error('TeamTable fetchData error', error)
      }
    },
    [showRetiredCrew, update]
  )

  const columns: any = useMemo(
    () => [
      {
        Header: 'Crew Name',
        accessor: 'crewName', // accessor is the "key" in the data
      },
      {
        Header: 'Type',
        accessor: 'type',
      },
      {
        Header: 'Manager',
        accessor: 'manager',
      },
      {
        Header: 'Foreman',
        accessor: 'foreman',
      },
      {
        Header: 'Actions',
        accessor: 'actions',
      },
    ],
    []
  )

  const getDetails = async () => {
    try {
      setLoading(true)
      let receivedData: any = []
      // if (Object.keys(currentCompany).length > 0) {
      // const statusResponse = await getCompanyCrews(
      //   { retired: false, deleted: false, companyId: currentCompany._id },
      //   id
      // )
      // const statusResponse1 = await getCompanyCrews(
      //   { retired: true, deleted: false, companyId: currentCompany._id },
      //   id
      // )
      // const apiCalls = [
      const statusResponse = await getCompanyCrews({ retired: false, deleted: false })
      // getCompanyCrews({ retired: true, deleted: false, companyId: currentCompany._id }, id),
      // ]
      // const [statusResponse, statusResponse1] = await Promise.all(apiCalls)
      // if (isSuccess(statusResponse) && isSuccess(statusResponse1)) {
      if (isSuccess(statusResponse)) {
        let statusRes = statusResponse?.data?.data?.crew

        statusRes.forEach((res: any, index: number) => {
          receivedData.push({
            ...res,
            crewName: res.name,
            type: WORK_TYPE1[res.workType],
            manager: res.managerName,
            foreman: res.foremanName ? res.foremanName : '-',
            managerId: res.managerId,
            crewId: res._id,
            link: true,
            actions: (
              <SharedStyled.FlexBox width="100%" alignItems="center" gap="10px">
                <>
                  <SharedStyled.TooltipContainer
                    width={'100px'}
                    onClick={(e: any) => {
                      e.stopPropagation()
                    }}
                    className="crew"
                  >
                    <span className="tooltip-content">Edit</span>

                    <RoundButton
                      className="edit"
                      onClick={(e) => {
                        e.stopPropagation()
                        setCrewId(res?._id)
                        setShowEditCrewForm(true)
                      }}
                    >
                      <img src={EditIcon} alt="edit icon" />
                    </RoundButton>
                  </SharedStyled.TooltipContainer>
                </>
              </SharedStyled.FlexBox>
            ),
          })
        })

        setCrewData(receivedData?.sort((a: any, b: any) => a?.order - b?.order))
      } else {
        notify(statusResponse?.data?.message, 'error')
      }
      // }
    } catch (error) {
      console.error('getDetails error', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    getDetails()
  }, [])

  const refetchCrew = () => {
    getDetails()
  }

  // useEffect(() => {
  //   // fetchData({ pageSize: 10, pageIndex: 0 })
  // }, [showRetiredCrew])

  return (
    <>
      {loading ? (
        <>
          <SharedStyled.Skeleton custWidth="100%" custHeight={'51px'} />
          <SharedStyled.Skeleton custWidth="100%" custHeight="50%" custMarginTop="10px" />
          <SharedStyled.Skeleton custWidth="100%" custHeight="50%" custMarginTop="10px" />
        </>
      ) : (
        <SettingsCont gap="24px">
          <FlexRow justifyContent="space-between" flexWrap="wrap">
            <SectionTitle>Crew</SectionTitle>

            <ButtonCont>
              <Button
                onClick={() => {
                  setShowAddCrewForm(true)
                  setSelectedRow([])
                }}
              >
                Add Crew
              </Button>
            </ButtonCont>
          </FlexRow>

          <FlexRow alignItems="flex-start">
            <FlexCol gap="24px">
              <TabBar
                tabs={[
                  {
                    title: 'Active',
                    render: () => (
                      <NestedTable
                        columns={crewColumns}
                        data={crewData}
                        isLoading={false}
                        onRowClick={(val) => {
                          if (selectedRow?.includes(val?._id)) {
                            selectedRow?.splice(selectedRow.indexOf(val?._id), 1)
                            setSelectedRow([...selectedRow])
                          } else {
                            setSelectedRow([...selectedRow, val?._id])
                          }
                        }}
                        Component={ProfileInfo}
                        className="scroll nested"
                        selectedRows={selectedRow}
                        refetchCrew={refetchCrew}
                      />
                    ),
                  },

                  {
                    title: 'Retired',
                    render: () => <RetiredCrew refetchCrew={refetchCrew} />,
                  },
                ]}
                filterComponent={<></>}
              />
            </FlexCol>
          </FlexRow>
          <CustomModal show={showAddCrewForm}>
            <CrewManagementForm
              setShowAddCrewForm={setShowAddCrewForm}
              setUpdate={setUpdate}
              setNoData={setNoData}
              onSuccess={() => {
                refetchCrew()
              }}
            />
          </CustomModal>

          <CustomModal show={showEditCrewForm}>
            <EditCrewForm
              setShowEditCrewFormPopup={setShowEditCrewForm}
              setDetailsUpdate={setDetailsUpdate}
              startDate={startDate}
              crewId={crewId}
              triggerRefetch={triggerRefetch}
              setShowRetireForm={setShowRetireCrewForm}
            />
          </CustomModal>

          <CustomModal show={showRetireCrewForm}>
            <CrewPopup
              setShowCrewPopup={setShowRetireCrewForm}
              header="Retire Crew"
              inputLabel="Date"
              submitButtonColor={colors.error}
              buttonText="Retire Crew"
              loaderText="Retiring Crew.."
              currentCrewMemberId={currentCrewMemberId}
              currentMemberId={currentMemberId}
              startDate={startDate}
              crewId={crewId}
              triggerRefetch={triggerRefetch}
            />
          </CustomModal>
        </SettingsCont>
      )}
    </>
  )
}

export default NewCrew

// const RenderInactive = () => {
//   const currentCompany = getDataFromLocalStorage(StorageKey.currentCompany)
//   const [selectedRow, setSelectedRow] = useState<string[]>([])

//   // const { data, isLoading } = getDeletedCrews(currentCompany?._id)

//   return (
//     <>
//       <NestedTable
//         columns={crewColumns}
//         iconType="restore"
//         data={[]}
//         isLoading={false}
//         Component={ProfileInfo}
//         className="scroll"
//       />
//     </>
//   )
// }
// const RetiredComponent = () => {
//   const currentCompany = getDataFromLocalStorage(StorageKey.currentCompany)

//   const { mutate: reActivateCrew } = reActivateCrewMutation()

//   const { data, isLoading } = getRetiredCrews(currentCompany?._id)

//   return (
//     <>
//       <NestedTable
//         hasOption
//         iconTable
//         iconType="restore"
//         columns={crewColumns}
//         data={data?.data?.crew}
//         isLoading={false}
//         Component={Profile}
//         onLeftIconClick={(data) => {
//           reActivateCrew({
//             companyId: data?.companyId,
//             crewId: data?._id,
//           })
//         }}
//         className="scroll"
//       />
//     </>
//   )
// }
