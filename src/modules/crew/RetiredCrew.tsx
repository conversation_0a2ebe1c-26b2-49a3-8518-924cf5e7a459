import { useAppSelector } from '../../logic/redux/reduxHook'
import { Table } from '../../shared/table/Table'
import { useMemo, useState, useCallback, useEffect, useRef } from 'react'
import { useParams } from 'react-router-dom'

import { FlexBox, TooltipContainer } from '../../styles/styled'
import { IconContainer } from '../subContractor/style'
import { RevokeIcon } from '../../assets/icons/RevokeIcon'
import { StorageKey, WORK_TYPE1 } from '../../shared/helpers/constants'
import { getCompanyCrews } from '../../logic/apis/crew'
import { formatDate, getDataFromLocalStorage, isSuccess, notify } from '../../shared/helpers/util'
import { CustomModal } from '../../shared/customModal/CustomModal'
import { ConfirmationPopUp } from './components/confirmationPopup/ConfirmationPopUp'

const RetiredCrew = ({ refetchCrew }: { refetchCrew?: any }) => {
  const [pageCount, setPageCount] = useState<number>(10)
  const globalSelector = useAppSelector((state: any) => state)
  const { currentCompany, position } = globalSelector.company
  const fetchIdRef = useRef(0)
  const [showConfirmationPopUp, setShowConfirmationPopUp] = useState<boolean>(false)
  const [loading, setLoading] = useState(false)
  const [crewDetails, setCrewDetails] = useState<any>({})

  const [crewData, setCrewData] = useState([])

  const fetchData = useCallback(async ({ pageSize, pageIndex }: any) => {
    try {
      // This will get called when the table needs new data
      setLoading(true)
      let receivedData: any = []
      let receivedData1: any = []

      let currentCompanyData: any = localStorage.getItem('currentCompany')

      const statusResponse = await getCompanyCrews({ retired: true, deleted: false })

      if (isSuccess(statusResponse)) {
        let statusRes = statusResponse?.data?.data?.crew

        statusRes.forEach((res: any, index: number) => {
          receivedData.push({
            crewName: res.name,
            type: WORK_TYPE1[res.workType],
            manager: res.managerName,
            foreman: res.foremanName ? res.foremanName : '-',
            managerId: res.managerId,
            crewId: res._id,
            retireDate: res?.retireDate,
            link: true,
            action: (
              <TooltipContainer width={'100px'} className="crew">
                <span className="tooltip-content">Restore</span>
                <FlexBox width="100%" alignItems="center" gap="10px">
                  <IconContainer
                    content="Revoke"
                    className="restore"
                    onClick={() => {
                      setCrewDetails({
                        name: res?.name,
                        id: res?._id,
                      })

                      setShowConfirmationPopUp(true)
                    }}
                  >
                    <RevokeIcon />
                  </IconContainer>
                </FlexBox>
              </TooltipContainer>
            ),
          })
        })
      } else {
        notify(statusResponse?.data?.message, 'error')
      }

      // if (showRetiredCrew) {
      //   receivedData = [...receivedData, ...receivedData1]
      //   if (noData) {
      //     setNoData(false)
      //   }
      // } else {
      //   if (receivedData.length === 0) {
      //     setNoData(true)
      //   }
      // }

      // Give this fetch an ID
      const fetchId = ++fetchIdRef.current

      // Set the loading state

      // We'll even set a delay to simulate a server here
      // setTimeout(() => {
      // Only update the data if this is the latest fetch
      if (fetchId === fetchIdRef.current) {
        const startRow = pageSize * pageIndex
        const endRow = startRow + pageSize
        setCrewData(receivedData.slice(startRow, endRow))

        // Your server could send back total page count.
        // For now we'll just fake it, too
        setPageCount(Math.ceil(receivedData.length / pageSize))

        setLoading(false)
      }
      // }, 1000)
    } catch (error) {
      console.error('TeamTable fetchData error', error)
      setLoading(false)
    }
  }, [])

  const columns: any = useMemo(
    () => [
      {
        Header: 'Crew Name',
        accessor: 'crewName', // accessor is the "key" in the data
      },

      {
        Header: 'Manager',
        accessor: 'manager',
      },
      {
        Header: 'Foreman',
        accessor: 'foreman',
      },
      {
        Header: 'Retired',
        accessor: 'retireDate',
        Cell: (props: any) =>
          props?.row?.original?.retireDate ? formatDate(props?.row?.original?.retireDate) : '-------',
      },
      {
        Header: 'Action',
        accessor: 'action',
      },
    ],
    []
  )

  const getDetails = async () => {
    try {
      let receivedData: any = []
      // if (Object.keys(currentCompany).length > 0) {
      // const statusResponse = await getCompanyCrews(
      //   { retired: false, deleted: false, companyId: currentCompany._id },
      //   id
      // )
      // const statusResponse1 = await getCompanyCrews(
      //   { retired: true, deleted: false, companyId: currentCompany._id },
      //   id
      // )
      // const apiCalls = [
      const statusResponse = await getCompanyCrews({ retired: true, deleted: false })
      // getCompanyCrews({ retired: true, deleted: false, companyId: currentCompany._id }, id),
      // ]
      // const [statusResponse, statusResponse1] = await Promise.all(apiCalls)
      // if (isSuccess(statusResponse) && isSuccess(statusResponse1)) {
      if (isSuccess(statusResponse)) {
        let statusRes = statusResponse?.data?.data?.crew

        statusRes.forEach((res: any, index: number) => {
          receivedData.push({
            ...res,
            crewName: res.name,
            type: WORK_TYPE1[res.workType],
            manager: res.managerName,
            foreman: res.foremanName ? res.foremanName : '-',
            managerId: res.managerId,
            crewId: res._id,
            link: true,
            action: (
              <TooltipContainer width={'100px'} className="crew">
                <span className="tooltip-content">Restore</span>
                <FlexBox width="100%" alignItems="center" gap="10px">
                  <IconContainer
                    content="Revoke"
                    className="restore"
                    onClick={() => {
                      setCrewDetails({
                        name: res?.name,
                        id: res?._id,
                      })
                      setShowConfirmationPopUp(true)
                      // hadleRestoreSub(res?._id)
                    }}
                  >
                    <RevokeIcon />
                  </IconContainer>
                </FlexBox>
              </TooltipContainer>
            ),
          })
        })
        // let statusRes1 = statusResponse1?.data?.data?.crew

        // let statusRes = statusResponse?.data?.data?.crew

        setCrewData(receivedData)
        // if (statusRes1.length > 0) {
        //   setShowRetiredCrewButton(true)
        // }
        // if (statusRes.length > 0) {
        //   setNoData(false)
        // } else {
        //   setNoData(true)
        // }

        setLoading(false)
      } else {
        notify(statusResponse?.data?.message, 'error')
      }
      // }
    } catch (error) {
      console.error('getDetails error', error)
    }
  }

  useEffect(() => {
    getDetails()
  }, [])

  return (
    <>
      <Table
        columns={columns}
        data={crewData}
        loading={loading}
        pageCount={pageCount}
        fetchData={fetchData}
        noLink={true}
      />

      <CustomModal show={showConfirmationPopUp}>
        <ConfirmationPopUp
          setShowConfirmationPopUp={setShowConfirmationPopUp}
          setDetailsUpdate={() => {
            // getDetails()
            refetchCrew?.()
          }}
          header="Reactivate Crew"
          crewData={crewDetails}
        />
      </CustomModal>
    </>
  )
}

export default RetiredCrew
