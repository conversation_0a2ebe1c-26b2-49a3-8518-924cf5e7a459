import { Form, Formik } from 'formik'
import { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { useParams } from 'react-router-dom'
import * as Yup from 'yup'

import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import { createCrew } from '../../../../logic/apis/crew'
import { getTeamMembers } from '../../../../logic/apis/team'
import { SharedDate } from '../../../../shared/date/SharedDate'
import { Dropdown } from '../../../../shared/dropDown/Dropdown'
import { StorageKey, WORK_TYPE } from '../../../../shared/helpers/constants'
import { onlyMmDdYyyy, onlyText } from '../../../../shared/helpers/regex'
import {
  formatDateymd,
  getDataFromLocalStorage,
  getFormattedDate,
  isSuccess,
  notify,
} from '../../../../shared/helpers/util'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import * as SharedStyled from '../../../../styles/styled'
import { colors } from '../../../../styles/theme'
import { ModalHeaderInfo } from '../../../units/components/newUnitModal/style'
import * as Styled from './style'
import UnitSvg from '../../../../assets/newIcons/unitModal.svg'
import Button from '../../../../shared/components/button/Button'
import CustomSelect from '../../../../shared/customSelect/CustomSelect'

/**
 * InitialValues is an interface declared here so that it can be used as a type for the useState hook
 */
interface InitialValues {
  crewName: string
  crewStartDate: string
  workType: string
  manager: string
}

/**
 * I_CrewManagementForm is an interface which defines the props type which we receive from our parent component to this component
 */
interface I_CrewManagementForm {
  setShowAddCrewForm: React.Dispatch<React.SetStateAction<boolean>>
  setUpdate: React.Dispatch<React.SetStateAction<boolean>>
  setNoData: React.Dispatch<React.SetStateAction<boolean>>
  onSuccess?: () => void
}

export const CrewManagementForm = (props: I_CrewManagementForm) => {
  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */
  let currentDate: any = new Date()
  const [initialValues, setInitialValues] = useState<InitialValues>({
    crewName: '',
    crewStartDate: formatDateymd(currentDate) + '',
    workType: '',
    manager: '',
  })

  const [managerData, setManagerData] = useState<any>([])
  const [managerIdData, setManagerIdData] = useState<any>({})
  const [loading, setLoading] = useState<boolean>(false)

  const { setShowAddCrewForm, setUpdate, setNoData, onSuccess } = props

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany } = globalSelector.company

  /**
   * CrewManagementFormSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */
  const CrewManagementFormSchema = Yup.object().shape({
    crewName: Yup.string().min(2, 'Too Short!').max(50, 'Too Long!').required('Required'),
    crewStartDate: Yup.string()
      .min(2, 'Too Short!')
      .max(50, 'Too Long!')
      .required('Required')
      .matches(onlyMmDdYyyy, 'Enter the date in MM/DD/YYYY format'),
    workType: Yup.string().min(2, 'Too Short!').max(50, 'Too Long!').required('Required'),
    manager: Yup.string().min(2, 'Too Short!').max(50, 'Too Long!').required('Required'),
  })

  const getTeamDetails = async () => {
    // if (Object.keys(currentCompany).length > 0) {
    try {
      const memberResponse = await getTeamMembers({ deleted: false })
      if (isSuccess(memberResponse)) {
        let memberObj: any = []
        let memberIdObj: any = {}
        let memberData = memberResponse?.data?.data?.memberData
        memberData.forEach((member: any) => {
          memberObj.push(member.name)
          memberIdObj = { ...memberIdObj, [`${member.name}`]: member._id }
        })
        setManagerData(memberObj)
        setManagerIdData(memberIdObj)
      } else {
        notify(memberResponse?.data?.message, 'error')
      }
    } catch (error) {
      console.error('getTeamDetails error', error)
    }
    // }
  }

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async (submittedValues: InitialValues, { resetForm }: any) => {
    try {
      setLoading(true)

      // if (Object.keys(currentCompany).length > 0) {
      let dataObj = {
        name: submittedValues.crewName,
        startDate: getFormattedDate(submittedValues.crewStartDate),
        workType: Number(WORK_TYPE[submittedValues.workType]),
        managerId: managerIdData[submittedValues.manager],
      }
      let response = await createCrew(dataObj)

      if (isSuccess(response)) {
        notify('Crew Created Successfully', 'success')
        setShowAddCrewForm(false)
        setUpdate((prev) => !prev)
        setNoData(false)
        resetForm()
        setLoading(false)
        onSuccess?.()
      } else {
        notify(response?.data?.message, 'error')
        setLoading(false)
      }
      // }
    } catch (error) {
      console.error('CrewManagementForm handleSubmit', error)
      setLoading(false)
    }
  }

  useEffect(() => {
    getTeamDetails()
  }, [])

  return (
    <Styled.CrewManagementFormContainer>
      <Formik
        initialValues={initialValues}
        onSubmit={handleSubmit}
        validationSchema={CrewManagementFormSchema}
        enableReinitialize={true}
        validateOnChange={true}
        validateOnBlur={true}
      >
        {({ errors, touched, values, setFieldValue, resetForm }) => {
          return (
            <Form className="form">
              <Styled.ModalHeaderContainer>
                <SharedStyled.FlexRow>
                  <img src={UnitSvg} alt="modal icon" />
                  <SharedStyled.FlexCol>
                    <Styled.ModalHeader>{'Add Crew'}</Styled.ModalHeader>
                  </SharedStyled.FlexCol>
                </SharedStyled.FlexRow>
                <Styled.CrossContainer
                  onClick={() => {
                    resetForm()
                    setShowAddCrewForm(false)
                  }}
                >
                  <CrossIcon />
                </Styled.CrossContainer>
              </Styled.ModalHeaderContainer>
              <SharedStyled.SettingModalContentContainer>
                <SharedStyled.Content width="100%" disableBoxShadow={true} noPadding={true}>
                  <InputWithValidation
                    labelName="Crew Name"
                    stateName="crewName"
                    error={touched.crewName && errors.crewName ? true : false}
                    twoInput={true}
                  />
                  <SharedDate
                    value={values.crewStartDate}
                    labelName="Crew Start Date"
                    stateName="crewStartDate"
                    setFieldValue={setFieldValue}
                    error={touched.crewStartDate && errors.crewStartDate ? true : false}
                  />
                  <CustomSelect
                    margin="10px 0 0 0"
                    value={values.workType}
                    labelName="Work Type"
                    stateName="workType"
                    dropDownData={['roofing']}
                    setFieldValue={setFieldValue}
                    setValue={() => {}}
                    error={touched.workType && errors.workType ? true : false}
                  />
                  <CustomSelect
                    margin="10px 0 0 0"
                    value={values.manager}
                    labelName="Manager"
                    stateName="manager"
                    dropDownData={managerData}
                    setValue={() => {}}
                    setFieldValue={setFieldValue}
                    error={touched.manager && errors.manager ? true : false}
                  />
                  <SharedStyled.FlexBox
                    width="100%"
                    justifyContent="space-between"
                    alignItems="center"
                    gap="10px"
                    marginTop="26px"
                  >
                    <Button type="submit" maxWidth="200px" isLoading={loading}>
                      Add
                    </Button>
                    <Button type="button" maxWidth="200px" className="delete" onClick={() => setShowAddCrewForm(false)}>
                      Close
                    </Button>
                  </SharedStyled.FlexBox>
                </SharedStyled.Content>
              </SharedStyled.SettingModalContentContainer>
            </Form>
          )
        }}
      </Formik>
    </Styled.CrewManagementFormContainer>
  )
}
