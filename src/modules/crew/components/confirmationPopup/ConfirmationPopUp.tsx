import { useState } from 'react'
import { useSelector } from 'react-redux'
import { useParams } from 'react-router-dom'

import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import UnitSvg from '../../../../assets/newIcons/unitModal.svg'
import { reActivateCrew } from '../../../../logic/apis/crew'
import Button from '../../../../shared/components/button/Button'
import { getDataFromLocalStorage, isSuccess, notify } from '../../../../shared/helpers/util'
import * as SharedStyled from '../../../../styles/styled'
import { colors } from '../../../../styles/theme'
import * as Styled from './style'
import { StorageKey } from '../../../../shared/helpers/constants'

interface I_ConfirmationPopUp {
  setShowConfirmationPopUp: React.Dispatch<React.SetStateAction<boolean>>
  setDetailsUpdate: React.Dispatch<React.SetStateAction<boolean>>
  header: string
  crewData: any
}

export const ConfirmationPopUp = (props: I_ConfirmationPopUp) => {
  const { setShowConfirmationPopUp, setDetailsUpdate, header, crewData } = props

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany } = globalSelector.company

  /**
   * loading will be the loading state when performing the operations
   */
  const [loading, setLoading] = useState<boolean>(false)

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async () => {
    try {
      // if (Object.keys(currentCompany).length > 0) {
      setLoading(true)

      let dataObj = {
        crewId: crewData.id,
      }

      let response = await reActivateCrew(dataObj)

      if (isSuccess(response)) {
        notify('Reactivated the crew successfully', 'success')
        setDetailsUpdate((prev) => !prev)
        setLoading(false)
        setShowConfirmationPopUp(false)
      } else {
        setLoading(false)
        notify(response?.data?.message, 'error')
      }
      // }
    } catch (error) {
      console.error('Reactivated Crew handleSubmit', error)
      setLoading(false)
    }
  }

  return (
    <Styled.ConfirmationContainer>
      {' '}
      <Styled.ModalHeaderContainer>
        <SharedStyled.FlexRow>
          <img src={UnitSvg} alt="modal icon" />
          <SharedStyled.FlexCol>
            <Styled.ModalHeader>{header}</Styled.ModalHeader>
          </SharedStyled.FlexCol>
        </SharedStyled.FlexRow>
        <Styled.CrossContainer onClick={() => setShowConfirmationPopUp(false)}>
          <CrossIcon />
        </Styled.CrossContainer>
      </Styled.ModalHeaderContainer>
      <Styled.ModalBodyContainer>
        <Styled.ModalDescription>Are you sure you want to Reactivate {crewData.name} ?</Styled.ModalDescription>
        <SharedStyled.FlexBox width="100%" alignItems="center" justifyContent="space-around" marginTop="20px" gap="5px">
          <Button
            type="submit"
            bgColor={colors.blueLight}
            color={colors.white}
            maxWidth="200px"
            onClick={() => handleSubmit()}
            isLoading={loading}
          >
            Yes
          </Button>
          <Button type="submit" onClick={() => setShowConfirmationPopUp(false)} maxWidth="200px">
            No
          </Button>
        </SharedStyled.FlexBox>
      </Styled.ModalBodyContainer>
    </Styled.ConfirmationContainer>
  )
}
