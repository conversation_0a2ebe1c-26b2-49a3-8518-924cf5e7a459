import { Form, Formik } from 'formik'
import { useEffect, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate, useParams } from 'react-router-dom'
import * as Yup from 'yup'
import dayjs from 'dayjs'
import 'dayjs/locale/en'

import { CrossIcon } from '../../../../../../assets/icons/CrossIcon'
import { getCrewById, updateCrew, updateCrewOrderNumber } from '../../../../../../logic/apis/crew'
import { getTeamMembers } from '../../../../../../logic/apis/team'
import { SharedDate } from '../../../../../../shared/date/SharedDate'
import { StorageKey, WORK_TYPE, WORK_TYPE1 } from '../../../../../../shared/helpers/constants'
import { getDataFromLocalStorage, getFormattedDate, isSuccess, notify } from '../../../../../../shared/helpers/util'
import { InputWithValidation } from '../../../../../../shared/inputWithValidation/InputWithValidation'
import * as SharedStyled from '../../../../../../styles/styled'
import * as Styled from './style'
import CustomSelect from '../../../../../../shared/customSelect/CustomSelect'
import { onlyMmDdYyyy } from '../../../../../../shared/helpers/regex'
import { ModalHeaderInfo } from '../../../../../units/components/newUnitModal/style'
import UnitSvg from '../../../../../../assets/newIcons/unitModal.svg'
import Button from '../../../../../../shared/components/button/Button'
import { SLoader } from '../../../../../../shared/components/loader/Loader'

/**
 * InitialValues is an interface declared here so that it can be used as a type for the useState hook
 */
interface InitialValues {
  crewName: string
  crewStartDate: string
  workType: string
  manager: string
  order: string
}

/**
 * I_EditCrewForm is an interface which defines the props type which we receive from our parent component to this component
 */
interface I_EditCrewForm {
  setShowEditCrewFormPopup: React.Dispatch<React.SetStateAction<boolean>>
  setDetailsUpdate: React.Dispatch<React.SetStateAction<boolean>>
  startDate: string
  crewId: string
  triggerRefetch?: any
  setShowRetireForm?: React.Dispatch<React.SetStateAction<boolean>>
}

export const EditCrewForm = (props: I_EditCrewForm) => {
  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */
  const [initialValues, setInitialValues] = useState<InitialValues>({
    crewName: '',
    crewStartDate: new Date().getTime() + '',
    workType: '',
    manager: '',
    order: '',
  })

  const [crewDetails, setCrewDetails] = useState({})

  /**
   * loading will be the loading state when performing the operations
   */
  const [loading, setLoading] = useState<boolean>(false)
  const [deleteLoading, setDeleteLoading] = useState<boolean>(false)
  const [managerData, setManagerData] = useState<any>([])
  const [managerIdData, setManagerIdData] = useState<any>({})

  const navigate = useNavigate()

  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [inputRef])
  /**
   * Destructuring the values from the props received
   */
  const { setShowEditCrewFormPopup, setDetailsUpdate, setShowRetireForm, startDate, crewId, triggerRefetch } = props

  /**
   * EditCrewFormSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */
  const EditCrewFormSchema = Yup.object().shape({
    crewName: Yup.string().min(4).required('name must be longer than or equal to 4 characters'),
    crewStartDate: Yup.string()
      .required('No date provided.')
      .matches(onlyMmDdYyyy, 'Enter the date in MM/DD/YYYY format'),
    workType: Yup.string().required('required'),
    manager: Yup.string().required('required'),
    order: Yup.string().required('required'),
  })

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async (submittedValues: InitialValues, { resetForm }: any) => {
    try {
      setLoading(true)
      // if (Object.keys(currentCompany).length > 0) {
      let crId: any = crewId
      let dataObj = {
        _id: crId,
        name: submittedValues.crewName,
        startDate: getFormattedDate(submittedValues.crewStartDate),
        workType: Number(WORK_TYPE[submittedValues.workType]),
        managerId: managerIdData[submittedValues.manager],
      }
      let dataObj1 = {
        _id: crId,
        name: submittedValues.crewName,
        order: Number(submittedValues.order),
      }
      let response = await updateCrew(dataObj)
      let response1 = await updateCrewOrderNumber(dataObj1)
      if (isSuccess(response) && isSuccess(response1)) {
        notify('Crew Updated Successfully', 'success')
        triggerRefetch?.()
        setDetailsUpdate((prev) => !prev)
        setShowEditCrewFormPopup(false)
        setLoading(false)
      } else {
        notify(response?.data?.message, 'error')
        setLoading(false)
      }
      // }
    } catch (error) {
      console.error('EditCrewForm handleSubmit', error)
      setLoading(false)
    }
  }

  const onDelete = async (submittedValues: InitialValues) => {
    try {
      setDeleteLoading(true)
      // if (Object.keys(currentCompany).length > 0) {
      let crId: any = crewId
      let dataObj = {
        _id: crId,
        name: submittedValues.crewName,
        startDate: getFormattedDate(submittedValues.crewStartDate),
        workType: Number(WORK_TYPE[submittedValues.workType]),
        managerId: managerIdData[submittedValues.manager],
        deleted: true,
      }
      let response = await updateCrew(dataObj)
      if (isSuccess(response)) {
        notify('Crew Deleted Successfully', 'success')
        triggerRefetch?.()
        setDeleteLoading(false)
        navigate(`/team`)
      } else {
        notify(response?.data?.message, 'error')
        setDeleteLoading(false)
      }
      // }
    } catch (error) {
      console.error('EditCrewForm onDelete', error)
    }
  }

  const getDetails = async () => {
    try {
      let crId: any = crewId

      // if (Object.keys(currentCompany).length > 0) {
      const statusResponse = await getCrewById({ _id: crId })
      if (isSuccess(statusResponse)) {
        let statusRes = statusResponse?.data?.data?.crew[0]
        let dataObj: any = {
          crewName: statusRes.name,
          crewStartDate: statusRes.startDate ? dayjs(statusRes?.startDate).format('YYYY-MM-DD') : '',
          workType: WORK_TYPE1[statusRes.workType],
          manager: statusRes.managerName ? statusRes.managerName : '',
          order: statusRes.order,
        }
        setInitialValues({ ...initialValues, ...dataObj })
      } else {
        notify(statusResponse?.data?.message, 'error')
      }
      // }
    } catch (error) {
      console.error('getDetails error', error)
    }
  }

  const getTeamDetails = async () => {
    // if (Object.keys(currentCompany).length > 0) {
    try {
      const memberResponse = await getTeamMembers({ deleted: false, limit: 5000 })
      if (isSuccess(memberResponse)) {
        let memberObj: any = []
        let memberIdObj: any = {}
        let memberData = memberResponse?.data?.data?.memberData
        memberData.forEach((member: any) => {
          memberObj.push(member.name)
          memberIdObj = { ...memberIdObj, [`${member.name}`]: member._id }
        })
        setManagerData(memberObj)
        setManagerIdData(memberIdObj)
      } else {
        notify(memberResponse?.data?.message, 'error')
      }
    } catch (error) {
      console.error('getTeamDetails error', error)
    }
    // }
  }

  useEffect(() => {
    getDetails()
    getTeamDetails()
  }, [])

  return (
    <Styled.EditCrewFormContainer>
      <Formik
        initialValues={initialValues}
        enableReinitialize={true}
        onSubmit={handleSubmit}
        validationSchema={EditCrewFormSchema}
        validateOnChange={true}
        validateOnBlur={false}
      >
        {/* <Styled.ResetpasswordContainer> */}
        {({ values, errors, touched, setFieldValue, resetForm }) => {
          return (
            <>
              <Styled.ModalHeaderContainer>
                <SharedStyled.FlexRow>
                  <img src={UnitSvg} alt="modal icon" />
                  <SharedStyled.FlexCol>
                    <Styled.ModalHeader>Edit Crew Details</Styled.ModalHeader>
                  </SharedStyled.FlexCol>
                </SharedStyled.FlexRow>
                <Styled.CrossContainer onClick={() => setShowEditCrewFormPopup(false)}>
                  <CrossIcon />
                </Styled.CrossContainer>
              </Styled.ModalHeaderContainer>
              {!values?.crewName ? (
                <SharedStyled.SettingModalContentContainer>
                  <SharedStyled.FlexCol gap="10px">
                    <SLoader height={40} />
                    <SLoader height={40} />
                    <SLoader height={40} />
                    <SLoader height={40} />
                  </SharedStyled.FlexCol>
                </SharedStyled.SettingModalContentContainer>
              ) : (
                <SharedStyled.SettingModalContentContainer>
                  <Form className="form">
                    <SharedStyled.Content maxWidth="706px" width="100%" disableBoxShadow={true} noPadding={true}>
                      <InputWithValidation
                        labelName="Crew Name"
                        stateName="crewName"
                        value={values.crewName}
                        error={touched.crewName && errors.crewName ? true : false}
                        passRef={inputRef}
                      />
                      <SharedDate
                        value={values.crewStartDate}
                        labelName="Crew Start Date"
                        stateName="crewStartDate"
                        error={touched.crewStartDate && errors.crewStartDate ? true : false}
                        setFieldValue={setFieldValue}
                        // min={startDate}
                      />
                      <CustomSelect
                        value={values.workType}
                        labelName="Work Type"
                        stateName="workType"
                        dropDownData={['Roofing']}
                        setFieldValue={setFieldValue}
                        setValue={() => {}}
                        margin="10px 0 0 0"
                        error={touched.workType && errors.workType ? true : false}
                      />
                      <CustomSelect
                        value={values.manager}
                        labelName="Manager"
                        stateName="manager"
                        dropDownData={managerData}
                        setFieldValue={setFieldValue}
                        setValue={() => {}}
                        margin="10px 0 0 0"
                        error={touched.manager && errors.manager ? true : false}
                      />
                      <InputWithValidation
                        labelName="Order"
                        stateName="order"
                        value={values.order}
                        error={touched.order && errors.order ? true : false}
                      />
                      <SharedStyled.ButtonContainer marginTop="20px">
                        <Button type="submit" isLoading={loading}>
                          Edit
                        </Button>
                        <Button
                          className="delete"
                          type="button"
                          onClick={() => {
                            setShowEditCrewFormPopup(false)
                            setShowRetireForm?.(true)
                          }}
                        >
                          Retire
                        </Button>
                      </SharedStyled.ButtonContainer>
                      {/* <Styled.DeleteOptionContainer marginTop="16px" onClick={() => onDelete(values)}>
                      {deleteLoading ? (
                        <>
                          Deleting..
                          <SharedStyled.Loader />
                        </>
                      ) : (
                        'Delete Crew'
                      )}
                    </Styled.DeleteOptionContainer> */}
                    </SharedStyled.Content>
                  </Form>
                </SharedStyled.SettingModalContentContainer>
              )}
            </>
          )
        }}
        {/* </Styled.ResetpasswordContainer> */}
      </Formik>
    </Styled.EditCrewFormContainer>
  )
}
