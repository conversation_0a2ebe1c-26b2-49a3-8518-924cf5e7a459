import { Form, Formik } from 'formik'
import { useState } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate, useParams } from 'react-router-dom'
import * as Yup from 'yup'

import UnitSvg from '../../../../../../assets/newIcons/unitModal.svg'
import { CrossIcon } from '../../../../../../assets/icons/CrossIcon'
import { promoteCrewMember, removeCrewMember, updateCrew, updateCrewMember } from '../../../../../../logic/apis/crew'
import { SharedDate } from '../../../../../../shared/date/SharedDate'
import { onlyMmDdYyyy } from '../../../../../../shared/helpers/regex'
import {
  dayjsFormat,
  formatDateymd,
  getDataFromLocalStorage,
  isSuccess,
  notify,
  startOfDate,
} from '../../../../../../shared/helpers/util'
import * as SharedStyled from '../../../../../../styles/styled'
import * as Styled from './style'
import { ModalHeaderInfo } from '../../../../../units/components/newUnitModal/style'
import Button from '../../../../../../shared/components/button/Button'
import { StorageKey } from '../../../../../../shared/helpers/constants'

/**
 * InitialValues is an interface declared here so that it can be used as a type for the useState hook
 */
interface InitialValues {
  date: string
}

/**
 * I_CrewPopup is an interface which defines the props type which we receive from our parent component to this component
 */
interface I_CrewPopup {
  setShowCrewPopup: React.Dispatch<React.SetStateAction<boolean>>
  header: string
  inputLabel: string
  submitButtonColor: string
  buttonText: string
  loaderText: string
  currentCrewMemberId: string
  currentMemberId: string
  setUpdate?: React.Dispatch<React.SetStateAction<boolean>>
  startDate: string
  crewId: string
  triggerRefetch?: any
  currentCrewDate?: string
}

export const CrewPopup = (props: I_CrewPopup) => {
  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */
  const {
    setShowCrewPopup,
    header,
    inputLabel,
    submitButtonColor,
    buttonText,
    loaderText,
    currentMemberId,
    currentCrewMemberId,
    setUpdate,
    startDate,
    crewId,
    triggerRefetch,
    currentCrewDate,
  } = props
  let currentDate: any = new Date()
  const [initialValues, setInitialValues] = useState<InitialValues>({
    date:
      header === 'Retire Crew'
        ? ''
        : header === 'Remove Crew Member'
        ? ''
        : dayjsFormat(currentCrewDate, 'MMM D, YYYY') + '',
  })

  /**
   * loading will be the loading state when performing the operations
   */
  const [loading, setLoading] = useState<boolean>(false)

  /**
   * Destructuring the values from the props received
   */

  /**
   * CrewPopupSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */
  const CrewPopupSchema = Yup.object().shape({
    date: Yup.string().required('No date provided.'),
  })

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company

  const navigate = useNavigate()

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async (submittedValues: InitialValues, { resetForm }: any) => {
    setLoading(true)
    try {
      if (header === 'Retire Crew') {
        await retireCrewFunc(submittedValues)
      }
      if (header === 'Remove Crew Member') {
        await removeCrewMemberFunc(submittedValues)
      }
      if (header === 'Promote Crew Member') {
        await promoteCrewMemberFunc(submittedValues)
        resetForm()
      }
      if (header === 'Edit Crew Member') {
        await editCrewMember(submittedValues)
        resetForm()
      }
    } catch (error) {
      console.error('CrewPopup handleSubmit', error)
      setLoading(false)
    }
  }
  const retireCrewFunc = async (submittedValues: InitialValues) => {
    try {
      setLoading(true)
      // if (Object.keys(currentCompany).length > 0) {
      let crId: any = crewId
      let dataObj = {
        _id: crId,
        deleted: false,
        retired: true,
        retireDate: startOfDate(submittedValues.date),
      }
      let response = await updateCrew(dataObj)
      if (isSuccess(response)) {
        notify('Retired Crew Successfully', 'success')
        // navigate(`/crew/${id}`)
        setShowCrewPopup(false)
        setLoading(false)
        triggerRefetch?.()
      } else {
        notify(response?.data?.message, 'error')
        setLoading(false)
      }
      // }
    } catch (error) {
      console.error('retireCrewFunc handleSubmit', error)
      setLoading(false)
    }
  }

  const editCrewMember = async (submittedValues: InitialValues) => {
    try {
      setLoading(true)
      // if (Object.keys(currentCompany).length > 0) {
      let crId: any = crewId
      let dataObj = {
        crewId: crId,
        crewMemberId: currentCrewMemberId,
        memberId: currentMemberId,
        startDate: startOfDate(submittedValues.date),
      }
      let response = await updateCrewMember(dataObj)
      if (isSuccess(response)) {
        notify('Edited Crew Member Successfully', 'success')
        setShowCrewPopup(false)
        setUpdate ? setUpdate((prev) => !prev) : ''
        setLoading(false)
      } else {
        notify(response?.data?.message, 'error')
        setLoading(false)
      }
      // }
    } catch (error) {
      console.error('retireCrewFunc handleSubmit', error)
      setLoading(false)
    }
  }
  const promoteCrewMemberFunc = async (submittedValues: InitialValues) => {
    try {
      setLoading(true)
      // if (Object.keys(currentCompany).length > 0) {
      let crId: any = crewId
      let dataObj = {
        crewId,
        crewMemberId: currentCrewMemberId,
        memberId: currentMemberId,
        promoteDate: startOfDate(submittedValues.date),
      }
      let response = await promoteCrewMember(dataObj)
      if (isSuccess(response)) {
        notify('Promoted Crew Member Successfully', 'success')
        setShowCrewPopup(false)
        setUpdate ? setUpdate((prev) => !prev) : ''
        setLoading(false)
      } else {
        notify(response?.data?.message, 'error')
        setLoading(false)
      }
      // }
    } catch (error) {
      console.error('promoteCrewMemberFunc handleSubmit', error)
      setLoading(false)
    }
  }
  const removeCrewMemberFunc = async (submittedValues: InitialValues) => {
    try {
      setLoading(true)
      // if (Object.keys(currentCompany).length > 0) {
      let crId: any = crewId
      let dataObj = {
        crewId: crId,
        crewMemberId: currentCrewMemberId,
        memberId: currentMemberId,
        removeDate: startOfDate(submittedValues.date),
      }
      let response = await removeCrewMember(dataObj)
      if (isSuccess(response)) {
        notify('Removed Crew Member Successfully', 'success')
        setShowCrewPopup(false)
        setUpdate ? setUpdate((prev) => !prev) : ''
        setLoading(false)
      } else {
        notify(response?.data?.message, 'error')
        setLoading(false)
      }
      // }
    } catch (error) {
      console.error('removeCrewMemberFunc handleSubmit', error)
      setLoading(false)
    }
  }

  return (
    <Styled.CrewPopupContainer>
      <Formik
        initialValues={initialValues}
        enableReinitialize={true}
        onSubmit={handleSubmit}
        validationSchema={CrewPopupSchema}
        validateOnChange={true}
        validateOnBlur={false}
      >
        {/* <Styled.ResetpasswordContainer> */}
        {({ values, errors, touched, resetForm, setFieldValue }) => {
          return (
            <>
              <Styled.ModalHeaderContainer>
                <SharedStyled.FlexRow>
                  <img src={UnitSvg} alt="modal icon" />
                  <SharedStyled.FlexCol>
                    <Styled.ModalHeader>{header}</Styled.ModalHeader>
                  </SharedStyled.FlexCol>
                </SharedStyled.FlexRow>
                <Styled.CrossContainer onClick={() => setShowCrewPopup(false)}>
                  <CrossIcon />
                </Styled.CrossContainer>
              </Styled.ModalHeaderContainer>
              <SharedStyled.SettingModalContentContainer>
                <Form className="form">
                  <SharedStyled.Content maxWidth="706px" width="100%" disableBoxShadow={true} noPadding={true}>
                    <SharedStyled.FlexCol>
                      {header === 'Retire Crew' ? (
                        <SharedStyled.Text fontSize="14px" fontWeight="medium">
                          Last day the crew was active
                        </SharedStyled.Text>
                      ) : null}
                      <SharedDate
                        value={values.date}
                        labelName={inputLabel}
                        stateName="date"
                        error={touched.date && errors.date ? true : false}
                        min={startDate}
                        setFieldValue={setFieldValue}
                      />
                    </SharedStyled.FlexCol>

                    <SharedStyled.ButtonContainer marginTop="20px">
                      <Button type="submit" isLoading={loading} bgColor={submitButtonColor}>
                        {buttonText}
                      </Button>
                    </SharedStyled.ButtonContainer>
                  </SharedStyled.Content>
                </Form>
              </SharedStyled.SettingModalContentContainer>
            </>
          )
        }}
        {/* </Styled.ResetpasswordContainer> */}
      </Formik>
    </Styled.CrewPopupContainer>
  )
}
