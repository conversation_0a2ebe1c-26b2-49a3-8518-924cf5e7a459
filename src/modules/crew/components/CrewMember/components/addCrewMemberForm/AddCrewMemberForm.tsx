import { Form, Formik } from 'formik'
import { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { useParams } from 'react-router-dom'
import * as Yup from 'yup'

import { CrossIcon } from '../../../../../../assets/icons/CrossIcon'
import { createCrewMember, getCrewById, getCrewMembers, getNonCrewMembers } from '../../../../../../logic/apis/crew'
import { getTeamMembers } from '../../../../../../logic/apis/team'
import CustomSelect from '../../../../../../shared/customSelect/CustomSelect'
import { SharedDate } from '../../../../../../shared/date/SharedDate'
import { Dropdown } from '../../../../../../shared/dropDown/Dropdown'
import { onlyMmDdYyyy } from '../../../../../../shared/helpers/regex'
import {
  checkMinCondition,
  dayjsFormat,
  formatDateymd,
  getDataFromLocalStorage,
  getFormattedDate,
  isSuccess,
  notify,
  startOfDate,
} from '../../../../../../shared/helpers/util'
import * as SharedStyled from '../../../../../../styles/styled'
import { colors } from '../../../../../../styles/theme'
import * as Styled from './style'
import UnitSvg from '../../../../../../assets/newIcons/unitModal.svg'
import { ModalHeaderInfo } from '../../../../../units/components/newUnitModal/style'
import Button from '../../../../../../shared/components/button/Button'
import { StorageKey } from '../../../../../../shared/helpers/constants'

/**
 * InitialValues is an interface declared here so that it can be used as a type for the useState hook
 */
interface InitialValues {
  personToAdd: string
  dateJoinedCrew: string
}

/**
 * I_AddCrewMemberForm is an interface which defines the props type which we receive from our parent component to this component
 */
interface I_CrewManagementForm {
  setShowAddCrewMemberForm: React.Dispatch<React.SetStateAction<boolean>>
  setUpdate: React.Dispatch<React.SetStateAction<boolean>>
  setNoData: React.Dispatch<React.SetStateAction<boolean>>
  startDate: any
  crewId: string
  apiStartDate: any
}

export const AddCrewMemberForm = (props: I_CrewManagementForm) => {
  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */
  const { setShowAddCrewMemberForm, setUpdate, startDate, setNoData, crewId, apiStartDate } = props

  let currentDate: any = new Date()

  const [initialValues, setInitialValues] = useState<InitialValues>({
    personToAdd: '',
    dateJoinedCrew: formatDateymd(currentDate) + '',
  })
  const [loading, setLoading] = useState<boolean>(false)
  const [memberData, setMemberData] = useState<any>([])
  const [memberIdData, setMemberIdData] = useState<any>({})

  /**
   * CrewManagementFormSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */
  const CrewManagementFormSchema = Yup.object().shape({
    personToAdd: Yup.string().min(2, 'Too Short!').max(50, 'Too Long!').required('Required'),
    dateJoinedCrew: Yup.string()
      .test({
        name: 'is-dateJoinedCrew',
        test(value: any, ctx) {
          console.log('Date Joined Crew', {
            a: apiStartDate,
            b: startOfDate(value),
            'a>b': apiStartDate > startOfDate(value),
          })
          if (apiStartDate > startOfDate(value)) {
            return ctx.createError({
              message: `Joining date should be more than or equal to crew start date (${dayjsFormat(
                apiStartDate,
                'MM/DD/YYYY'
              )})`,
            })
          }
          return true
        },
      })
      .required('Required')
      .matches(onlyMmDdYyyy, 'Enter the date in MM/DD/YYYY format'),
  })

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async (submittedValues: InitialValues, { resetForm }: any) => {
    try {
      setLoading(true)

      // if (Object.keys(currentCompany).length > 0) {
      let crId: any = crewId
      let dataObj = {
        crewId: crId,
        memberId: memberIdData[submittedValues.personToAdd],
        startDate: startOfDate(submittedValues.dateJoinedCrew),
      }
      let response = await createCrewMember(dataObj)
      if (isSuccess(response)) {
        notify('Crew Member Created Successfully', 'success')
        setLoading(false)
        setShowAddCrewMemberForm(false)
        setUpdate((prev) => !prev)
        setNoData(false)
        resetForm()
      } else {
        notify(response?.data?.message, 'error')
        setLoading(false)
      }
      // }
    } catch (error) {
      console.error('CrewManagementForm handleSubmit', error)
    }
  }

  const getTeamDetails = async () => {
    // if (Object.keys(currentMember).length > 0) {
    try {
      let crId: any = crewId

      // const memberResponse = await getTeamMembers({ deleted: false, companyId: currentCompany._id }, id)
      // const statusResponse = await getCrewMembers({ crewId: crId, companyId: currentCompany._id, deleted: false }, id)

      const apiCalls = [
        getTeamMembers({ deleted: false }),
        getNonCrewMembers({ deleted: false, crewId: crId }),
        getCrewById({ _id: crId }),
      ]
      const [memberResponse, nonMemberResponse, statusResponse] = await Promise.all(apiCalls)
      if (isSuccess(memberResponse) && isSuccess(statusResponse)) {
        let memberObj: any = []
        let memberIdObj: any = {}
        let memberData = memberResponse?.data?.data?.memberData
        let nonMemberData = nonMemberResponse?.data?.data?.filteredMembers
        let crewData = statusResponse?.data?.data?.crew[0]
        nonMemberData.forEach((member: any) => {
          // if (crewData.managerId !== member._id) {
          memberObj.push(`${member.firstName} ${member.lastName ?? ''}`?.trim())
          memberIdObj = { ...memberIdObj, [`${member.firstName} ${member.lastName ?? ''}`?.trim()]: member._id }
          // }
        })
        setMemberData(memberObj)
        setMemberIdData(memberIdObj)
      } else {
        notify(memberResponse?.data?.message, 'error')
      }
    } catch (error) {
      console.error('getTeamDetails error', error)
    }
    // }
  }

  // const getDetails = async () => {
  //   try {
  //     let crId: any = crewId

  //     if (Object.keys(currentCompany).length > 0 && Object.keys(currentMember).length > 0) {
  //       const statusResponse = await getCrewById({ _id: crId, companyId: currentCompany._id }, id)
  //       console.log('statusResponse', statusResponse)
  //       if (isSuccess(statusResponse)) {
  //         let statusRes = statusResponse?.data?.data?.crew[0]
  //         //  setCrewName(statusRes1.name)
  //         //  setStartDate((new Date(statusRes1.startDate.slice(0, 10))).format('YYYY-MM-DD'))
  //         if (statusRes.managerId !== currentMember._id) {
  //         }
  //         if (statusRes.length > 0) {
  //           setNoData(false)
  //         } else {
  //           setNoData(true)
  //         }
  //       } else {
  //         notify(statusResponse?.data?.message, 'error')
  //       }
  //     }
  //   } catch (error) {
  //     console.error('getDetails error', error)
  //   }
  // }

  useEffect(() => {
    getTeamDetails()
  }, [])

  // useEffect(() => {
  //   getDetails()
  // }, [currentCompany])

  return (
    <Styled.AddCrewMemberFormContainer>
      <Formik
        initialValues={initialValues}
        onSubmit={handleSubmit}
        validationSchema={CrewManagementFormSchema}
        enableReinitialize={true}
        validateOnChange={true}
        validateOnBlur={false}
      >
        {({ errors, touched, values, resetForm, setFieldValue }) => {
          return (
            <Form className="form">
              <Styled.ModalHeaderContainer>
                <SharedStyled.FlexRow>
                  <img src={UnitSvg} alt="modal icon" />
                  <SharedStyled.FlexCol>
                    <Styled.ModalHeader>{'Add Crew Member'}</Styled.ModalHeader>
                  </SharedStyled.FlexCol>
                </SharedStyled.FlexRow>
                <Styled.CrossContainer
                  onClick={() => {
                    resetForm()
                    setShowAddCrewMemberForm(false)
                  }}
                >
                  <CrossIcon />
                </Styled.CrossContainer>
              </Styled.ModalHeaderContainer>
              <SharedStyled.SettingModalContentContainer>
                <SharedStyled.Content maxWidth="1280px" width="100%" disableBoxShadow={true} noPadding={true}>
                  <CustomSelect
                    value={values.personToAdd}
                    labelName="Person To Add"
                    stateName="personToAdd"
                    dropDownData={memberData}
                    setFieldValue={setFieldValue}
                    setValue={() => {}}
                    margin="10px 0 0 0"
                    error={touched.personToAdd && errors.personToAdd ? true : false}
                  />
                  <SharedDate
                    value={values.dateJoinedCrew}
                    labelName="Date Joined Crew"
                    stateName="dateJoinedCrew"
                    error={touched.dateJoinedCrew && errors.dateJoinedCrew ? true : false}
                    // min={startDate}
                    setFieldValue={setFieldValue}
                  />

                  <SharedStyled.FlexBox
                    width="100%"
                    alignItems="center"
                    gap="10px"
                    justifyContent="space-between"
                    marginTop="26px"
                  >
                    <Button type="submit" maxWidth="200px" isLoading={loading}>
                      Add
                    </Button>
                    <Button
                      type="button"
                      className="delete"
                      maxWidth="200px"
                      onClick={() => setShowAddCrewMemberForm(false)}
                    >
                      Close
                    </Button>
                  </SharedStyled.FlexBox>
                </SharedStyled.Content>
              </SharedStyled.SettingModalContentContainer>
            </Form>
          )
        }}
      </Formik>
    </Styled.AddCrewMemberFormContainer>
  )
}
