import { Field, Form, Formik, FieldArray, ErrorMessage } from 'formik'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate, useParams } from 'react-router-dom'
import * as Yup from 'yup'

import { CustomModal } from '../../shared/customModal/CustomModal'
import { Dropdown } from '../../shared/dropDown/Dropdown'
import { StorageKey, TIME_ZONES } from '../../shared/helpers/constants'
import { onlyText } from '../../shared/helpers/regex'
import {
  convertKeyToStr,
  getDataFromLocalStorage,
  getNameFromId,
  getUnitIdFromSymbol,
  getUnitSymbolFromId,
  isSuccess,
  notify,
  truncateParagraph,
  getIdFromName,
  getNameFrom_Id,
  getObjectKeyByValue,
  getValueByKeyAndMatch,
} from '../../shared/helpers/util'
import { InputWithValidation } from '../../shared/inputWithValidation/InputWithValidation'
import * as SharedStyled from '../../styles/styled'
import * as Styled from './style'
import RadioButtonGroup from '../../shared/radioButtonGroup/RadioButtonGroup'
import { IType } from '../newProject/NewProject'
import CustomSelect from '../../shared/customSelect/CustomSelect'
import InputModal from '../units/components/inputModal/InputModal'
import NewUnitModal from '../units/components/newUnitModal/NewUnitModal'
import {
  createTask,
  getCatergoryApi,
  getInputsApi,
  getMaterialsApi,
  getProjectTypes,
  getSubCatergoryApi,
  getTaskApi,
  getUnitsApi,
} from '../../logic/apis/projects'
import { PROJECT_TYPE } from '../units/Units'
import MaterialsModal, { vendorDrop } from '../materials/components/materialsModal/MaterialsModal'
import CategoryModal from '../category/components/categoryModal/CategoryModal'
import SubcategoryModal from '../category/components/subcategoryModal/SubcategoryModal'
// import CrewPositionModal from '../crewPositions/components/crewPositionsModal/CrewPositionModal'
import { Table } from '../../shared/table/Table'
import CustomizeTaskModal, { modifiedByValues } from './customizeTaskModal/CustomizeTaskModal'
import { ButtonCont, SettingsCont } from '../units/style'
import Button from '../../shared/components/button/Button'
import TabBar from '../../shared/components/tabBar/TabBar'
import DeletedTasks from './deletedTasks/DeletedTasks'
import AddTaskModal from './customizeTaskModal/AddTaskModal'
import { InputLabelWithValidation } from '../../shared/inputLabelWithValidation/InputLabelWithValidation'
import Toggle from '../../shared/toggle/Toggle'
import { ProjectTypeGroups } from '../projectTypes/components/projectTypeModal/constant'
import AutoComplete from '../../shared/autoComplete/AutoComplete'
import { sortMaterials } from '../materials/Materials'
import { getTasks } from '../../logic/apis/task'
import { CreateTaskPopUp } from '../taskSettings/components/createTaskPopUp/CreateTaskPopUp'
import ReverseSvg from '../../assets/newIcons/reverse.svg'
import AutoCompleteIndentation from '../../shared/autoCompleteIndentation/AutoCompleteIndentation'

interface I_InputObject {
  iNum: string
  input: string
  oper: string
  name: string
}
interface I_MaterialObject {
  tMat: string
  mat: string
  cov: string
  name: string
  switch?: boolean
  customValue: string
}
interface I_LaborObject {
  tLabor: string
  worker: string
  time: string
  mod: string
  name: string
}
interface InitialValues {
  type: string
  active?: boolean
  name: string
  taskDescription: string
  unit: string
  unitName: string
  waste: number
  group: string
  order: number
  // worker: string
  // time: string
  // modifiedBy: string
  input: I_InputObject[]
  material: I_MaterialObject[]
  labor: I_LaborObject[]
  createdBy: string | undefined
}
export enum TaskGroups {
  None = 'none',
  SteepRoofing = 'Steep Roofing',
  DripMetal = 'Drip Metal',
  EndWallFlashing = 'End Wall Flashing',
  FlatRoofing = 'Flat Roofing',
  Starter = 'Starter',
  RidgeCap = 'Ridge Cap',
  CanVents = 'Can Vents',
  RidgeVent = 'Ridge Vent',
  SideWallFlashing = 'Side Wall Flashing',
  SyntheticFelt = 'Synthetic Felt',
  ValleyMetal = 'Valley Metal',
  IceShield = 'Ice Shield',
}
export interface IFullUnit {
  _id: string
  name: string
  symbol: string
  createdBy: string
}

const extractUniqueGroups = (items: any[]) => {
  const uniqueGroups: { [key: string]: string } = {}

  items.forEach((item: any) => {
    item.groups.forEach((group: any) => {
      if (!uniqueGroups[group.name]) {
        uniqueGroups[group.name] = group._id
      }
    })
  })

  return Object.keys(uniqueGroups).map((groupName) => ({
    groupName,
    groupId: uniqueGroups[groupName],
  }))
}

const Tasks = () => {
  const [showAddModal, setShowAddModal] = useState(false)
  const [initialValues, setInitialValues] = useState<InitialValues>({
    active: true,
    type: '',
    name: '',
    taskDescription: '',
    unit: '',
    unitName: '',
    waste: 0,
    group: '',
    order: null,
    // worker: '',
    // time: '',
    // modifiedBy: '',
    labor: [
      {
        tLabor: '',
        worker: '',
        time: '',
        mod: '',
        name: '',
      },
    ],
    input: [
      {
        iNum: '',
        input: '',
        oper: '+',
        name: '',
      },
    ],
    material: [
      {
        tMat: '',
        mat: '',
        cov: '',
        name: '',
        switch: false,
        customValue: '',
      },
    ],
    createdBy: '',
    materialWaste: 0,
  })
  const [showChangePasswordModal, setShowChangePasswordModal] = useState<boolean>(false)
  const [loading, setLoading] = useState<boolean>(false)
  const [types, setTypes] = useState<IType[]>([])

  const [filterType, setFilterType] = useState({ name: '', id: undefined })
  const [filterGroup, setFilterGroup] = useState('')
  const [input, setInput] = useState<any>([{ calculate: '', calculateBy: '' }])
  const [material, setMaterial] = useState<any>([{ name: '', unit: '' }])
  const [timeZ, setTimeZ] = useState<string>(TIME_ZONES[0])
  const [measurement, setMeasurement] = useState('')
  const [projectType, setProjectType] = useState('')
  const [calculated, setCalculated] = useState('')
  const [searchData, setSearchData] = useState<any>('')
  const [calculatedBy, setCalculatedBy] = useState('')
  const [materialName, setMaterialName] = useState('')
  const [autoMaterialName, setAutoMaterialName] = useState('')
  const [autoInputName, setAutoInputName] = useState('')
  const [tempMaterialName, setTempMaterialName] = useState('')
  const [tempInputName, setTempInputName] = useState('')
  const [modifiedBy, setModifiedBy] = useState('')
  const [groupType, setGroupType] = useState<any[]>([])

  const [worker, setWorker] = useState('')
  const [materialModal, setMaterialModal] = useState(false)
  const [inputData, setInputData] = useState<any>([])

  const [catData, setCatData] = useState<any>([])
  const [subCatData, setSubCatData] = useState<any>([])
  const [unitData, setUnitData] = useState<any>([])
  const [modifiedByDropdown, setModifiedByDropdown] = useState<any>([])

  const [categoryModal, setCategoryModal] = useState(false)
  const [subCategoryModal, setSubCategoryModal] = useState(false)
  const [materials, setMaterials] = useState<any>([])
  const [materialsDropdownData, setMaterialsDropdownData] = useState<any>([])
  const [inputDropdownData, setInputDropdownData] = useState<any>([])
  const [unitDropdownData, setUnitDropdownData] = useState<any>([])
  const [unitDrops, setUnitDrops] = useState({})

  const [newInputModal, setNewInputModal] = useState(false)
  const [customizeTaskModal, setCustomizeTaskModal] = useState(false)

  const [newMaterial, setNewMaterial] = useState({})

  const [editInputVals, setEditInputVals] = useState<any>(null)
  const [newUnitsModal, setNewUnitsModal] = useState(false)
  const [units, setUnits] = useState<IFullUnit[]>([])
  const [materialForUpdate, setmaterialforUpdate] = useState<any[]>([])
  const [crewPositionForUpdate, setCrewPositionForUpdate] = useState<any[]>([])
  const [inputTableValues, setInputTableValues] = useState([])
  const [workerValues, setWorkerValues] = useState([])
  const [editUnitVals, setEditUnitVals] = useState<IFullUnit | null>(null)
  const [editTaskVals, setEditTaskVals] = useState<any>(null)
  const [unitTableValues, setUnitTableValues] = useState([])
  const [taskTableValues, setTaskTableValues] = useState([])
  const [isRoofRepair, setIsRoofRepair] = useState(false)
  const [addInputIndex, setAddInputIndex] = useState(null)
  const [addInputResponse, setAddInputResponse] = useState<any>({})
  const [addMaterialIndex, setAddMaterialIndex] = useState(null)
  const [addMaterialResponse, setAddMaterialResponse] = useState<any>({})

  const [selectedIndex, setSelectedIndex] = useState<null | number>(null)

  const CreateTaskSchema = Yup.object().shape({
    name: Yup.string().min(2, 'Too Short!').max(100, 'Too Long!').required('Required'),
    // .matches(onlyText, 'Enter Valid Name'),
    taskDescription: Yup.string().required('Required'),
    unit: Yup.string(),
    materialName: Yup.string(),
    coverageUnit: Yup.string(),
    materialWaste: Yup.number().max(100, 'Percentage cannot exceed 100%').required('Required'),
    worker: Yup.string(),
    modifiedBy: Yup.string(),
    active: Yup.boolean(),
    group: isRoofRepair ? Yup.string() : Yup.string().required('Required'),
    unitName: Yup.string().required('Required'),
    input: Yup.array().of(
      Yup.object().shape({
        name: Yup.string().required('Required'),
        oper: Yup.string().required('Required'),
      })
    ),
    material: Yup.array().of(
      Yup.object().shape({
        name: Yup.string(),
        cov: Yup.string().when('name', {
          is: (name: string) => !!name, // Check if name has a value
          then: Yup.string().required('Required'),
        }),
      })
    ),
    labor: Yup.array().of(
      Yup.object().shape({
        name: Yup.string().required('Required'),
        time: Yup.string().required('Required'),
      })
    ),
  })

  const [submitLoading, setSubmitLoading] = useState(false)

  const [crewModal, setCrewModal] = useState(false)

  const fetchIdRef = useRef(0)
  const [pageCount, setPageCount] = useState<number>(10)
  const [data, setData] = useState<any[]>([])
  const [detailsUpdate, setDetailsUpdate] = useState(false)
  const [typeGroups, setTypeGroups] = useState<ProjectTypeGroups[]>([])
  const [taskAutofill, setTaskAutofill] = useState<{ name?: string; mat?: string }>({})

  const loadmoreRef = useRef(null)

  const navigate = useNavigate()
  const [selectedType, setSelectedType] = useState('')
  const [group, setGroup] = useState('')
  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company
  const { isLoggedIn } = globalSelector.auth
  const { companies } = globalSelector.company
  const [lastLimit, setLastLimit] = useState(20)

  const inputColumns = [
    {
      Header: 'Name',
      accessor: 'name',
      Cell: ({ row, value }: { row: any; value: string }) => {
        const packagesTasks = row.original.packages // Access packagesTasks from the row data
        const optionsTasks = row.original.options // Access optionsTasks from the row data

        const isEmpty =
          Array.isArray(packagesTasks) &&
          packagesTasks.length === 0 &&
          Array.isArray(optionsTasks) &&
          optionsTasks.length === 0
        return row?.original?.active ? (
          <span style={{ fontWeight: isEmpty ? 'bold' : 'normal' }}>{value}</span>
        ) : (
          <span style={{ fontWeight: isEmpty ? 'bold' : 'normal' }}>
            <Styled.NotActive>{value}</Styled.NotActive>
          </span>
        )
      },
    },
    {
      Header: 'Unit',
      accessor: 'unit',
      Cell: ({ value, row }: { value: string; row: any }) => {
        const packagesTasks = row.original.packages // Access packagesTasks from the row data
        const optionsTasks = row.original.options // Access optionsTasks from the row data

        const isEmpty =
          Array.isArray(packagesTasks) &&
          packagesTasks.length === 0 &&
          Array.isArray(optionsTasks) &&
          optionsTasks.length === 0
        return row?.original?.active ? (
          <span style={{ fontWeight: isEmpty ? 'bold' : 'normal' }}>
            {getUnitSymbolFromId(value, units).split(' ')[0]}
          </span>
        ) : (
          <span style={{ fontWeight: isEmpty ? 'bold' : 'normal' }}>
            <Styled.NotActive>{getUnitSymbolFromId(value, units).split(' ')[0]}</Styled.NotActive>
          </span>
        )
      },
    },
    {
      Header: 'Project Type',
      accessor: 'type',
      Cell: ({ value, row }: { value: string; row: any }) => {
        const packagesTasks = row.original.packages // Access packagesTasks from the row data
        const optionsTasks = row.original.options // Access optionsTasks from the row data

        const isEmpty =
          Array.isArray(packagesTasks) &&
          packagesTasks.length === 0 &&
          Array.isArray(optionsTasks) &&
          optionsTasks.length === 0
        return row?.original?.active ? (
          <span style={{ fontWeight: isEmpty ? 'bold' : 'normal' }}>{getNameFromId(value, types)}</span>
        ) : (
          <span style={{ fontWeight: isEmpty ? 'bold' : 'normal' }}>
            <Styled.NotActive>{getNameFromId(value, types)}</Styled.NotActive>
          </span>
        )
      },
    },
    {
      Header: 'Group',
      accessor: 'group',
      Cell: ({ value, row }: { value: string; row: any }) => {
        const packagesTasks = row.original.packages // Access packagesTasks from the row data
        const optionsTasks = row.original.options // Access optionsTasks from the row data

        const isEmpty =
          Array.isArray(packagesTasks) &&
          packagesTasks.length === 0 &&
          Array.isArray(optionsTasks) &&
          optionsTasks.length === 0
        return row?.original?.active && value ? (
          <span style={{ fontWeight: isEmpty ? 'bold' : 'normal' }}>
            {getNameFrom_Id(value, types?.find((v) => v.id === row?.original?.type)?.groups || [])}
          </span>
        ) : (
          <span style={{ fontWeight: isEmpty ? 'bold' : 'normal' }}>
            <Styled.NotActive>
              {getNameFrom_Id(value, types?.find((v) => v.id === row?.original?.type)?.groups || [])}
            </Styled.NotActive>
          </span>
        )
      },
    },
    {
      Header: 'Worker',
      accessor: (data: any) => data.labor.map((m: any) => m.name).join('; '),
      Cell: ({ value, row }: { value: string; row: any }) => {
        const packagesTasks = row.original.packages // Access packagesTasks from the row data
        const optionsTasks = row.original.options // Access optionsTasks from the row data

        const isEmpty =
          Array.isArray(packagesTasks) &&
          packagesTasks.length === 0 &&
          Array.isArray(optionsTasks) &&
          optionsTasks.length === 0
        return row?.original?.active ? (
          <span style={{ fontWeight: isEmpty ? 'bold' : 'normal' }}>{value}</span>
        ) : (
          <span style={{ fontWeight: isEmpty ? 'bold' : 'normal' }}>
            <Styled.NotActive>{value}</Styled.NotActive>
          </span>
        )
      },
    },
    {
      Header: 'Time',
      accessor: (data: any) => `${data.labor.map((m: any) => m.time).join('')} min`,
      Cell: ({ value, row }: { value: string; row: any }) => {
        const packagesTasks = row.original.packages // Access packagesTasks from the row data
        const optionsTasks = row.original.options // Access optionsTasks from the row data

        const isEmpty =
          Array.isArray(packagesTasks) &&
          packagesTasks.length === 0 &&
          Array.isArray(optionsTasks) &&
          optionsTasks.length === 0
        return row?.original?.active ? (
          <span style={{ fontWeight: isEmpty ? 'bold' : 'normal' }}>{value}</span>
        ) : (
          <span style={{ fontWeight: isEmpty ? 'bold' : 'normal' }}>
            <Styled.NotActive>{value}</Styled.NotActive>
          </span>
        )
      },
    },
    {
      Header: 'MOD',
      accessor: (data: any) => `${modifiedByValues?.[data?.labor?.[0]?.mod]}`,
      Cell: ({ value, row }: { value: string; row: any }) => {
        const packagesTasks = row.original.packages // Access packagesTasks from the row data
        const optionsTasks = row.original.options // Access optionsTasks from the row data

        const isEmpty =
          Array.isArray(packagesTasks) &&
          packagesTasks.length === 0 &&
          Array.isArray(optionsTasks) &&
          optionsTasks.length === 0
        return row?.original?.active ? (
          <span style={{ fontWeight: isEmpty ? 'bold' : 'normal' }}>{value}</span>
        ) : (
          <span style={{ fontWeight: isEmpty ? 'bold' : 'normal' }}>
            <Styled.NotActive>{value}</Styled.NotActive>
          </span>
        )
      },
    },
    {
      Header: 'Material',
      accessor: (data: any) => data.material.map((m: any) => m.name).join('; '),
      Cell: ({ value, row }: { value: string; row: any }) => {
        const packagesTasks = row.original.packages // Access packagesTasks from the row data
        const optionsTasks = row.original.options // Access optionsTasks from the row data

        const isEmpty =
          Array.isArray(packagesTasks) &&
          packagesTasks.length === 0 &&
          Array.isArray(optionsTasks) &&
          optionsTasks.length === 0
        return (
          <>
            {row?.original?.active ? (
              <span style={{ fontWeight: isEmpty ? 'bold' : 'normal' }}>
                <SharedStyled.TooltipContainer
                  positionLeft="0"
                  positionBottom="0"
                  positionLeftDecs="66px"
                  positionBottomDecs="25px"
                >
                  <span className="tooltip-content">{value}</span>
                  {truncateParagraph(value, 20)}
                </SharedStyled.TooltipContainer>
              </span>
            ) : (
              <span style={{ fontWeight: isEmpty ? 'bold' : 'normal' }}>
                <Styled.NotActive>
                  <SharedStyled.TooltipContainer
                    positionLeft="0"
                    positionBottom="0"
                    positionLeftDecs="66px"
                    positionBottomDecs="25px"
                  >
                    <span className="tooltip-content">{value}</span>
                    {truncateParagraph(value, 20)}
                  </SharedStyled.TooltipContainer>
                </Styled.NotActive>
              </span>
            )}
          </>
        )
      },
    },
  ]
  useEffect(() => {
    initFetch()
  }, [])

  const initFetch = async () => {
    try {
      const res = await getProjectTypes({ deleted: false })
      if (isSuccess(res)) {
        const { projectType } = res.data.data
        const object = projectType.map(
          ({
            _id,
            name,
            typeReplacement,
            groups,
            usesPitch,
          }: {
            _id: string
            name: string
            typeReplacement: boolean
            groups: string[]
            usesPitch: boolean
          }) => ({
            name: name,
            id: _id,
            value: _id,
            label: name,
            groups,
            usesPitch,
            typeReplacement,
          })
        )

        // setProjectTypes(projectType)
        const groupArr = extractUniqueGroups(object)

        setTypes(object)
        setGroupType(groupArr)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('Failed init fetch', err)
    }
  }

  useEffect(() => {
    const isRoofReplacement = types?.find((item) => item?.id === selectedType)?.typeReplacement
    const isUsesPitch = types?.find((item) => item?.id === selectedType)?.usesPitch

    const isRoofRepair = !types?.find((item) => item?.id === selectedType)?.typeReplacement

    if (isRoofRepair) {
      setIsRoofRepair(true)
    } else {
      setIsRoofRepair(false)
    }
    if (isRoofReplacement) {
      setModifiedByDropdown(['Nothing', 'Remove', "Remove Add'l Layers", 'Plywood', 'Install'])
    } else {
      setModifiedByDropdown(isUsesPitch ? ['Nothing', 'Pitch'] : ['Nothing'])
      // setModifiedBy('Nothing')
    }
  }, [selectedType])

  useEffect(() => {
    initFetchForMaterial()
  }, [])

  const initFetchForMaterial = async () => {
    fetchCategories()
    fetchSubCategories()
    fetchUnits()
  }
  const fetchCategories = async () => {
    try {
      const res = await getCatergoryApi({ deleted: false })
      if (isSuccess(res)) {
        setCatData(res.data.data.category)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('init fetch failed!', err)
    }
  }

  const fetchSubCategories = async () => {
    try {
      const res = await getSubCatergoryApi({ deleted: false })
      if (isSuccess(res)) {
        setSubCatData(res.data.data.subCategory)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('init fetch failed!', err)
    }
  }
  const fetchUnits = async () => {
    try {
      const res = await getUnitsApi({ deleted: false })
      if (isSuccess(res)) {
        setUnitData(res.data.data.unit)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('init fetch failed!', err)
    }
  }

  const fetchMaterials = async () => {
    try {
      const res = await getMaterialsApi({ deleted: false, limit: 1000 })
      if (isSuccess(res)) {
        const { material } = res.data.data
        let tableData: any = []
        material.forEach((item: any) => {
          tableData.push({
            ...item,
            category: getObjFromId(catData, item.categoryId).name,
            subcategory: getObjFromId(subCatData, item.subCategoryId).name,
            materialName: item.name,
            unit: getObjFromId(unitData, item.unitId).name,
            vendor: vendorDrop[item.vendor - 1],
            inventory: item.inv ? 'Yes' : 'No',
          })
        })
        // setmaterialforUpdate(material)
        setmaterialforUpdate(sortMaterials(material))
        setMaterials(tableData)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('init fetch failed!', err)
    }
  }

  useEffect(() => {
    if (catData.length && subCatData.length && unitData.length) {
      fetchMaterials()
    }
  }, [catData, subCatData, unitData])

  useEffect(() => {
    // const filterWithName = sortMaterials(materials)?.map(
    //   (item: any) => `${item.name} ($${item?.cost}/${item?.unitName}) :-${item._id}`
    // )

    const filterWithName: any = {}
    materialForUpdate.forEach((item) => {
      if (!filterWithName[item.categoryName]) {
        filterWithName[item.categoryName] = {}
      }

      if (!filterWithName[item.categoryName][item.subCategoryName]) {
        filterWithName[item.categoryName][item.subCategoryName] = []
      }

      filterWithName[item.categoryName][item.subCategoryName].push(`${item.name} ($${item?.cost}/${item?.unitName})`)
    })

    setMaterialsDropdownData(filterWithName)
  }, [materialForUpdate])

  const getObjFromId = (obj: any[], id: string) => {
    let [res] = obj.filter((item: any) => {
      return item._id === id
    })
    return res ?? { name: '' }
  }

  const handleAddInput = () => {
    setInput([...input, { calculate: '', calculateBy: '' }])
  }
  const handleRemoveInput = () => {
    if (input.length > 1) {
      const updatedInput = [...input]
      updatedInput.pop()
      setInput(updatedInput)
    }
  }

  const handleAddMaterial = () => {
    setMaterial([...material, { name: '', unit: '' }])
  }
  const handleRemoveMaterial = () => {
    if (material.length > 1) {
      const updatedMaterial = [...material]
      updatedMaterial.pop()
      setMaterial(updatedMaterial)
    }
  }

  useEffect(() => {
    fetchUnit()
  }, [])

  useEffect(() => {
    fetchInputsData()
  }, [measurement, selectedType])

  const fetchUnit = async () => {
    fetchUnitsData()
    fetchInputsData()
  }

  const fetchUnitsData = async () => {
    try {
      const res = await getUnitsApi({ deleted: false, limit: 1000 })
      if (isSuccess(res)) {
        const { unit } = res.data.data
        const tableData = unit.reduce((prev: any, cur: any) => {
          return [
            ...prev,
            {
              ...cur,
              name: cur.name,
              symbol: cur.symbol,
            },
          ]
        }, [])
        setUnits(unit)
        setUnitTableValues(tableData)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('Failed init fetch', err)
    }
  }

  useEffect(() => {
    let unitObj: any = {}
    const filterWithNameAndSymbol = units.map((item: any) => {
      unitObj[`${item.symbol} (${item.name})`] = item
      return `${item.symbol} (${item.name})`
    })
    filterWithNameAndSymbol.push('--Add New--')
    setUnitDrops(unitObj)
    setUnitDropdownData(filterWithNameAndSymbol)
  }, [units])

  useEffect(() => {
    const filterWithName = inputTableValues.map((item: any) => `${item.name} :-${item._id}`)
    // filterWithName.push('--Add New--')
    setInputDropdownData(filterWithName)
  }, [inputTableValues])

  // inputDropdownData
  const fetchInputsData = async () => {
    try {
      const res = await getInputsApi({
        deleted: false,
        unit: getUnitIdFromSymbol(measurement.split(' ')[0], units),
        type: selectedType,
      })
      if (isSuccess(res)) {
        const { input } = res.data.data
        const tableData = input.reduce((prev: any, cur: any) => {
          return [
            ...prev,
            {
              ...cur,
              name: cur?.name,
              unit: cur?.unit,
              order: cur?.orderNumber,
              type: convertKeyToStr(PROJECT_TYPE[cur?.projectType]),
            },
          ]
        }, [])

        setInputTableValues(tableData)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('Failed init fetch', err)
    }
  }

  function sortTasks(tasks: any) {
    return tasks?.sort((a, b) => {
      const groupA = a.group ?? ''
      const groupB = b.group ?? ''
      const orderA = a.order ?? 0
      const orderB = b.order ?? 0

      if (groupA === '' && groupB !== '') {
        return 1
      }
      if (groupA !== '' && groupB === '') {
        return -1
      }
      if (groupA === groupB) {
        return orderA - orderB
      }
      return groupA.localeCompare(groupB)
    })
  }

  const fetchData = useCallback(
    async ({ pageSize, pageIndex, search }: { pageSize: any; pageIndex: any; search?: string }) => {
      try {
        // This will get called when the table needs new data
        setSearchData(search)
        setLoading(true)
        let receivedData: any = []
        const currentCompany: any = getDataFromLocalStorage('currentCompany')

        const clientResponse = await getTaskApi(
          {
            deleted: false,
            limit: pageSize,
            group:
              filterGroup !== 'All' && filterGroup
                ? groupType?.find((grp) => grp?.groupName === filterGroup)?.groupId
                : undefined,
            search,
          },
          filterType?.id
        )

        if (isSuccess(clientResponse)) {
          const { task } = clientResponse?.data?.data
          const tableData = sortTasks(task).reduce((prev: any, cur: any) => {
            return [
              ...prev,
              {
                ...cur,
                name: cur.name,
                type: cur.type,
                unit: cur.unit,
                // labor: cur.labor.tLabor,
                // material: cur.material.tMat,
              },
            ]
          }, [])

          receivedData.push(...tableData)
        } else {
          notify(clientResponse?.data?.message, 'error')
        }

        // Give this fetch an ID
        const fetchId = ++fetchIdRef.current

        // Set the loading state
        // setLoading(true)

        setData(receivedData)

        // We'll even set a delay to simulate a server here
        // setTimeout(() => {
        // Only update the data if this is the latest fetch
        // if (fetchId === fetchIdRef.current) {
        //   const endRow = startRow + pageSize
        //   const startRow = pageSize * pageIndex

        //   // Your server could send back total page count.
        //   // For now we'll just fake it, too
        //   // setPageCount(Math.ceil(receivedData.length / pageSize))
        //   // setLoading(false)
        // }
        // }, 1000)
      } catch (error) {
        console.error('TeamTable fetchData error', error)
      } finally {
        setLoading(false)
      }
    },
    [detailsUpdate, filterType, filterGroup]
  )

  const handleSubmit = async (submittedValues: InitialValues) => {
    try {
      setSubmitLoading(true)
      const typeId = getIdFromName(submittedValues?.type, types)
      const groupById = getIdFromName(submittedValues?.group, typeGroups)
      // submittedValues.labor[0].tLabor = 'LaborId1'
      // submittedValues.labor[0].worker = workerValues
      // ?.find((item: string) => item.split(' :-')[0]?.trim() === worker?.trim())
      // ?.split(' :-')?.[1]
      // submittedValues.type = typeId
      // submittedValues.labor[0].mod = getObjectKeyByValue(modifiedByValues, modifiedBy || 'instMod')!
      submittedValues.unit = getUnitIdFromSymbol(measurement.split(' ')[0], units)
      for (let i = 0; i < submittedValues?.input?.length; i++) {
        submittedValues.input[i].iNum = `InputId${i + 1}`
      }
      for (let m = 0; m < submittedValues?.material?.length; m++) {
        submittedValues.material[m].tMat = `MatId${m + 1}`
        submittedValues.material[m].cov = submittedValues?.material?.[m]?.switch
          ? Number(Number(1 / submittedValues?.material?.[m]?.customValue)?.toFixed(6))
          : submittedValues?.material?.[m]?.cov
      }

      if (submittedValues.input[0].iNum === 'InputId1') submittedValues.input[0].oper = '+'

      submittedValues.createdBy = currentMember._id
      // submittedValues.group = groupById ? groupById : undefined

      submittedValues.material = submittedValues?.material?.map((itm) => {
        const { customValue, ...rest } = itm
        return rest
      })

      submittedValues.material = submittedValues?.material?.map((itm) => itm?.cov)?.filter(Boolean)?.length
        ? submittedValues.material
        : []

      const { materialWaste, taskDescription, ...payload } = submittedValues

      const res = await createTask({
        ...payload,
        waste: materialWaste,
        type: typeId,
        group: groupById ? groupById : undefined,
        labor: [
          {
            tLabor: 'LaborId1',
            worker: workerValues
              ?.find((item: string) => item.split(' :-')[0]?.trim() === worker?.trim())
              ?.split(' :-')?.[1],
            mod: getObjectKeyByValue(modifiedByValues, modifiedBy || 'instMod')!,
            time: submittedValues.labor[0].time,
          },
        ],

        description: taskDescription,
      })

      if (isSuccess(res)) {
        notify(res?.data?.data?.message, 'success')
        setMeasurement('')
        fetchData({
          pageIndex: 0,
          pageSize: 20,
          search: searchData,
        })
        setShowAddModal(false)
        setTaskAutofill({})
        setAddInputResponse({})
        setAddMaterialResponse({})
        setAutoMaterialName('')
        setAutoInputName('')
      }
    } catch (err) {
      console.log('Submit error', err)
    } finally {
      setSubmitLoading(false)
      // setShowAddModal(false)
    }
  }

  useEffect(() => {
    initFetchForWorker()
  }, [detailsUpdate])

  const initFetchForWorker = async () => {
    try {
      const res = await getTasks({}, false)
      if (isSuccess(res)) {
        const { workTask } = res.data.data
        let tableData: any = []
        const data = workTask
          ?.filter((wt: { pieceWork: boolean }) => wt?.pieceWork)
          ?.map((item: any) => `${item.name} ($${item.rate || 0}/Hr) :-${item._id}`)
        data.push('--Add New--')
        setWorkerValues(data)
        // setCrewPositionForUpdate(crewPosition)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('init fetch failed!', err)
    }
  }

  // useEffect(() => {
  //   const filterWithNameAndSymbol = units.map((item: any) => `${item.symbol} (${item.name})`)
  //   filterWithNameAndSymbol.push('--Add New--')
  //   setUnitDropdownData(filterWithNameAndSymbol)
  // }, [units])

  useEffect(() => {
    if (calculated === '--Add New--') {
      setNewInputModal(true)
    }
  }, [calculated])

  useEffect(() => {
    if (materialName === '--Add New--') {
      setMaterialModal(true)
    }
  }, [materialName])

  useEffect(() => {
    if (measurement === '--Add New--') {
      setNewUnitsModal(true)
    }
  }, [measurement])

  useEffect(() => {
    if (worker === '--Add New--') {
      setCrewModal(true)
    }
  }, [worker])

  return (
    <>
      <Styled.TaskCont gap="24px">
        <SharedStyled.FlexRow justifyContent="space-between">
          <SharedStyled.SectionTitle>Project Tasks</SharedStyled.SectionTitle>
          <ButtonCont>
            <Button
              onClick={() => {
                setShowAddModal(true)
              }}
            >
              Add task
            </Button>
          </ButtonCont>
        </SharedStyled.FlexRow>
        <SharedStyled.FlexRow alignItems="flex-start">
          <SharedStyled.FlexCol gap="24px">
            <TabBar
              className="end"
              tabs={[
                {
                  title: 'Active',
                  render: () => (
                    <>
                      <Table
                        noOverflow
                        columns={inputColumns}
                        data={data}
                        loading={loading}
                        // pageCount={1}
                        fetchData={fetchData}
                        onRowClick={(vals) => {
                          setCustomizeTaskModal(true)
                          setEditTaskVals(vals)
                        }}
                        minWidth=""
                        noBorder
                        ref={loadmoreRef}
                        isLoadMoreLoading={loading}
                        setLastLimit={setLastLimit}
                      />
                    </>
                  ),
                },
                {
                  title: 'Deleted',
                  render: () => <DeletedTasks types={types} units={units} />,
                },
              ]}
              filterComponent={
                <SharedStyled.FlexRow justifyContent="flex-end">
                  <CustomSelect
                    labelName="Filter by type"
                    stateName=""
                    value={filterType?.name}
                    dropDownData={[...types.map(({ name }: { name: string }) => name), 'All']}
                    setValue={(val: string) => {
                      const extractValue = types?.find((item) => item.name === val)
                      if (!extractValue) {
                        setFilterType({ name: 'All', id: undefined })
                        return
                      }

                      setFilterType(extractValue)
                      setFilterGroup('All')
                    }}
                    setFieldValue={() => {}}
                    innerHeight="50px"
                    margin="10px 0 0 0"
                    maxWidth="200px"
                  />
                  <CustomSelect
                    labelName="Filter by group"
                    stateName=""
                    value={filterGroup}
                    dropDownData={[...groupType?.map((itm) => itm?.groupName), 'All']}
                    setValue={(val: string) => {
                      setFilterGroup(val)
                    }}
                    setFieldValue={() => {}}
                    innerHeight="50px"
                    margin="10px 0 0 0"
                    maxWidth="200px"
                  />
                </SharedStyled.FlexRow>
              }
            />
          </SharedStyled.FlexCol>
        </SharedStyled.FlexRow>

        <CustomModal show={showAddModal}>
          <AddTaskModal
            onClose={() => {
              setShowAddModal(false)
              setProjectType('')
              setMeasurement('')
              setEditTaskVals(null)
              setAddInputResponse({})
              setAddMaterialResponse({})
              setAutoMaterialName('')
              setAutoInputName('')
            }}
            showAddModal={showAddModal}
          >
            <Formik
              initialValues={initialValues}
              onSubmit={handleSubmit}
              validationSchema={CreateTaskSchema}
              enableReinitialize={true}
              validateOnChange={true}
              validateOnBlur={false}
            >
              {({ values, errors, touched, setFieldValue }) => {
                const testId = (id: string, name: string, index: number | undefined) => {}

                const scrollToFirstError = () => {
                  const errorFields = Object.keys(errors) // Get fields with errors

                  const firstErrorOrEmptyField = errorFields.find((field: any) => errors[field])
                  if (firstErrorOrEmptyField) {
                    const element = document.querySelector(`[name="${firstErrorOrEmptyField}"]`)
                    if (element) {
                      element.scrollIntoView({ behavior: 'smooth', block: 'center' })
                    }
                  }
                }

                useEffect(() => {
                  if (values?.type !== '') {
                    const typeObject: any = types?.find((v) => v.name === values?.type)

                    if (typeObject?.groups?.length) {
                      setTypeGroups([...(typeObject?.groups || [])])
                    } else {
                      setTypeGroups([])
                    }
                  }
                }, [values?.type])

                useEffect(() => {
                  if (values.type !== '') {
                    setSelectedType(getIdFromName(values.type, types))
                  }
                }, [values.type])

                useEffect(() => {
                  if (Object.entries(taskAutofill).length) {
                    setFieldValue(`material[${selectedIndex}].name`, taskAutofill?.name)
                    setFieldValue(`material[${selectedIndex}].mat`, taskAutofill?.mat)
                  }
                }, [taskAutofill])

                useEffect(() => {
                  if (Object?.entries?.(addInputResponse || {})?.length) {
                    setFieldValue(`input.${addInputIndex}.input`, addInputResponse._id)
                    setFieldValue(`input.${addInputIndex}.name`, addInputResponse.name)
                  }
                }, [addInputResponse])

                useEffect(() => {
                  if (Object?.entries?.(addMaterialResponse || {})?.length) {
                    setFieldValue(`material.${addMaterialIndex}.mat`, addMaterialResponse._id)
                    setFieldValue(
                      `material.${addMaterialIndex}.name`,
                      `${addMaterialResponse.name} ($${addMaterialResponse?.cost}/${getValueByKeyAndMatch(
                        'name',
                        addMaterialResponse?.unitId,
                        '_id',
                        units
                      )})`
                    )
                  }
                }, [addMaterialResponse])

                return (
                  <Styled.ProfileContainer>
                    <Form className="form taskForm">
                      {/* <SharedStyled.FlexBox flexDirection="column">
                        <SharedStyled.FlexRow>
                          <SharedStyled.Text fontSize="14px" fontWeight="medium" style={{ whiteSpace: 'nowrap' }}>
                            Project Type :
                          </SharedStyled.Text>
                          <SharedStyled.ErrorContainer>
                            <SharedStyled.ErrorMsg>
                              <ErrorMessage name={'type'} component="div" className="error-message" />
                            </SharedStyled.ErrorMsg>
                          </SharedStyled.ErrorContainer>
                        </SharedStyled.FlexRow>
                        <RadioButtonGroup
                          onChange={(e) => {
                            onTypeChange(e.target.value)
                            setFieldValue('type', e.target.value)
                          }}
                          radioButtons={types?.map((item) => ({ ...item, name: 'type' }))}
                        />
                      </SharedStyled.FlexBox> */}

                      <CustomSelect
                        labelName="Project Type"
                        error={!!errors?.type}
                        value={values.type}
                        dropDownData={types?.map((v) => v.name)}
                        setValue={setProjectType}
                        setFieldValue={setFieldValue}
                        innerHeight="45px"
                        margin="10px 0 0 0"
                        stateName="type"
                      />

                      <Styled.InfoContainer>
                        <InputWithValidation
                          labelName="Task name"
                          stateName="name"
                          error={touched.name && errors.name ? true : false}
                          twoInput={true}
                        />
                        <SharedStyled.Text fontSize="14px" fontWeight="medium">
                          Description (seen on the contract page):{' '}
                        </SharedStyled.Text>
                        <Styled.TextArea
                          component="textarea"
                          as={Field}
                          name="taskDescription"
                          marginTop="8px"
                          height="52px"
                          // placeHolder="ie. Remove existing shingle,flashing, and underlayment; then install new ice shield, flashing, and shingle to match"
                          error={touched.taskDescription && errors.taskDescription ? true : false}
                        />
                        {errors?.taskDescription && (
                          <SharedStyled.ErrorMsg>
                            <ErrorMessage name={'taskDescription'} component="div" className="error-message" />
                          </SharedStyled.ErrorMsg>
                        )}

                        <InputWithValidation
                          labelName="Work Order"
                          stateName="order"
                          forceType="number"
                          error={touched.order && errors.order ? true : false}
                        />

                        <SharedStyled.FlexRow margin="16px 0 16px 0">
                          <SharedStyled.Text fontSize="14px" fontWeight="500" className="bold-text">
                            How is it measured?
                          </SharedStyled.Text>
                        </SharedStyled.FlexRow>
                        <CustomSelect
                          labelName="Unit of measurement"
                          error={!!touched?.unitName && errors?.unitName}
                          value={measurement}
                          dropDownData={unitDropdownData}
                          setValue={setMeasurement}
                          setFieldValue={setFieldValue}
                          innerHeight="45px"
                          margin="10px 0 0 0"
                          stateName="unitName"
                        />

                        <SharedStyled.FlexCol alignItems="stretch" margin="20px 0 0 0">
                          <SharedStyled.FlexRow justifyContent="space-between">
                            <SharedStyled.Text fontSize="14px" fontWeight="medium">
                              How is it calculated? (only inputs with the same unit are shown){' '}
                            </SharedStyled.Text>
                            <SharedStyled.Text fontSize="14px" fontWeight="medium">
                              calculated by
                            </SharedStyled.Text>
                          </SharedStyled.FlexRow>
                          <FieldArray name="input">
                            {({ insert, remove, push }) => (
                              <>
                                {values.input?.length > 0 &&
                                  values.input.map((input: any, index: number) => (
                                    <Styled.SaperatorDiv key={index}>
                                      <div>
                                        {/* <CustomSelect
                                          labelName="Pick an input"
                                          error={
                                            touched.input?.[index]?.name && errors.input?.[index]?.name ? true : false
                                          }
                                          value={values.input[index].name?.trim()}
                                          dropDownData={inputDropdownData.map((item: any) =>
                                            item.split(' :-')[0]?.trim()
                                          )}
                                          setValue={setCalculated}
                                          setFieldValue={setFieldValue}
                                          testId={testId}
                                          dropDownDataForId={inputDropdownData}
                                          index={index}
                                          innerHeight="45px"
                                          margin="10px 0 0 0"
                                          stateName={`input.${index}.name`}
                                          selectedIdName={`input.${index}.input`}
                                        /> */}

                                        <AutoComplete
                                          labelName="Pick an input"
                                          stateName={`input.${index}.name`}
                                          error={
                                            touched.input?.[index]?.name && errors.input?.[index]?.name ? true : false
                                          }
                                          setFieldValue={setFieldValue}
                                          options={inputDropdownData.map((item: any) => item.split(' :-')[0]?.trim())}
                                          value={values.input[index].name}
                                          showAddOption
                                          autoFillData={''}
                                          onAddClick={(val) => {
                                            setFieldValue(`input.${index}.name`, val)
                                            setCalculated('--Add New--')
                                            setAddInputIndex(index)
                                            setAutoInputName(val)
                                            setTempInputName(val)
                                          }}
                                          borderRadius="0px"
                                          setValueOnClick={(val: string) => {
                                            setCalculated(val)
                                            setFieldValue(
                                              `input.${index}.input`,
                                              inputDropdownData
                                                ?.find((item: any) => item.split(' :-')[0]?.trim() === val?.trim())
                                                ?.split(' :-')[1]
                                            )
                                            setFieldValue(`input.${index}.name`, val)
                                          }}
                                          className="material-autocomplete"
                                        />
                                      </div>
                                      <Styled.Gap className="margin-left">
                                        <CustomSelect
                                          labelName=""
                                          error={
                                            touched.input?.[index]?.oper && errors.input?.[index]?.oper ? true : false
                                          }
                                          value={values.input[index].oper}
                                          dropDownData={['+', '-', 'x', '/']}
                                          setValue={setCalculatedBy}
                                          setFieldValue={setFieldValue}
                                          innerHeight="45px"
                                          // margin="10px 0 0 0"
                                          stateName={`input.${index}.oper`}
                                        />
                                      </Styled.Gap>

                                      {values.input.length > 1 && (
                                        <div style={{ margin: '8px 0 0 10px' }}>
                                          <Button
                                            padding="8px 15px"
                                            className="fit delete"
                                            type="button"
                                            onClick={() => remove(index)}
                                          >
                                            X
                                          </Button>
                                        </div>
                                      )}
                                    </Styled.SaperatorDiv>
                                  ))}
                                <SharedStyled.ButtonContainer justifyContent="start" margin="10px 0 20px 0">
                                  <Button
                                    width="max-content"
                                    type="button"
                                    onClick={() => push({ iNum: '', input: '', oper: '+', name: '' })}
                                  >
                                    Add Input
                                  </Button>
                                  {/* &emsp;
                                  <Button
                                    width="max-content"
                                    className="delete"
                                    type="button"
                                    onClick={() => {
                                      values.input.length > 1 ? remove(values.input.length - 1) : null
                                    }}
                                  >
                                    Remove Input
                                  </Button> */}
                                </SharedStyled.ButtonContainer>
                              </>
                            )}
                          </FieldArray>
                        </SharedStyled.FlexCol>

                        <SharedStyled.Text fontSize="14px" margin="12px 0 0 0" fontWeight="500" className="bold-text">
                          Materials Needed
                        </SharedStyled.Text>
                        <FieldArray name="material">
                          {({ insert, remove, push }) => (
                            <>
                              {values?.material?.length > 0 &&
                                values?.material?.map((input: any, index: number) => (
                                  <Styled.SaperatorDiv key={index}>
                                    <Styled.SapceWidth>
                                      {/* <CustomSelect
                                        labelName="Material Name"
                                        error={
                                          touched.material?.[index]?.name && errors.material?.[index]?.name
                                            ? true
                                            : false
                                        }
                                        value={values.material[index].name?.trim()}
                                        dropDownData={materialsDropdownData.map((item: any) =>
                                          item.split(':-')[0]?.trim()
                                        )}
                                        setValue={setMaterialName}
                                        setFieldValue={setFieldValue}
                                        dropDownDataForId={materialsDropdownData}
                                        testId={testId}
                                        innerHeight="45px"
                                        margin="10px 0 0 0"
                                        stateName={`material.${index}.name`}
                                        selectedIdName={`material.${index}.mat`}
                                      /> */}

                                      <AutoCompleteIndentation
                                        labelName="Material Name"
                                        stateName={`material.${index}.name`}
                                        dropdownHeight="300px"
                                        error={
                                          touched.material?.[index]?.name && errors.material?.[index]?.name
                                            ? true
                                            : false
                                        }
                                        showAddOption
                                        onAddClick={(val) => {
                                          setSelectedIndex(index)
                                          setAutoMaterialName(val)
                                          setFieldValue(`material.${index}.name`, val)
                                          setAddMaterialIndex(index)
                                          setMaterialName('--Add New--')
                                          setTempMaterialName(val)
                                        }}
                                        borderRadius="0px"
                                        setFieldValue={setFieldValue}
                                        // options={materialsDropdownData
                                        //   .map((item: any) => item.split(':-')[0]?.trim())
                                        //   ?.filter(
                                        //     (name: string) => !values?.material?.some((obj) => obj.name === name)
                                        //   )}
                                        options={
                                          Object.values(materialsDropdownData)
                                            .flatMap((category) => Object.values(category))
                                            .flat()
                                            ?.filter(
                                              (name: string) => !values?.material?.some((obj) => obj.name === name)
                                            ) as string[]
                                        }
                                        formatedOptions={materialsDropdownData}
                                        value={values?.material?.[index].name}
                                        setValueOnClick={(val: string) => {
                                          setMaterialName(val)
                                          setFieldValue(
                                            `material.${index}.mat`,
                                            getValueByKeyAndMatch(
                                              '_id',
                                              val.split('($')[0]?.trim(),
                                              `name`,
                                              materialForUpdate
                                            )
                                          )
                                        }}
                                        className="material-autocomplete"
                                        isIndentation={true}
                                      />
                                    </Styled.SapceWidth>
                                    &emsp;
                                    <Styled.SapceWidth>
                                      <InputWithValidation
                                        value={
                                          values?.material?.[index]?.switch
                                            ? values?.material?.[index].customValue
                                            : values?.material?.[index].cov
                                        }
                                        labelName={
                                          values?.material[index].mat
                                            ? `1 ${
                                                values?.material?.[index]?.switch
                                                  ? unitDrops[values?.unitName]?.name
                                                  : materialForUpdate?.find(
                                                      (itm: any) => itm?._id === values?.material[index].mat
                                                    )?.unitName
                                              } ${values?.material?.[index]?.switch ? 'uses' : 'covers'} how many ${
                                                values?.material?.[index]?.switch
                                                  ? materialForUpdate?.find(
                                                      (itm: any) => itm?._id === values?.material[index].mat
                                                    )?.unitName
                                                  : unitDrops[values?.unitName]?.name || ''
                                              }?`
                                            : 'Please select unit and material'
                                        }
                                        stateName={
                                          values?.material?.[index]?.switch
                                            ? `material.${index}.customValue`
                                            : `material.${index}.cov`
                                        }
                                        error={
                                          touched.material?.[index]?.cov && errors.material?.[index]?.cov ? true : false
                                        }
                                        forceType="number"
                                        twoInput={true}
                                      />
                                    </Styled.SapceWidth>
                                    &emsp;
                                    <SharedStyled.FlexRow width="max-content" margin="8px 0 0 0" alignItems="center">
                                      <Button
                                        style={{ display: 'flex' }}
                                        type="button"
                                        padding="6px 8px"
                                        bgColor={values?.material?.[index]?.switch ? '#1aa41a' : '#E9ECEF'}
                                        onClick={() => {
                                          setFieldValue(`material.${index}.switch`, !values.material[index].switch)
                                          // if (!Number.isInteger(values?.material?.[index]?.cov)) {

                                          if (values?.material?.[index]?.customValue) {
                                            setFieldValue(
                                              `material.${index}.cov`,
                                              values?.material?.[index]?.cov
                                                ? Math.round(values?.material?.[index]?.cov)
                                                : Number(Number(1 / values?.material?.[index]?.customValue)?.toFixed(6))
                                            )
                                          } else {
                                            setFieldValue(
                                              `material.${index}.customValue`,

                                              values?.material?.[index]?.customValue
                                                ? Math.round(values?.material?.[index]?.customValue)
                                                : Number(Number(1 / values?.material?.[index]?.cov)?.toFixed(6))
                                            )
                                          }

                                          // }
                                        }}
                                      >
                                        <img src={ReverseSvg} alt="switch-icon" />
                                      </Button>

                                      {values?.material?.length > 1 && (
                                        <div style={{ margin: '0 0 0 10px' }}>
                                          <Button
                                            padding="8px 15px"
                                            className="fit delete"
                                            type="button"
                                            onClick={() => remove(index)}
                                          >
                                            X
                                          </Button>
                                        </div>
                                      )}
                                    </SharedStyled.FlexRow>
                                  </Styled.SaperatorDiv>
                                ))}
                              <SharedStyled.ButtonContainer justifyContent="start" marginTop="10px">
                                <Button
                                  width="max-content"
                                  type="button"
                                  marginTop="10px"
                                  onClick={() => push({ tMat: '', mat: '', cov: '', name: '' })}
                                >
                                  Add Material
                                </Button>
                                {/* &emsp;
                                <Button
                                  type="button"
                                  width="max-content"
                                  className="delete"
                                  onClick={() =>
                                    values?.material?.length > 1 ? remove(values?.material?.length - 1) : null
                                  }
                                >
                                  Remove Material
                                </Button> */}
                              </SharedStyled.ButtonContainer>
                            </>
                          )}
                        </FieldArray>

                        <SharedStyled.FlexRow margin="16px 0 16px 0"></SharedStyled.FlexRow>
                        <div>
                          <Styled.NameValueUnitContainer width={'100%'}>
                            <SharedStyled.FlexCol>
                              <InputLabelWithValidation
                                disabled={
                                  values?.material?.length === 1 &&
                                  (values?.material?.[0].name === '' || values?.material?.[0].cov === '')
                                }
                                stateName={`materialWaste`}
                                labelName="Material Waste (%)"
                                error={touched.materialWaste && errors.materialWaste ? true : false}
                                labelUnit={'%'}
                                inputType="number"
                                onWheel={(e) => {
                                  e.target.blur()
                                }}
                              />
                            </SharedStyled.FlexCol>
                          </Styled.NameValueUnitContainer>
                        </div>

                        <SharedStyled.FlexRow margin="16px 0 16px 0">
                          <SharedStyled.Text fontSize="14px" fontWeight="500" className="bold-text">
                            Labor Required
                          </SharedStyled.Text>
                        </SharedStyled.FlexRow>
                        <Styled.SaperatorDiv>
                          <Styled.SapceWidth>
                            {/* <SharedStyled.Text fontSize="14px" fontWeight="medium">
                              Worker: <SharedStyled.AstricColor>*</SharedStyled.AstricColor>
                            </SharedStyled.Text> */}
                            <CustomSelect
                              labelName="Worker:"
                              error={touched.labor?.[0]?.name && errors.labor?.[0]?.name}
                              value={values.labor[0].name?.trim()}
                              dropDownData={workerValues.map((item: any) => item.split(':-')[0]?.trim())}
                              setValue={setWorker}
                              setFieldValue={setFieldValue}
                              testId={testId}
                              dropDownDataForId={workerValues}
                              innerHeight="45px"
                              margin="10px 0 0 0"
                              stateName={`labor.[0].name`}
                              selectedIdName={`labor.[0].worker`}
                            />
                          </Styled.SapceWidth>
                          &emsp;
                          <Styled.SapceWidth>
                            <SharedStyled.Text fontSize="14px" fontWeight="medium">
                              {/* Time (min): <SharedStyled.AstricColor>*</SharedStyled.AstricColor> */}
                            </SharedStyled.Text>
                            <Styled.NameValueUnitContainer width={'100%'}>
                              {/* <Styled.PercentValueInput
                                value={values.labor[0].time}
                                width={'100%'}
                                name="labor[0].time"
                                type="number"
                                placeholder="How long does it take?"
                                borderRadius="4px 0px 0px 4px"
                              />
                              <Styled.UnitDiv>Min</Styled.UnitDiv> */}

                              <InputLabelWithValidation
                                stateName={'labor[0].time'}
                                labelName="Time"
                                labelUnit={'Min'}
                                inputType="number"
                                error={touched.labor?.[0]?.time && errors.labor?.[0]?.time ? true : false}
                              />
                            </Styled.NameValueUnitContainer>
                          </Styled.SapceWidth>
                          &emsp;
                          <Styled.SapceWidth>
                            <CustomSelect
                              labelName="Modified By..."
                              error={false}
                              value={values.labor[0].mod}
                              dropDownData={modifiedByDropdown}
                              setValue={setModifiedBy}
                              setFieldValue={setFieldValue}
                              innerHeight="45px"
                              margin="10px 0 0 0"
                              stateName="labor[0].mod"
                            />
                          </Styled.SapceWidth>
                        </Styled.SaperatorDiv>

                        {/* {isRoofRepair ? null : (
                          <SharedStyled.FlexBox marginTop="10px" flexDirection="column">
                            <CustomSelect
                              labelName={`Group`}
                              error={!!errors.group}
                              value={values.group}
                              dropDownData={Object.values(TaskGroups).map((v) => v)}
                              setValue={setGroup}
                              testId={testId}
                              setFieldValue={setFieldValue}
                              innerHeight="45px"
                              margin="10px 0 0 0"
                              stateName={`group`}
                            />
                          </SharedStyled.FlexBox>
                        )} */}

                        <SharedStyled.FlexBox marginTop="10px" flexDirection="column">
                          <CustomSelect
                            labelName={`Group`}
                            error={touched.group && errors.group ? true : false}
                            value={values.group}
                            dropDownData={typeGroups?.map((v) => v.name)}
                            setValue={setGroup}
                            testId={testId}
                            setFieldValue={setFieldValue}
                            innerHeight="45px"
                            margin="10px 0 0 0"
                            stateName={`group`}
                          />
                        </SharedStyled.FlexBox>

                        <Toggle
                          title="Active"
                          customStyles={{ marginBottom: '16px', marginTop: '26px', fontSize: '14px' }}
                          isToggled={!!values?.active}
                          onToggle={() => {
                            setFieldValue('active', !values?.active)
                          }}
                          className="text"
                        />
                        <SharedStyled.ButtonContainer marginTop="20px">
                          <Button
                            width="max-content"
                            type="submit"
                            onClick={() => {
                              scrollToFirstError()
                            }}
                            isLoading={submitLoading}
                          >
                            Save New Task
                          </Button>
                        </SharedStyled.ButtonContainer>
                      </Styled.InfoContainer>
                    </Form>
                  </Styled.ProfileContainer>
                )
              }}
            </Formik>
          </AddTaskModal>
        </CustomModal>
        <CustomModal show={customizeTaskModal}>
          {customizeTaskModal && (
            <CustomizeTaskModal
              taskAutofill={taskAutofill}
              onClose={() => {
                setCustomizeTaskModal(false)
                setEditTaskVals(null)
                setProjectType('')
                setMeasurement('')
                setAddInputResponse({})
                setAddMaterialResponse({})
                setAutoMaterialName('')
                setAutoInputName('')
              }}
              onComplete={() => {
                fetchData({
                  pageIndex: 0,
                  pageSize: lastLimit,
                  search: searchData,
                })
                setMeasurement('')
                setAddInputResponse({})
                setAddMaterialResponse({})
                setAutoMaterialName('')
                setAutoInputName('')
              }}
              inputData={editTaskVals}
              isEditing={!!editTaskVals}
              handleUnitModal={() => {
                setNewUnitsModal(true)
                setCustomizeTaskModal(false)
              }}
              handleMaterialModal={(value) => {
                setTempMaterialName(value)
                setMaterialModal(true)
              }}
              handleInputModal={(val) => {
                setTempInputName(val)
                setNewInputModal(true)
                // setCustomizeTaskModal(false)
              }}
              // units={units}
              // inputTableValues={inputTableValues}
              setProjectType={setProjectType}
              // inputDropdownData={inputDropdownData}
              materialForUpdate={materialForUpdate}
              workerValues={workerValues}
              types={types}
              handleAutoPick={{
                addInputIndex,
                setAddInputIndex,
                addInputResponse,
                addMaterialIndex,
                setAddMaterialIndex,
                addMaterialResponse,
                setMeasurement,
                measurement,
                setAutoMaterialName,
                setAutoInputName,
              }}
            />
          )}
        </CustomModal>
        {/* showModalForNewCalculated*/}
        <CustomModal show={newInputModal}>
          <InputModal
            onClose={() => {
              setNewInputModal(false)
              setEditInputVals(null)
              setTempInputName('')
              setCalculated('')
            }}
            onComplete={() => {
              fetchInputsData()
              setTempInputName('')
              setCalculated('')
              setAutoInputName('')
            }}
            handleUnitModal={() => {
              setNewUnitsModal(true)
              setNewInputModal(false)
            }}
            setAddInputResponse={setAddInputResponse}
            isEditing={!!editInputVals}
            inputData={editInputVals}
            inputName={tempInputName}
            measurement={measurement}
            projectType={projectType}
            units={units}
            projectTypesDrop={types}
            autoInputName={autoInputName}
          />
        </CustomModal>
        <CustomModal show={newUnitsModal}>
          <NewUnitModal
            onClose={() => {
              setNewUnitsModal(false)
              setEditUnitVals(null)
            }}
            onComplete={fetchUnitsData}
            isEditing={!!editUnitVals}
            inputData={editUnitVals}
          />
        </CustomModal>

        <CustomModal show={materialModal}>
          <MaterialsModal
            onClose={() => {
              setMaterialModal(false)
              setInputData([])
              setMaterialName('')
              setTempMaterialName('')
              setSelectedIndex(null)
            }}
            catData={catData}
            subCatData={subCatData}
            unitData={unitData}
            onAddCategoryClick={() => {
              setMaterialModal(false)
              setCategoryModal(true)
              setInputData([])
            }}
            onAddSubCategoryClick={() => {
              setMaterialModal(false)
              setSubCategoryModal(true)
              setInputData([])
            }}
            onComplete={(data) => {
              setTaskAutofill({
                name: `${data.name} ($${data?.cost ?? '--'}/${unitDrops?.[data?.unitName ?? '--']?.name})`,
                mat: data?._id,
              })
              // setFieldValue(
              //   `material[${selectedIndex}].name`,
              //   `${data.name} ($${data?.cost}/${unitDrops?.[data?.unitName]?.name})`
              // )
              // setFieldValue(`material[${selectedIndex}].mat`, data?._id)
              // setNewMaterial({
              //   name: `${data.name} ($${data?.cost}/${unitDrops?.[data?.unitName]?.name})`,
              //   mat: data?._id,
              // })
              fetchMaterials()
              setInputData([])
              setTempMaterialName('')
              setSelectedIndex(null)
              setMaterialModal(false)
              setAutoMaterialName('')
            }}
            measurement={measurement}
            autoMaterialName={autoMaterialName}
            setAddMaterialResponse={setAddMaterialResponse}
            inputData={{ ...inputData, materialName: tempMaterialName }}
          />
        </CustomModal>
        <CustomModal show={categoryModal}>
          <CategoryModal
            onClose={() => {
              setCategoryModal(false)
            }}
            onComplete={() => {
              setCategoryModal(false)
              fetchCategories()
            }}
          />
        </CustomModal>
        <CustomModal show={subCategoryModal}>
          <SubcategoryModal
            categories={catData}
            onClose={() => {
              setSubCategoryModal(false)
            }}
            onComplete={() => {
              setSubCategoryModal(false)
              fetchSubCategories()
            }}
          />
        </CustomModal>
        <CustomModal show={crewModal}>
          <CreateTaskPopUp setShowCreateTaskPopUp={setCrewModal} setDetailsUpdate={setDetailsUpdate} />
          {/* <CrewPositionModal
            onComplete={initFetchForWorker}
            onClose={() => {
              setCrewModal(false)
              setEditInputVals(null)
            }}
            inputData={editInputVals}
          /> */}
        </CustomModal>
      </Styled.TaskCont>
    </>
  )
}

export default Tasks
