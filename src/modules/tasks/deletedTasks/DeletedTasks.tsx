import React, { useCallback, useEffect, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate, useParams } from 'react-router-dom'
import { getTaskApi, permDeleteTasks, restoreTasks } from '../../../logic/apis/projects'
import { CustomModal } from '../../../shared/customModal/CustomModal'
import {
  getDataFromLocalStorage,
  getNameFromId,
  getUnitSymbolFromId,
  isSuccess,
  notify,
} from '../../../shared/helpers/util'
import { Table } from '../../../shared/table/Table'
import * as SharedStyled from '../../../styles/styled'
import DeletedModal from '../../deleted/components/deletedModal/DeletedModal'
// import { inputColumns } from '../taskTable/column'
import * as Styled from './style'
import { NotActive } from '../style'
import { StorageKey } from '../../../shared/helpers/constants'

const DeletedTasks = ({ units, types }: { units: any[]; types: any[] }) => {
  const [taskTableValues, setTaskTableValues] = useState([])
  const [inputData, setInputData] = useState<any>(null)
  const [deletedModal, setDeletedModal] = useState(false)
  const [loading, setLoading] = useState(true)

  const navigate = useNavigate()

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company

  const fetchIdRef = useRef(0)
  // const [pageCount, setPageCount] = useState<number>(10)
  const [data, setData] = useState<any[]>([])
  // const [detailsUpdate, setDetailsUpdate] = useState(false)

  const loadmoreRef = useRef(null)

  const inputColumns = [
    {
      Header: 'Name',
      accessor: 'name',
    },
    {
      Header: 'Job Type',
      accessor: 'type',
      Cell: ({ value, row }: { value: string; row: any }) =>
        row?.original?.active ? getNameFromId(value, types) : <NotActive>{getNameFromId(value, types)}</NotActive>,
    },
    {
      Header: 'Unit',
      accessor: 'unit',
      Cell: ({ value, row }: { value: string; row: any }) =>
        row?.original?.active ? (
          getUnitSymbolFromId(value, units).split(' ')[0]
        ) : (
          <NotActive>{getUnitSymbolFromId(value, units).split(' ')[0]}</NotActive>
        ),
    },
    {
      Header: 'Worker',
      accessor: (data: any) => data.labor.map((m: any) => m.name).join('; '),
    },
    {
      Header: 'Material',
      accessor: (data: any) => data.material.map((m: any) => m.name).join('; '),
    },
  ]

  // useEffect(() => {
  //   if (currentCompany && currentCompany._id) {
  //     fetchTask()
  //   }
  // }, [currentCompany])

  // const fetchTask = async () => {
  //   try {
  //     const res = await getTaskApi({ companyId: currentCompany._id, deleted: true })
  //     if (isSuccess(res)) {
  //       const { task } = res?.data?.data
  //       const tableData = task.reduce((prev: any, cur: any) => {
  //         return [
  //           ...prev,
  //           {
  //             ...cur,
  //             name: cur.name,
  //             type: cur.type,
  //             unit: cur.unit,
  //             // labor: cur.labor.tLabor,
  //             // material: cur.material.tMat,
  //           },
  //         ]
  //       }, [])

  //       setLoading(false)
  //       setTaskTableValues(tableData)
  //     } else throw new Error(res?.data?.message)
  //   } catch (error) {
  //     setLoading(false)
  //     console.error('init fetch failed!', error)
  //   }
  // }

  const fetchData = useCallback(async ({ pageSize, pageIndex }: any) => {
    try {
      // This will get called when the table needs new data
      setLoading(true)
      let receivedData: any = []
      const currentCompany: any = getDataFromLocalStorage('currentCompany')

      const clientResponse = await getTaskApi({ deleted: true, limit: pageSize })

      if (isSuccess(clientResponse)) {
        const { task } = clientResponse?.data?.data

        const tableData = task.reduce((prev: any, cur: any) => {
          return [
            ...prev,
            {
              ...cur,
              name: cur.name,
              type: cur.type,
              unit: cur.unit,
              // labor: cur.labor.tLabor,
              // material: cur.material.tMat,
            },
          ]
        }, [])

        receivedData.push(...tableData)
      } else {
        notify(clientResponse?.data?.message, 'error')
      }

      // Give this fetch an ID
      const fetchId = ++fetchIdRef.current

      // Set the loading state
      // setLoading(true)

      // We'll even set a delay to simulate a server here
      // setTimeout(() => {
      // Only update the data if this is the latest fetch
      if (fetchId === fetchIdRef.current) {
        const startRow = pageSize * pageIndex
        const endRow = startRow + pageSize
        setData(receivedData.slice(startRow, endRow))

        // Your server could send back total page count.
        // For now we'll just fake it, too
        // setPageCount(Math.ceil(receivedData.length / pageSize))
        // setLoading(false)
      }
      // }, 1000)
    } catch (error) {
      console.error('TeamTable fetchData error', error)
    } finally {
      setLoading(false)
    }
  }, [])

  const onComplete = () => {
    setInputData(null)
    setDeletedModal(false)
    fetchData({
      pageIndex: 0,
      pageSize: 20,
    })
  }

  const onDelete = async () => {
    try {
      const res = await permDeleteTasks({
        id: inputData?._id ?? '',
      })
      if (isSuccess(res)) {
        notify(`Deleted task!`, 'success')
        onComplete()
      } else throw new Error(res?.data?.message)
    } catch (err) {
      notify('Failed to delete task!', 'error')
      console.error('Input err', err)
    }
  }

  const onRestore = async () => {
    try {
      const res = await restoreTasks({
        id: inputData?._id ?? '',
      })
      if (isSuccess(res)) {
        notify(`Restored task!`, 'success')
        onComplete()
      } else throw new Error(res?.data?.message)
    } catch (err) {
      notify('Failed to restore task!', 'error')
      console.error('Input err', err)
    }
  }
  return (
    <>
      <Table
        columns={inputColumns}
        data={data}
        loading={loading}
        fetchData={fetchData}
        onRowClick={(data) => {
          setInputData(data)
          setDeletedModal(true)
        }}
        noSearch
        minWidth=""
        noBorder
        // client={true}
        ref={loadmoreRef}
        isLoadMoreLoading={loading}
      />

      <CustomModal show={deletedModal}>
        <DeletedModal
          onDelete={onDelete}
          onRestore={onRestore}
          onClose={() => {
            setDeletedModal(false)
            setInputData(null)
          }}
          inputData={inputData}
          title="Deleted Task"
        />
      </CustomModal>
    </>
  )
}

export default DeletedTasks
