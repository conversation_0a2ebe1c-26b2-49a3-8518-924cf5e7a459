import React, { useCallback, useEffect, useRef, useState } from 'react'
import * as SharedStyled from '../../../styles/styled'
import { Table } from '../../../shared/table/Table'
import { useParams } from 'react-router-dom'
import { useSelector } from 'react-redux'
import { convertKeyToStr, getDataFromLocalStorage, isSuccess, notify } from '../../../shared/helpers/util'
import { CustomModal } from '../../../shared/customModal/CustomModal'
import { getInputsApi, getUnitsApi } from '../../../logic/apis/projects'
import { PROJECT_TYPE } from '../../units/Units'
import DeletedModal from '../components/deletedModal/DeletedModal'
import { restoreUnit } from '../../../logic/apis/projects'
import { deleteUnit } from '../../../logic/apis/projects'
import { permDeleteUnit } from '../../../logic/apis/projects'
import { SettingsCont } from '../../units/style'
import { StorageKey } from '../../../shared/helpers/constants'

const DeletedUnits = () => {
  const columns = [
    {
      Header: 'Name',
      accessor: 'name',
    },
    {
      Header: 'Symbol',
      accessor: 'symbol',
    },
  ]

  const [deletedUnits, setDeletedUnits] = useState<any>([])
  const [deletedInputs, setDeletedInputs] = useState<any>(null)
  const [inputData, setInputData] = useState<any>()
  const [deletedModal, setDeletedModal] = useState(false)

  const fetchIdRef = useRef(0)
  const loadmoreRef = useRef(null)
  const [loading, setLoading] = useState<boolean>(false)
  const [data, setData] = useState<any[]>([])
  const [detailsUpdate, setDetailsUpdate] = useState(false)

  const onRestore = async () => {
    try {
      const res = await restoreUnit({
        id: inputData?._id ?? '',
      })
      if (isSuccess(res)) {
        notify(`Restored Unit!`, 'success')
        setDeletedModal(false)
        setInputData(null)
        fetchData({
          pageIndex: 0,
          pageSize: 20,
        })
      } else throw new Error(res?.data?.message)
    } catch (err) {
      notify('Failed to restore Unit!', 'error')
      console.log('Unit err', err)
    }
  }

  const onDelete = async () => {
    try {
      const res = await permDeleteUnit({
        id: inputData?._id ?? '',
      })
      if (isSuccess(res)) {
        notify(`Deleted Unit!`, 'success')
        setDeletedModal(false)
        setInputData(null)
        fetchData({
          pageIndex: 0,
          pageSize: 20,
        })
      } else throw new Error(res?.data?.message)
    } catch (err) {
      notify('Failed to delete Unit!', 'error')
      console.log('Unit create err', err)
    }
  }

  const fetchData = useCallback(
    async ({ pageSize, pageIndex }: any) => {
      try {
        // This will get called when the table needs new data
        setLoading(true)
        let receivedData: any = []
        const currentCompany: any = getDataFromLocalStorage('currentCompany')

        const clientResponse = await getUnitsApi({ deleted: true, limit: pageSize })

        if (isSuccess(clientResponse)) {
          let statusRes = clientResponse?.data?.data?.unit
          const tableData = statusRes.reduce((prev: any, cur: any) => {
            return [
              ...prev,
              {
                ...cur,
                name: cur.name,
                symbol: cur.symbol,
              },
            ]
          }, [])

          receivedData.push(...tableData)
        } else {
          notify(clientResponse?.data?.message, 'error')
        }

        // Give this fetch an ID
        const fetchId = ++fetchIdRef.current

        // Set the loading state
        // setLoading(true)

        // We'll even set a delay to simulate a server here
        // setTimeout(() => {
        // Only update the data if this is the latest fetch
        if (fetchId === fetchIdRef.current) {
          const startRow = pageSize * pageIndex
          const endRow = startRow + pageSize
          setData([...receivedData.slice(startRow, endRow)])

          // Your server could send back total page count.
          // For now we'll just fake it, too
          // setPageCount(Math.ceil(receivedData.length / pageSize))

          // setLoading(false)
        }
        // }, 1000)
      } catch (error) {
        console.error('TeamTable fetchData error', error)
      } finally {
        setLoading(false)
      }
    },
    [detailsUpdate]
  )

  return (
    <>
      <Table
        columns={columns}
        data={data}
        // pageCount={1}
        fetchData={fetchData}
        // client={true}
        noOverflow
        noSearch
        onRowClick={(data) => {
          setInputData(data)
          setDeletedModal(true)
        }}
        loading={loading}
        ref={loadmoreRef}
        isLoadMoreLoading={loading}
      />
      <CustomModal show={deletedModal}>
        <DeletedModal
          onDelete={onDelete}
          onRestore={onRestore}
          onClose={() => {
            setDeletedModal(false)
            setInputData(null)
          }}
          inputData={inputData}
          title="Deleted Unit"
        />
      </CustomModal>
    </>
  )
}

export default DeletedUnits
