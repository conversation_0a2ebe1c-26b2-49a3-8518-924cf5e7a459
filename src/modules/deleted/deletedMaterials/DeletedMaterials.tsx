import React, { useCallback, useEffect, useRef, useState } from 'react'
import * as SharedStyled from '../../../styles/styled'
import { Table } from '../../../shared/table/Table'
import {
  getCatergoryApi,
  getMaterialsApi,
  getSubCatergoryApi,
  getUnitsApi,
  permDeleteMaterials,
  restoreMaterials,
} from '../../../logic/apis/projects'
import { useParams } from 'react-router-dom'
import { useSelector } from 'react-redux'
import { getDataFromLocalStorage, isSuccess, notify } from '../../../shared/helpers/util'
import { CustomModal } from '../../../shared/customModal/CustomModal'
import CategoryModal from '../../category/components/categoryModal/CategoryModal'
import SubcategoryModal from '../../category/components/subcategoryModal/SubcategoryModal'
import DeletedModal from '../components/deletedModal/DeletedModal'
import { vendorDrop } from '../../materials/components/materialsModal/MaterialsModal'
import { StorageKey } from '../../../shared/helpers/constants'

const DeletedMaterials = () => {
  const columns = [
    {
      Header: 'Category',
      accessor: 'category',
    },
    {
      Header: 'Subcategory',
      accessor: 'subcategory',
    },
    {
      Header: 'Material Name',
      accessor: 'materialName',
    },
    {
      Header: 'Unit',
      accessor: 'unit',
    },
    {
      Header: 'Cost/Unit',
      accessor: 'cost',
    },
    {
      Header: 'Vendor',
      accessor: 'vendor',
    },
    {
      Header: 'Inventory',
      accessor: 'inventory',
    },
  ]

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany } = globalSelector.company

  const [materials, setMaterials] = useState<any>([])
  const [deletedModal, setDeletedModal] = useState(false)

  // modal data
  const [inputData, setInputData] = useState<any>(null)
  const [catData, setCatData] = useState<any>([])
  const [subCatData, setSubCatData] = useState<any>([])
  const [unitData, setUnitData] = useState<any>([])

  const fetchIdRef = useRef(0)
  // const [pageCount, setPageCount] = useState<number>(10)
  const [data, setData] = useState<any[]>([])
  const [detailsUpdate, setDetailsUpdate] = useState(false)

  const loadmoreRef = useRef(null)
  const [loading, setLoading] = useState<boolean>(false)

  useEffect(() => {
    initFetch()
  }, [])

  const fetchData = useCallback(
    async ({ pageSize, pageIndex }: any) => {
      try {
        // This will get called when the table needs new data
        setLoading(true)
        let receivedData: any = []
        const currentCompany: any = getDataFromLocalStorage('currentCompany')

        const clientResponse = await getMaterialsApi({ deleted: true, limit: pageSize })

        if (isSuccess(clientResponse)) {
          const { material } = clientResponse?.data?.data

          material.forEach((item: any) => {
            receivedData.push({
              ...item,
              category: getObjFromId(catData, item.categoryId).name,
              subcategory: getObjFromId(subCatData, item.subCategoryId).name,
              materialName: item.name,
              unit: getObjFromId(unitData, item.unitId).name,
              vendor: vendorDrop[item.vendor - 1],
              inventory: item.inv ? 'Yes' : 'No',
            })
          })

          receivedData.push(...receivedData)
        } else {
          notify(clientResponse?.data?.message, 'error')
        }

        // Give this fetch an ID
        const fetchId = ++fetchIdRef.current

        // Set the loading state
        // setLoading(true)

        // We'll even set a delay to simulate a server here
        // setTimeout(() => {
        // Only update the data if this is the latest fetch
        if (fetchId === fetchIdRef.current) {
          const startRow = pageSize * pageIndex
          const endRow = startRow + pageSize
          setData(receivedData.slice(startRow, endRow))

          // Your server could send back total page count.
          // For now we'll just fake it, too
          // setPageCount(Math.ceil(receivedData.length / pageSize))

          // setLoading(false)
        }
        // }, 1000)
      } catch (error) {
        console.error('TeamTable fetchData error', error)
      } finally {
        setLoading(false)
      }
    },
    [detailsUpdate, catData.length, subCatData.length, unitData.length]
  )

  useEffect(() => {
    if (catData.length && subCatData.length && unitData.length) {
      fetchData({
        pageIndex: 0,
        pageSize: 20,
      })
    }
  }, [catData, subCatData, unitData])

  const initFetch = async () => {
    fetchCategories()
    fetchSubCategories()
    fetchUnits()
  }

  const fetchCategories = async () => {
    try {
      const res = await getCatergoryApi({ deleted: false })
      if (isSuccess(res)) {
        setCatData(res.data.data.category)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('init fetch failed!', err)
    }
  }

  const fetchSubCategories = async () => {
    try {
      const res = await getSubCatergoryApi({ deleted: false })
      if (isSuccess(res)) {
        setSubCatData(res.data.data.subCategory)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('init fetch failed!', err)
    }
  }

  const fetchUnits = async () => {
    try {
      const res = await getUnitsApi({ deleted: false })
      if (isSuccess(res)) {
        setUnitData(res.data.data.unit)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('init fetch failed!', err)
    }
  }

  // const fetchMaterials = async () => {
  //   try {
  //     const res = await getMaterialsApi({ companyId: currentCompany._id, deleted: true })
  //     if (isSuccess(res)) {
  //       console.log({ initFetch: res })
  //       const { material } = res.data.data
  //       let tableData: any = []
  //       material.forEach((item: any) => {
  //         tableData.push({
  //           ...item,
  //           category: getObjFromId(catData, item.categoryId).name,
  //           subcategory: getObjFromId(subCatData, item.subCategoryId).name,
  //           materialName: item.name,
  //           unit: getObjFromId(unitData, item.unitId).name,
  //           vendor: vendorDrop[item.vendor - 1],
  //           inventory: item.inv ? 'Yes' : 'No',
  //         })
  //       })
  //       setMaterials(tableData)
  //     } else throw new Error(res?.data?.message)
  //   } catch (err) {
  //     console.log('init fetch failed!', err)
  //   }
  // }

  const getObjFromId = (obj: any[], id: string) => {
    let [res] = obj.filter((item: any) => {
      return item._id === id
    })

    return res ?? { name: '' }
  }

  const onRestore = async () => {
    try {
      const res = await restoreMaterials({
        id: inputData?._id ?? '',
      })
      if (isSuccess(res)) {
        notify(`Restored Material!`, 'success')
        setDeletedModal(false)
        setInputData(null)
        fetchData({
          pageIndex: 0,
          pageSize: 20,
        })
      } else throw new Error(res?.data?.message)
    } catch (err) {
      notify('Failed to restore Material!', 'error')
      console.log('Material err', err)
    }
  }

  const onDelete = async () => {
    try {
      const res = await permDeleteMaterials({
        id: inputData?._id ?? '',
      })
      if (isSuccess(res)) {
        notify(`Deleted Material!`, 'success')
        setDeletedModal(false)
        setInputData(null)
        fetchData({
          pageIndex: 0,
          pageSize: 20,
        })
      } else throw new Error(res?.data?.message)
    } catch (err) {
      notify('Failed to delete Material!', 'error')
      console.log('Material err', err)
    }
  }

  return (
    <>
      {catData.length > 0 && subCatData.length > 0 && unitData.length > 0 ? (
        <Table
          columns={columns}
          data={data}
          fetchData={fetchData}
          // client={true}
          noBorder
          loading={loading}
          noOverflow
          noSearch
          onRowClick={(data) => {
            setInputData(data)
            setDeletedModal(true)
          }}
          ref={loadmoreRef}
          isLoadMoreLoading={loading}
        />
      ) : null}
      <CustomModal show={deletedModal}>
        <DeletedModal
          onDelete={onDelete}
          onRestore={onRestore}
          onClose={() => {
            setDeletedModal(false)
            setInputData(null)
          }}
          inputData={inputData}
          title="Deleted Material"
        />
      </CustomModal>
    </>
  )
}

export default DeletedMaterials
