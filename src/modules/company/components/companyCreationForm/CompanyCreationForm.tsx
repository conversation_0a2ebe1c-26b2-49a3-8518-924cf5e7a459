import { Form, Formik } from 'formik'
import { useEffect, useRef, useState } from 'react'
import { useDispatch } from 'react-redux'
import { useParams } from 'react-router-dom'
import * as Yup from 'yup'

import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import UnitSvg from '../../../../assets/newIcons/unitModal.svg'
import { createCompany, getAllCompanies } from '../../../../logic/apis/company'
import { setCompanies, setCurrentCompany } from '../../../../logic/redux/actions/company'
import { setShowCompanyCreationModal as setShowModal } from '../../../../logic/redux/actions/ui'
import { getDataFromLocalStorage, isSuccess, notify } from '../../../../shared/helpers/util'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import * as SharedStyled from '../../../../styles/styled'
import * as Styled from './style'
import Button from '../../../../shared/components/button/Button'
import { StorageKey } from '../../../../shared/helpers/constants'

/**
 * InitialValues is an interface declared here so that it can be used as a type for the useState hook
 */
interface InitialValues {
  companyName: string
  description: string
}

/**
 * I_CompanyCreationForm is an interface which defines the props type which we receive from our parent component to this component
 */
interface I_CompanyCreationForm {
  setShowCompanyCreationModal?: React.Dispatch<React.SetStateAction<boolean>>
  isGlobal?: boolean
}

/**
 *
 * @returns A CompanyCreationForm component with all the validations to its input fields
 */
export const CompanyCreationForm = (props: I_CompanyCreationForm) => {
  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */
  const [initialValues, setInitialValues] = useState<InitialValues>({
    companyName: '',
    description: '',
  })

  /**
   * loading will be the loading state when performing the operations
   */
  const [loading, setLoading] = useState<boolean>(false)

  const dispatch = useDispatch()

  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [inputRef])
  /**
   * Destructuring the values from the props received
   */
  const { setShowCompanyCreationModal, isGlobal } = props

  /**
   * CompanyCreationFormSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */
  const CompanyCreationFormSchema = Yup.object().shape({
    companyName: Yup.string().required('No company name provided.'),
    description: Yup.string().required('No description provided.'),
  })

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async (submittedValues: InitialValues, { resetForm }: any) => {
    try {
      setLoading(true)
      const response = await createCompany({ ...submittedValues })
      const allCompanyResponse = await getAllCompanies()
      if (isSuccess(response)) {
        const allCompanies = allCompanyResponse?.data?.data?.companiesData
        dispatch(setCurrentCompany(allCompanies[0].company))
        localStorage.setItem('currentCompany', JSON.stringify(allCompanies[0].company))
        dispatch(setCompanies(allCompanies))
        setLoading(false)
        notify('Company created successfully', 'success')

        setShowCompanyCreationModal?.(false)
        resetForm()
        if (isGlobal) {
          dispatch(setShowModal(false))
        }
      } else {
        notify(response?.data?.message, 'error')
        setLoading(false)
      }
    } catch (error) {
      console.error('Company Creation error', error)
      setLoading(false)
    }
  }

  return (
    <Styled.CompanyCreationFormContainer>
      <Formik
        initialValues={initialValues}
        onSubmit={handleSubmit}
        validationSchema={CompanyCreationFormSchema}
        validateOnChange={true}
        validateOnBlur={false}
      >
        {/* <Styled.ResetpasswordContainer> */}
        {({ touched, errors, resetForm }) => {
          return (
            <>
              <Styled.ModalHeaderContainer>
                <SharedStyled.FlexRow>
                  <img src={UnitSvg} alt="modal icon" />
                  <SharedStyled.FlexCol>
                    <Styled.ModalHeader>Create Company</Styled.ModalHeader>
                  </SharedStyled.FlexCol>
                </SharedStyled.FlexRow>

                <Styled.CrossContainer
                  onClick={() => {
                    if (isGlobal) {
                      dispatch(setShowModal(false))
                    }
                    setShowCompanyCreationModal?.(false)
                    setLoading(false)
                    resetForm()
                  }}
                >
                  <CrossIcon />
                </Styled.CrossContainer>
              </Styled.ModalHeaderContainer>
              <SharedStyled.SettingModalContentContainer>
                <Form className="form">
                  <SharedStyled.Content
                    maxWidth="706px"
                    gap="10px"
                    width="100%"
                    disableBoxShadow={true}
                    noPadding={true}
                  >
                    <InputWithValidation
                      labelName="Company Name"
                      stateName="companyName"
                      error={touched.companyName && errors.companyName ? true : false}
                      passRef={inputRef}
                    />
                    <InputWithValidation
                      labelName="Description"
                      stateName="description"
                      error={touched.description && errors.description ? true : false}
                    />
                    <SharedStyled.ButtonContainer marginTop="20px">
                      <Button type="submit" isLoading={loading}>
                        Create Company
                      </Button>
                    </SharedStyled.ButtonContainer>
                  </SharedStyled.Content>
                </Form>
              </SharedStyled.SettingModalContentContainer>
            </>
          )
        }}
        {/* </Styled.ResetpasswordContainer> */}
      </Formik>
    </Styled.CompanyCreationFormContainer>
  )
}
