import styled from 'styled-components'
import { colors, screenSizes } from '../../styles/theme'
import { Nue } from '../../shared/helpers/constants'

export const CreateCompanyOuterGridContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  place-items: start;
  max-width: 1280px;
  width: 100%;
  @media (min-width: 2000px) {
    margin: 0 auto;
  }

  .company-info {
    margin-top: 10px;
    width: max-content;
    span {
      font-family: ${Nue.medium};
    }
  }

  img {
    border-radius: 50%;
    border: 1px solid #9747ff;
    width: 80px;
    height: 80px;
    padding: 4px;
  }
  .capitalize {
    text-transform: capitalize;
  }
`

export const CompanyContainer = styled.div`
  width: 100%;
`

interface I_CompanyCard {
  height: string
}

export const CompanyCard = styled.div<any>`
  height: ${(props) => props.height};
  border-radius: 5px;
  box-shadow: 0 4px 8px 0 ${colors.lightBlack1};
  transition: 0.3s;
  border: ${(props) => (props.selected ? `2px solid ${colors.darkGrey}` : '')};
  /* max-width: 300px;
  width: 100%; */
  margin-top: 30px;
  padding: 15px;
  display: flex;
  gap: 10px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  :hover {
    /* transform: scale(1.03); */
    box-shadow: 0 4px 8px 0 ${colors.lightBlack1};
    .add {
      svg {
        animation: rotation 0.4s linear;
      }
    }
    .desc {
      text-decoration: underline;
    }
  }
  @keyframes rotation {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(180deg);
    }
  }
`

export const CardTitle = styled.p`
  color: ${colors.darkGrey};
  font-size: 20px;
  font-weight: 300;
  margin: 0;
  @media (min-width: ${screenSizes.M}px) {
    font-size: 30px;
  }
`

export const ViewDescription = styled.p`
  font-size: 16px;
  color: ${colors.darkGrey};
`

export const CardImg = styled.div`
  svg {
    width: 100px;
    height: 100px;
  }
  @media (min-width: ${screenSizes.M}px) {
    svg {
      width: 130px;
      height: 130px;
    }
  }
`

export const CardIcon = styled.div`
  svg {
    width: 40px;
    height: 40px;
  }
  svg path {
    stroke: ${colors.darkGrey};
  }
  @media (min-width: ${screenSizes.M}px) {
    svg {
      width: 45px;
      height: 45px;
    }
  }
`

export const SearchBarContainer = styled.div`
  margin-top: 30px;
`
export const Label = styled.p`
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: ${colors.darkGrey};
`

export const SearchInput = styled.input`
  padding: 12px 16px;
  font-size: 16px;
  max-width: 320px;
  width: 100%;
  border: none;
  outline: none;
  border: 0.5px outset ${colors.grey3};
  border-right: none;
`

export const SearchButton = styled.button`
  height: 48px;
  background: ${colors.darkGrey};
  border: none;
  border: 0.5px outset ${colors.grey3};
  border-left: none;
  cursor: pointer;
  svg {
    width: 16px;
    height: 16px;
    stroke: ${colors.white};
  }
  :hover {
    svg {
      transform: scale(1.2);
      transition: 0.3s ease-in-out;
    }
  }
`
export const Description = styled.p`
  margin: 0;
  font-size: 16px;
  font-weight: 300;
  span {
    /* font-family: NunitoSansBold; */
    font-weight: 500;
  }
`
