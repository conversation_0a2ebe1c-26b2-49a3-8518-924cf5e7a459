import { useState } from 'react'
import { useSelector } from 'react-redux'
import { useParams } from 'react-router-dom'

import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import { deleteMarketingChannel, restoreMarketingChannel } from '../../../../logic/apis/marketingChannel'
import { getDataFromLocalStorage, isSuccess, notify } from '../../../../shared/helpers/util'
import * as SharedStyled from '../../../../styles/styled'
import { colors } from '../../../../styles/theme'
import * as Styled from './style'
import { ModalHeaderInfo } from '../../../units/components/newUnitModal/style'
import UnitSvg from '../../../../assets/newIcons/unitModal.svg'
import Button from '../../../../shared/components/button/Button'
import { StorageKey } from '../../../../shared/helpers/constants'

interface I_ConfirmationPopUp {
  setShowConfirmationPopUp: React.Dispatch<React.SetStateAction<boolean>>
  setDetailsUpdate: React.Dispatch<React.SetStateAction<boolean>>
  header: string
  marketingChannelData: any
}

const ChannelConfirmationPopUp = (props: I_ConfirmationPopUp) => {
  const { setShowConfirmationPopUp, setDetailsUpdate, header, marketingChannelData } = props

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany } = globalSelector.company

  /**
   * loading will be the loading state when performing the operations
   */
  const [loading, setLoading] = useState<boolean>(false)

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async () => {
    try {
      // if (Object.keys(currentCompany).length > 0) {
      setLoading(true)

      let dataObj = {
        id: marketingChannelData.id,
      }

      if (header === 'Delete Marketing Channel') {
        let response = await deleteMarketingChannel(dataObj)

        if (isSuccess(response)) {
          notify('Marketing Channel Deleted Successfully', 'success')
          setDetailsUpdate((prev) => !prev)
          setLoading(false)
          setShowConfirmationPopUp(false)
        } else {
          setLoading(false)
          notify(response?.data?.message, 'error')
        }
      } else {
        let response = await restoreMarketingChannel(dataObj)

        if (isSuccess(response)) {
          notify('Marketing Channel Restored Successfully', 'success')
          setDetailsUpdate((prev) => !prev)
          setLoading(false)
          setShowConfirmationPopUp(false)
        } else {
          setLoading(false)
          notify(response?.data?.message, 'error')
        }
      }
      // } else {
      //   notify('Integration is pending', 'warning')
      // }
    } catch (error) {
      console.error('Delete Marketing Channel handleSubmit', error)
      setLoading(false)
    }
  }

  return (
    <Styled.ConfirmationContainer>
      {' '}
      <Styled.ModalHeaderContainer>
        <SharedStyled.FlexRow>
          <img src={UnitSvg} alt="modal icon" />
          <SharedStyled.FlexCol>
            <Styled.ModalHeader>{header}</Styled.ModalHeader>
          </SharedStyled.FlexCol>
        </SharedStyled.FlexRow>

        <Styled.CrossContainer onClick={() => setShowConfirmationPopUp(false)}>
          <CrossIcon />
        </Styled.CrossContainer>
      </Styled.ModalHeaderContainer>
      <Styled.ModalBodyContainer>
        <Styled.ModalDescription>
          {header === 'Delete Marketing Channel'
            ? 'Are you sure you want to delete this Channel?'
            : 'Are you sure you want to restore this Channel?'}
        </Styled.ModalDescription>
        <SharedStyled.FlexBox width="100%" alignItems="center" justifyContent="space-around" marginTop="20px" gap="5px">
          <Button
            type="submit"
            bgColor={header === 'Delete Marketing Channel' ? colors.error : colors.blueLight}
            color={colors.white}
            maxWidth="200px"
            onClick={() => handleSubmit()}
            isLoading={loading}
          >
            Yes
          </Button>
          <Button type="submit" onClick={() => setShowConfirmationPopUp(false)} maxWidth="200px">
            No
          </Button>
        </SharedStyled.FlexBox>
      </Styled.ModalBodyContainer>
    </Styled.ConfirmationContainer>
  )
}

export default ChannelConfirmationPopUp
