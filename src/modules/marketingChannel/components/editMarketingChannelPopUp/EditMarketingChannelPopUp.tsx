import { Form, Formik } from 'formik'
import { useEffect, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useParams } from 'react-router-dom'
import * as Yup from 'yup'

import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import { updateMarketingChannel } from '../../../../logic/apis/marketingChannel'
import { getDataFromLocalStorage, isSuccess, notify } from '../../../../shared/helpers/util'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import * as SharedStyled from '../../../../styles/styled'
import * as Styled from './style'
import UnitSvg from '../../../../assets/newIcons/unitModal.svg'
import { ModalHeaderInfo } from '../../../units/components/newUnitModal/style'
import Button from '../../../../shared/components/button/Button'
import { StorageKey } from '../../../../shared/helpers/constants'

/**
 * InitialValues is an interface declared here so that it can be used as a type for the useState hook
 */
interface InitialValues {
  marketingChannelName: string
  description: string
  order?: number
}

/**
 * I_EditMarketingChannelPopUp is an interface which defines the props type which we receive from our parent component to this component
 */
interface I_EditMarketingChannelPopUp {
  setShowEditMarketingChannelPopUp: React.Dispatch<React.SetStateAction<boolean>>
  setDetailsUpdate: React.Dispatch<React.SetStateAction<boolean>>
  marketingChannelData: any
  onDeleteClick: () => void
}

export const EditMarketingChannelPopUp = (props: I_EditMarketingChannelPopUp) => {
  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */
  const [initialValues, setInitialValues] = useState<InitialValues>({
    marketingChannelName: '',
    description: '',
  })

  /**
   * loading will be the loading state when performing the operations
   */
  const [loading, setLoading] = useState<boolean>(false)

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company

  /**
   * Destructuring the values from the props received
   */
  const { setShowEditMarketingChannelPopUp, setDetailsUpdate, marketingChannelData, onDeleteClick } = props

  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [inputRef])
  /**
   * MarketingChannelEditFormSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */
  const MarketingChannelEditFormSchema = Yup.object().shape({
    marketingChannelName: Yup.string().trim().required('No marketing channel name provided.'),
    description: Yup.string().trim().required('No description provided.'),
    order: Yup.number().positive('Order must be greater than 0.'),
  })

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async (submittedValues: InitialValues, { resetForm }: any) => {
    try {
      // if (Object.keys(currentCompany).length > 0 && Object.keys(currentMember).length > 0) {
      setLoading(true)
      let dataObj = {
        marketingChannelId: marketingChannelData.id,
        name: submittedValues.marketingChannelName,
        description: submittedValues.description,
        createdBy: currentMember._id,
        order: submittedValues.order ? Number(submittedValues.order) : undefined,
      }

      let response = await updateMarketingChannel(dataObj)
      if (isSuccess(response)) {
        notify('Marketing Channel Edited Successfully', 'success')
        resetForm()
        setDetailsUpdate((prev) => !prev)
        setLoading(false)
        setShowEditMarketingChannelPopUp(false)
      } else {
        setLoading(false)
        notify(response?.data?.message, 'error')
      }
      // }
    } catch (error) {
      setLoading(false)

      console.error('Marketing Channel Creation error', error)
    }
  }

  const setInitialValuesFunc = () => {
    try {
      setInitialValues({
        ...initialValues,
        marketingChannelName: marketingChannelData.marketingChannelName,
        description: marketingChannelData.description,
        order: marketingChannelData.order,
      })
    } catch (error) {
      console.log('Edit department setInitialValues error', error)
    }
  }

  useEffect(() => {
    setInitialValuesFunc()
  }, [marketingChannelData])

  return (
    <Styled.EditMarketingChannelPopUpContainer>
      {' '}
      <Formik
        initialValues={initialValues}
        onSubmit={handleSubmit}
        validationSchema={MarketingChannelEditFormSchema}
        validateOnChange={true}
        validateOnBlur={false}
        enableReinitialize={true}
      >
        {/* <Styled.ResetpasswordContainer> */}
        {({ touched, errors, resetForm }) => {
          return (
            <>
              <Styled.ModalHeaderContainer>
                <SharedStyled.FlexRow>
                  <img src={UnitSvg} alt="modal icon" />
                  <SharedStyled.FlexCol>
                    <Styled.ModalHeader>Edit Marketing Channel</Styled.ModalHeader>
                  </SharedStyled.FlexCol>
                </SharedStyled.FlexRow>
                <Styled.CrossContainer
                  onClick={() => {
                    setShowEditMarketingChannelPopUp(false)
                    setLoading(false)
                    resetForm()
                  }}
                >
                  <CrossIcon />
                </Styled.CrossContainer>
              </Styled.ModalHeaderContainer>
              <SharedStyled.SettingModalContentContainer>
                <Form className="form">
                  <SharedStyled.Content maxWidth="706px" width="100%" disableBoxShadow={true} noPadding={true}>
                    <InputWithValidation
                      labelName="Marketing Channel Name"
                      stateName="marketingChannelName"
                      error={touched.marketingChannelName && errors.marketingChannelName ? true : false}
                      passRef={inputRef}
                    />
                    <InputWithValidation
                      labelName="Description"
                      stateName="description"
                      error={touched.description && errors.description ? true : false}
                    />

                    <InputWithValidation
                      forceType="number"
                      labelName="Order"
                      stateName="order"
                      error={touched.order && errors.order ? true : false}
                    />
                    <SharedStyled.ButtonContainer marginTop="26px">
                      <Button type="button" className="delete" onClick={onDeleteClick}>
                        Delete Channel
                      </Button>

                      <Button type="submit" isLoading={loading}>
                        Save Changes
                      </Button>
                    </SharedStyled.ButtonContainer>
                  </SharedStyled.Content>
                </Form>
              </SharedStyled.SettingModalContentContainer>
            </>
          )
        }}
      </Formik>
    </Styled.EditMarketingChannelPopUpContainer>
  )
}
