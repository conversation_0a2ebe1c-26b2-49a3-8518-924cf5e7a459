import { Form, Formik } from 'formik'
import { useEffect, useRef, useState } from 'react'
import { useParams } from 'react-router-dom'
import * as Yup from 'yup'

import UnitSvg from '../../../../assets/newIcons/unitModal.svg'
import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import { updatePassword } from '../../../../logic/apis/profile'
import { isPasswordValid } from '../../../../shared/helpers/regex'
import { getDataFromLocalStorage, isSuccess, notify } from '../../../../shared/helpers/util'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import * as SharedStyled from '../../../../styles/styled'
import * as Style from './style'
import { ModalHeaderInfo } from '../../../units/components/newUnitModal/style'
import { StorageKey } from '../../../../shared/helpers/constants'

/**
 * InitialValues is an interface declared here so that it can be used as a type for the useState hook
 */
interface InitialValues {
  oldPassword: string
  newPassword: string
  confirmNewPassword: string
}

/**
 * I_ChangePassword is an interface which defines the props type which we receive from our parent component to this component
 */
interface I_ChangePassword {
  setShowChangePasswordModal: React.Dispatch<React.SetStateAction<boolean>>
}

/**
 *
 * @returns A ChangePassword component with all the validations to its input fields
 */
export const ChangePassword = (props: I_ChangePassword) => {
  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */
  const [initialValues, setInitialValues] = useState<InitialValues>({
    oldPassword: '',
    newPassword: '',
    confirmNewPassword: '',
  })

  /**
   * loading will be the loading state when performing the operations
   */
  const [loading, setLoading] = useState<boolean>(false)

  /**
   * Destructuring the values from the props received
   */
  const { setShowChangePasswordModal } = props

  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [inputRef])
  /**
   * ChangePasswordSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */
  const ChangePasswordSchema = Yup.object().shape({
    oldPassword: Yup.string()
      .required('No password provided.')
      .matches(
        isPasswordValid,
        'Password should be minimum eight characters, at least one uppercase letter, one lowercase letter, one number and one special character'
      ),
    newPassword: Yup.string()
      .required('No password provided.')
      .matches(
        isPasswordValid,
        'Password should be minimum eight characters, at least one uppercase letter, one lowercase letter, one number and one special character'
      ),
    confirmNewPassword: Yup.string()
      .oneOf([Yup.ref('newPassword'), null], 'Passwords must match')
      .required('Required'),
  })

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async (submittedValues: InitialValues, { resetForm }: any) => {
    setLoading(true)
    try {
      const response = await updatePassword(submittedValues)
      if (isSuccess(response)) {
        notify('Updated Password Successfully', 'success')
        setInitialValues({
          oldPassword: '',
          newPassword: '',
          confirmNewPassword: '',
        })
        setShowChangePasswordModal(false)
        resetForm()
        setLoading(false)
      } else {
        notify(response?.data?.message, 'error')
        setLoading(false)
      }
    } catch (error) {
      console.error('ChangePassword handleSubmit', error)
      setLoading(false)
    }
  }

  return (
    <Style.ChangePasswordContainer>
      <Formik
        initialValues={initialValues}
        enableReinitialize={true}
        onSubmit={handleSubmit}
        validationSchema={ChangePasswordSchema}
        validateOnChange={true}
        validateOnBlur={false}
      >
        {/* <Styled.ResetpasswordContainer> */}
        {({ values, errors, touched, resetForm }) => {
          return (
            <>
              <Style.ModalHeaderContainer>
                <SharedStyled.FlexRow>
                  <img src={UnitSvg} alt="modal icon" />
                  <SharedStyled.FlexCol>
                    <Style.ModalHeader>Change Password</Style.ModalHeader>
                  </SharedStyled.FlexCol>
                </SharedStyled.FlexRow>
                <Style.CrossContainer
                  onClick={() => {
                    setShowChangePasswordModal(false)
                    resetForm()
                  }}
                >
                  <CrossIcon />
                </Style.CrossContainer>
              </Style.ModalHeaderContainer>
              <SharedStyled.SettingModalContentContainer>
                <Form className="form">
                  <SharedStyled.Content maxWidth="706px" width="100%" disableBoxShadow={true} noPadding={true}>
                    <InputWithValidation
                      type="password"
                      labelName="Old Password"
                      stateName="oldPassword"
                      value={values.oldPassword}
                      error={touched.oldPassword && errors.oldPassword ? true : false}
                      passRef={inputRef}
                    />
                    <InputWithValidation
                      type="password"
                      labelName="New Password"
                      stateName="newPassword"
                      value={values.newPassword}
                      error={touched.newPassword && errors.newPassword ? true : false}
                    />
                    <InputWithValidation
                      type="password"
                      labelName="Confirm New Password"
                      stateName="confirmNewPassword"
                      value={values.confirmNewPassword}
                      error={touched.confirmNewPassword && errors.confirmNewPassword ? true : false}
                    />
                    <SharedStyled.ButtonContainer marginTop="20px">
                      <SharedStyled.Button type="submit">
                        {loading ? (
                          <>
                            Changing Password
                            <SharedStyled.Loader />
                          </>
                        ) : (
                          'Change Password'
                        )}
                      </SharedStyled.Button>
                    </SharedStyled.ButtonContainer>
                  </SharedStyled.Content>
                </Form>
              </SharedStyled.SettingModalContentContainer>
            </>
          )
        }}
        {/* </Styled.ResetpasswordContainer> */}
      </Formik>
    </Style.ChangePasswordContainer>
  )
}
