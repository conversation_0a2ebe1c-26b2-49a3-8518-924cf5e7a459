import { Form, Formik } from 'formik'
import { useEffect, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { Link, useParams } from 'react-router-dom'
import * as Yup from 'yup'

import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import {
  getSubContractorById,
  removeMemberFromSubcontractor,
  retireOrDeleteSubcontractor,
  upsertSubcontractor,
} from '../../../../logic/apis/subcontractor'
import AutoCompleteAddress from '../../../../shared/autoCompleteAdress/AutoCompleteAddress'
import { onlyNumber, onlyText } from '../../../../shared/helpers/regex'
import {
  formatPhoneNumber,
  getDataFromLocalStorage,
  getDigitsFromPhone,
  isSuccess,
  notify,
} from '../../../../shared/helpers/util'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import { SharedPhone } from '../../../../shared/sharedPhone/SharedPhone'
import * as SharedStyled from '../../../../styles/styled'
import { colors } from '../../../../styles/theme'
import { ModalHeaderInfo } from '../../../units/components/newUnitModal/style'
import * as Styled from './style'
import UnitSvg from '../../../../assets/newIcons/unitModal.svg'
import Button from '../../../../shared/components/button/Button'
import '../../../../shared/helpers/yupExtension'
import { StorageKey } from '../../../../shared/helpers/constants'
import { Link as StyledLink } from '../../../crmSettings/style'
import { CustomModal } from '../../../../shared/customModal/CustomModal'
import { CompensationModal } from '../../../team/components/teamMember/components/CompensationModal/CompensationModal'
import { InvitationForm } from '../../../team/components/invitationForm/InvitationForm'
import Modal from '../../../../shared/customModal/Modal'
import { Title } from '../../../subscription/components/planCard/style'
import { Info } from '../../../../shared/components/profileInfo/style'
import { SLoader } from '../../../../shared/components/loader/Loader'
interface I_SubcontractorModal {
  setShowSubContractorModal: React.Dispatch<React.SetStateAction<boolean>>
  setShowRetireConfirmationModal: React.Dispatch<React.SetStateAction<boolean>>
  setShowDeleteConfirmationModal: React.Dispatch<React.SetStateAction<boolean>>
  setSubcontractorData: React.Dispatch<any>
  subcontractorData: any
  dataUpdate: boolean
  setDataUpdate: React.Dispatch<React.SetStateAction<boolean>>
  action: string
  setDetailsUpdate: React.Dispatch<React.SetStateAction<boolean>>
}

export const SubcontractorModal = (props: I_SubcontractorModal) => {
  const {
    setShowSubContractorModal,
    action,
    dataUpdate,
    setDataUpdate,
    setShowRetireConfirmationModal,
    setShowDeleteConfirmationModal,
    subcontractorData,
    setDetailsUpdate,
  } = props

  /**
   * InitialValues is an interface declared here so that it can be used as a type for the useState hook
   */
  interface InitialValues {
    name: string
    address: string
    city: string
    state: string
    zip: string
    agreementCompleted: boolean
    isActive: boolean
    mainContractorName: string
    phone: string
    email?: string
    retired: boolean
    deleted: boolean
    tearOff: Array<any>
    roofing: Array<any>
    miscellaneous: Array<any>
    _id?: string
  }

  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */
  const [initialValues, setInitialValues] = useState<InitialValues>({
    name: '',
    address: '',
    city: '',
    state: '',
    zip: '',
    mainContractorName: '',
    phone: '',
    email: '',
    agreementCompleted: false,
    isActive: false,
    retired: false,
    deleted: false,
    tearOff: [
      { name: '4/12-7/12', value: '', unit: '/SQ', codeName: '4/12-7/12' },
      { name: 'Cut ridge vent', value: '', unit: '/LF', codeName: 'cutRidgeVent' },
      { name: '8/12-9/12', value: '', unit: '/SQ', codeName: '8/12-9/12' },
      { name: 'Cut can vent', value: '', unit: '/EA', codeName: 'cutCanVent' },
      { name: '10/12-11/12', value: '', unit: '/SQ', codeName: '10/12-11/12' },
      { name: 'Install vent plug', value: '', unit: '/EA', codeName: 'installVentPlug' },
      { name: '12/12+', value: '', unit: '/SQ', codeName: '12/12+' },
      { name: 'Install vent baffles', value: '', unit: '/EA', codeName: 'installVentBaffles' },
      { name: `Add'l layers', value: '`, unit: '/SQ', codeName: 'addLayers' },
      // { name: 'Remove plywood', value: '', unit: '/EA', codeName: 'removePlywood' },
      { name: `Add'l felt layers`, value: '', unit: '/SQ', codeName: 'addFeltLayers' },
      { name: 'Install plywood', value: '', unit: '/EA', codeName: 'installPlywood' },
    ],
    roofing: [
      { name: '4/12-7/12', value: '', unit: '/SQ', codeName: '4/12-7/12' },
      { name: 'Chimney counter-flashing', value: '', unit: '/EA', codeName: 'chimneyCounterFlashing' },
      { name: `8/12-9/12`, value: '', unit: '/SQ', codeName: '8/12-9/12' },
      { name: 'R&R skylight', value: '', unit: '/EA', codeName: 'R&RSkylight' },
      { name: `10/12-11/12`, value: '', unit: '/SQ', codeName: '10/12-11/12' },
      { name: 'Install Windsor', value: '', unit: '/SQ', codeName: 'installWindsor' },
      { name: '12/12+', value: '', unit: '/SQ', codeName: '12/12+' },
      { name: `Eyebrow/cornice return`, value: '', unit: 'EA', codeName: 'eyebrow' },
      { name: 'Bay window/ cornice strip', value: '', unit: '/EA', codeName: 'bayWindow' },
    ],
    miscellaneous: [
      { name: '3+ stories', value: '', unit: '/SQ', codeName: 'stories' },
      { name: 'Remove fascia', value: '', unit: '/LF', codeName: 'removeFascia' },
      { name: 'Hand load roof', value: '', unit: '/SQ', codeName: 'handLoadRoof' },
      { name: 'Install fascia', value: '', unit: '/LF', codeName: 'installFascia' },
      { name: 'Travel: distance over', value: '', unit: '/MI', codeName: 'travelDistanceOver' },
      { name: 'Extra work', value: '', unit: '/HR', codeName: 'extraWork' },
      { name: 'Pay per job', value: '', unit: '', codeName: 'travelFeePerJob' },
      { name: 'Flawless Job Bonus', value: '', unit: '/SQ', codeName: 'flawLessJobBonus' },
    ],
  })

  const [cityDropdown, setCityDropdown] = useState<any>([])
  const [loading, setLoading] = useState<boolean>(false)
  const [loadingRetire, setLoadingRetire] = useState<boolean>(false)
  const [loadingDelete, setLoadingDelete] = useState<boolean>(false)
  const [header, setHeader] = useState('')
  const [lat, setLat] = useState('')
  const [long, setLong] = useState('')
  // const [distance, setDistance] = useState(0)

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember, companySettingForAll } = globalSelector.company

  const [selectedInviteId, setSelectedInviteId] = useState('')
  const [showInviteModal, setShowInviteModal] = useState<boolean>(false)
  const [showCompensationModal, setCompensationModal] = useState<boolean>(false)
  const [showConfirmModal, setShowConfirmModal] = useState(false)
  const [memberSubContractor, setMemberSubContractor] = useState([])
  const [selectedMember, setSelectedMember] = useState<any>({})
  const [deleteLoading, setDeleteLoading] = useState(false)
  const [bool, setBool] = useState(false)
  const [subLoading, setSubLoading] = useState(false)

  /**
   * SubcontractorModalSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */
  const SubcontractorModalSchema = Yup.object().shape({
    name: Yup.string()
      .min(2, 'Too Short!')
      .max(50, 'Too Long!')
      .required('Required')
      .matches(onlyText, 'Enter Valid Name'),
    address: Yup.string().required('Required'),
    city: Yup.string().required('Required'),
    zip: Yup.string().required('Required').matches(onlyNumber, 'Invalid'),
    mainContractorName: Yup.string()
      .min(2, 'Too Short!')
      .max(50, 'Too Long!')
      .required('Required')
      .matches(onlyText, 'Enter Valid Name'),
    phone: Yup.string().required('Required'),
    // .matches(onlyUsNumber, 'Phone number should be in this (************* or ************ format'),
    email: Yup.string().trimEmail().required('Required'),
  })

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async (submittedValues: InitialValues, { resetForm }: any) => {
    try {
      setLoading(true)
      let tearOffValue = {
        [`4/12-7/12`]: submittedValues.tearOff[0].value,
        [`cutRidgeVent`]: submittedValues.tearOff[1].value,
        [`8/12-9/12`]: submittedValues.tearOff[2].value,
        [`cutCanVent`]: submittedValues.tearOff[3].value,
        [`10/12-11/12`]: submittedValues.tearOff[4].value,
        [`installVentPlug`]: submittedValues.tearOff[5].value,
        [`12/12+`]: submittedValues.tearOff[6].value,
        [`installVentBaffles`]: submittedValues.tearOff[7].value,
        [`addLayers`]: submittedValues.tearOff[8].value,
        // [`removePlywood`]: submittedValues.tearOff[9].value,
        [`addFeltLayers`]: submittedValues.tearOff[9].value,
        [`installPlywood`]: submittedValues.tearOff[10].value,
      }

      let roofingValue = {
        [`4/12-7/12`]: submittedValues.roofing[0].value,
        [`chimneyCounterFlashing`]: submittedValues.roofing[1].value,
        [`8/12-9/12`]: submittedValues.roofing[2].value,
        [`R&RSkylight`]: submittedValues.roofing[3].value,
        [`10/12-11/12`]: submittedValues.roofing[4].value,
        [`installWindsor`]: submittedValues.roofing[5].value,
        [`12/12+`]: submittedValues.roofing[6].value,
        [`eyebrow`]: submittedValues.roofing[7].value,
        [`bayWindow`]: submittedValues.roofing[8].value,
      }

      let miscellaneousValue = {
        stories: submittedValues.miscellaneous[0].value,
        removeFascia: submittedValues.miscellaneous[1].value,
        handLoadRoof: submittedValues.miscellaneous[2].value,
        installFascia: submittedValues.miscellaneous[3].value,
        travelDistanceOver: submittedValues.miscellaneous[4].value,
        extraWork: submittedValues.miscellaneous[5].value,
        travelFeePerJob: submittedValues.miscellaneous[6].value,
        flawLessJobBonus: submittedValues.miscellaneous[7].value,
      }
      let phone = formatPhoneNumber(submittedValues.phone, '')
      let dataObj: any = {
        ...submittedValues,
        phone: submittedValues.phone,
        city: submittedValues.city,
        state: submittedValues.state,
        tearOff: tearOffValue,
        roofing: roofingValue,
        miscellaneous: miscellaneousValue,
      }
      if (action !== 'Edit Subcontractor') {
        const response = await upsertSubcontractor(dataObj)
        if (isSuccess(response)) {
          //  let resData = response?.data?.data?.pieceWorkSetting
          notify('Added Subcontractor successfully', 'success')
          setLoading(false)
          resetForm()
          setDataUpdate((prev) => !prev)
          setShowSubContractorModal(false)
        } else {
          notify(response?.data?.message, 'error')
          setLoading(false)
        }
      } else {
        dataObj = { ...dataObj, _id: subcontractorData.id, state: 'ST' }
        const response = await upsertSubcontractor(dataObj)
        if (isSuccess(response)) {
          //  let resData = response?.data?.data?.pieceWorkSetting
          notify('Edited Subcontractor successfully', 'success')
          setLoading(false)
          resetForm()
          setDataUpdate((prev) => !prev)
          setShowSubContractorModal(false)
        } else {
          notify(response?.data?.message, 'error')
          setLoading(false)
        }
      }
    } catch (error) {
      console.error('SubcontractorModal handleSubmit', error)
      setLoading(false)
    }
  }
  const getDetails = async () => {
    try {
      if (
        action === 'Edit Subcontractor' &&
        // Object.keys(currentCompany).length > 0 &&
        Object.keys(subcontractorData).length > 0
      ) {
        setSubLoading(true)
        const subResponse = await getSubContractorById(subcontractorData.id)
        if (isSuccess(subResponse)) {
          resetFormFunc()
          let statusRes = subResponse?.data?.data?.subcontractor
          let subTearOff = [...initialValues.tearOff]
          let subRoofing = [...initialValues.roofing]
          let subMiscellaneous = [...initialValues.miscellaneous]

          const customTearOffObj = statusRes.tearOff
          delete customTearOffObj['removePlywood']

          setMemberSubContractor(statusRes?.memberIds)

          Object.entries(customTearOffObj).forEach(([key, values]: any, index: number) => {
            subTearOff[index].value = values
          })
          Object.entries(statusRes.roofing).forEach(
            ([key, values]: any, index: number) => (subRoofing[index].value = values)
          )
          Object.entries(statusRes.miscellaneous).forEach(
            ([key, values]: any, index: number) => (subMiscellaneous[index].value = values)
          )
          let dataObj = {
            ...statusRes,
            tearOff: subTearOff,
            roofing: subRoofing,
            miscellaneous: subMiscellaneous,
          }
          delete dataObj.__v
          delete dataObj.updatedAt
          delete dataObj.createdAt
          dataObj.phone = getDigitsFromPhone(dataObj.phone)
          setInitialValues({ ...initialValues, ...dataObj })
          // setCityDropdown(cityStateData)
        } else {
          notify(subResponse?.data?.message, 'error')
        }
      } else {
        resetFormFunc()
      }
    } catch (error) {
      console.error('getDetails error', error)
    } finally {
      setSubLoading(false)
    }
  }

  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [inputRef])

  const resetFormFunc = () => {
    try {
      setInitialValues({
        name: '',
        address: '',
        city: '',
        state: '',
        zip: '',
        mainContractorName: '',
        phone: '',
        email: '',
        agreementCompleted: false,
        isActive: false,
        retired: false,
        deleted: false,
        tearOff: [
          { name: '4/12-7/12', value: '', unit: '/SQ', codeName: '4/12-7/12' },
          { name: 'Cut ridge vent', value: '', unit: '/LF', codeName: 'cutRidgeVent' },
          { name: '8/12-9/12', value: '', unit: '/SQ', codeName: '8/12-9/12' },
          { name: 'Cut can vent', value: '', unit: '/EA', codeName: 'cutCanVent' },
          { name: '10/12-11/12', value: '', unit: '/SQ', codeName: '10/12-11/12' },
          { name: 'Install vent plug', value: '', unit: '/EA', codeName: 'installVentPlug' },
          { name: '12/12+', value: '', unit: '/SQ', codeName: '12/12+' },
          { name: 'Install vent baffles', value: '', unit: '/EA', codeName: 'installVentBaffles' },
          { name: `Add'l layers', value: '`, unit: '/SQ', codeName: 'addLayers' },
          // { name: 'Remove plywood', value: '', unit: '/EA', codeName: 'removePlywood' },
          { name: `Add'l felt layers`, value: '', unit: '/SQ', codeName: 'addFeltLayers' },
          { name: 'Install plywood', value: '', unit: '/EA', codeName: 'installPlywood' },
        ],
        roofing: [
          { name: '4/12-7/12', value: '', unit: '/SQ', codeName: '4/12-7/12' },
          { name: 'Chimney counter-flashing', value: '', unit: '/EA', codeName: 'chimneyCounterFlashing' },
          { name: `8/12-9/12`, value: '', unit: '/SQ', codeName: '8/12-9/12' },
          { name: 'R&R skylight', value: '', unit: '/EA', codeName: 'R&RSkylight' },
          { name: `10/12-11/12`, value: '', unit: '/SQ', codeName: '10/12-11/12' },
          { name: 'Install Windsor', value: '', unit: '/SQ', codeName: 'installWindsor' },
          { name: '12/12+', value: '', unit: '/SQ', codeName: '12/12+' },
          { name: `Eyebrow/cornice return`, value: '', unit: 'EA', codeName: 'eyebrow' },
          { name: 'Bay window/ cornice strip', value: '', unit: '/EA', codeName: 'bayWindow' },
        ],
        miscellaneous: [
          { name: '3+ stories', value: '', unit: '/SQ', codeName: 'stories' },
          { name: 'Remove fascia', value: '', unit: '/LF', codeName: 'removeFascia' },
          { name: 'Hand load roof', value: '', unit: '/SQ', codeName: 'handLoadRoof' },
          { name: 'Install fascia', value: '', unit: '/LF', codeName: 'installFascia' },
          { name: 'Travel: distance over', value: '', unit: '/MI', codeName: 'travelDistanceOver' },
          { name: 'Extra work', value: '', unit: '/HR', codeName: 'extraWork' },
          { name: 'Pay per job', value: '', unit: '', codeName: 'travelFeePerJob' },
          { name: 'Flawless Job Bonus', value: '', unit: '/SQ', codeName: 'flawLessJobBonus' },
        ],
      })
    } catch (error) {
      console.error('resetFormFunc error', error)
    }
  }

  const handleSubmitRetireOrDelete = async () => {
    try {
      // if (Object.keys(currentCompany).length > 0) {
      let dataObj = {
        subcontractorId: subcontractorData.id,
      }
      if (header === 'Delete Subcontractor') {
        let response = await retireOrDeleteSubcontractor({ ...dataObj, deleted: true, retired: false })

        if (isSuccess(response)) {
          notify('Subcontractor Deleted Successfully', 'success')
          setShowSubContractorModal(false)
          setDetailsUpdate((prev) => !prev)
          setLoadingDelete(false)
        } else {
          setLoadingDelete(false)
          setShowSubContractorModal(false)
          notify(response?.data?.message, 'error')
        }
      } else if (header === 'Retire Subcontractor') {
        let response = await retireOrDeleteSubcontractor({ ...dataObj, deleted: false, retired: true })
        if (isSuccess(response)) {
          notify('Subcontractor Retired Successfully', 'success')
          setLoadingRetire(false)
          setDetailsUpdate((prev) => !prev)
          setShowSubContractorModal(false)
        } else {
          setLoadingRetire(false)
          setShowSubContractorModal(false)
          notify(response?.data?.message, 'error')
        }
      }
      // }
    } catch (error) {
      console.error('Delete Department handleSubmit', error)
      setLoading(false)
    }
  }

  useEffect(() => {
    handleSubmitRetireOrDelete()
  }, [header])

  useEffect(() => {
    getDetails()
  }, [subcontractorData, bool])

  const isEditSubContractor = action === 'Edit Subcontractor'

  const handleDeleteMember = async () => {
    setDeleteLoading(true)
    try {
      const res = await removeMemberFromSubcontractor(selectedMember?.memberId, initialValues?._id!)
      if (isSuccess(res)) {
        notify(res?.data?.data?.message, 'success')
        setBool((p) => !p)
        setShowConfirmModal(false)
      }
    } catch (error) {
      console.error('Delete subcontractor error=====>', error)
    } finally {
      setDeleteLoading(false)
    }
  }

  return (
    <>
      <Styled.SubcontractModalContainer className={showCompensationModal || showInviteModal ? 'no-overflow' : ''}>
        <Formik
          initialValues={initialValues}
          enableReinitialize={true}
          onSubmit={handleSubmit}
          validationSchema={SubcontractorModalSchema}
          validateOnChange={true}
          validateOnBlur={false}
        >
          {({ values, errors, touched, resetForm, setFieldValue, handleChange }) => {
            return (
              <>
                <Styled.ModalHeaderContainer>
                  <SharedStyled.FlexRow>
                    <img src={UnitSvg} alt="modal icon" />
                    <SharedStyled.FlexCol>
                      <Styled.ModalHeader>{action}</Styled.ModalHeader>
                    </SharedStyled.FlexCol>
                  </SharedStyled.FlexRow>
                  <Styled.CrossContainer
                    onClick={() => {
                      resetForm()
                      setShowSubContractorModal(false)
                    }}
                  >
                    <CrossIcon />
                  </Styled.CrossContainer>
                </Styled.ModalHeaderContainer>
                <SharedStyled.SettingModalContentContainer>
                  {subLoading && isEditSubContractor ? (
                    <SharedStyled.FlexCol gap="10px">
                      <SLoader height={30} width={100} isPercent />
                      <SLoader height={30} width={100} isPercent />

                      <div
                        style={{
                          width: '100%',
                          visibility: isEditSubContractor ? 'hidden' : 'visible',
                          height: '1px',
                        }}
                      >
                        <AutoCompleteAddress
                          setFieldValue={setFieldValue}
                          street={'address'}
                          city={'city'}
                          state={'state'}
                          zip={'zip'}
                          sourceAddress={companySettingForAll?.address}
                          companyLatLong={companySettingForAll}
                          setLat={setLat}
                          setLong={setLong}
                          // setDistance={setDistance}
                        />
                      </div>
                      <SLoader height={30} width={100} isPercent />
                      <SLoader height={30} width={100} isPercent />
                    </SharedStyled.FlexCol>
                  ) : (
                    <Form className="form">
                      <SharedStyled.Content maxWidth="706px" width="100%" disableBoxShadow={true} noPadding={true}>
                        {isEditSubContractor ? (
                          <SharedStyled.FlexCol>
                            <SharedStyled.FlexRow width="100%" justifyContent="space-between">
                              {memberSubContractor?.length ? <h4>Members:</h4> : <div></div>}

                              <SharedStyled.Button
                                type="button"
                                maxWidth="130px"
                                maxHeight="30px"
                                onClick={() => {
                                  setShowInviteModal(true)
                                }}
                              >
                                Invite member
                              </SharedStyled.Button>
                            </SharedStyled.FlexRow>
                            {memberSubContractor?.length ? (
                              <SharedStyled.FlexCol margin="16px 0" gap="10px">
                                {memberSubContractor?.map((sub: any) => (
                                  <SharedStyled.FlexRow justifyContent="space-between" key={sub?._id}>
                                    <Link to={`/team/member/${sub?.user}/${sub?._id}/0`} target="_blank">
                                      <StyledLink>{sub?.name}</StyledLink>
                                    </Link>
                                    <p>{sub?.email}</p>
                                    <p
                                      style={{ cursor: 'pointer', color: 'red', fontWeight: 700 }}
                                      onClick={() => {
                                        setSelectedMember({
                                          name: sub?.name,
                                          memberId: sub?._id,
                                        })
                                        setShowConfirmModal(true)
                                      }}
                                    >
                                      &#x2715;
                                    </p>
                                  </SharedStyled.FlexRow>
                                ))}
                              </SharedStyled.FlexCol>
                            ) : null}
                          </SharedStyled.FlexCol>
                        ) : null}

                        <SharedStyled.FlexBox
                          width="100%"
                          alignItems="center"
                          gap="5px"
                          marginTop="6px"
                          justifyContent="space-between"
                        >
                          <SharedStyled.FlexBox
                            width="100%"
                            alignItems="center"
                            gap="5px"
                            marginTop="6px"
                            justifyContent="flex-start"
                          >
                            <Styled.CheckBox width="15px" height="20px" type="checkbox" name="isActive" />
                            <Styled.CheckBoxDescription>Active</Styled.CheckBoxDescription>
                          </SharedStyled.FlexBox>
                          {action === 'Edit Subcontractor' && (
                            <SharedStyled.FlexBox
                              width="100%"
                              alignItems="center"
                              gap="5px"
                              marginTop="6px"
                              justifyContent="flex-end"
                            >
                              <SharedStyled.Button
                                type="button"
                                // bgColor={colors.yellow}
                                maxWidth="100px"
                                maxHeight="30px"
                                onClick={() => {
                                  setHeader('Retire Subcontractor')
                                  setLoadingRetire(true)
                                }}
                                bgColor={loadingRetire ? colors.lightGrey : colors.yellow}
                              >
                                {loadingRetire ? (
                                  <>
                                    Retire
                                    <SharedStyled.Loader />
                                  </>
                                ) : (
                                  'Retire'
                                )}
                              </SharedStyled.Button>
                              <SharedStyled.Button
                                type="button"
                                // bgColor={colors.errorRed}
                                maxWidth="100px"
                                maxHeight="30px"
                                onClick={() => {
                                  setHeader('Delete Subcontractor')
                                  setLoadingDelete(true)
                                }}
                                bgColor={loadingDelete ? colors.lightGrey : colors.errorRed}
                              >
                                {loadingDelete ? (
                                  <>
                                    Delete
                                    <SharedStyled.Loader />
                                  </>
                                ) : (
                                  'Delete'
                                )}
                              </SharedStyled.Button>
                            </SharedStyled.FlexBox>
                          )}
                        </SharedStyled.FlexBox>

                        <InputWithValidation
                          labelName="Contractor Name"
                          stateName="name"
                          error={touched.name && errors.name ? true : false}
                          passRef={inputRef}
                        />

                        <div style={{ width: '100%' }}>
                          <AutoCompleteAddress
                            setFieldValue={setFieldValue}
                            street={'address'}
                            city={'city'}
                            state={'state'}
                            zip={'zip'}
                            sourceAddress={companySettingForAll?.address}
                            companyLatLong={companySettingForAll}
                            setLat={setLat}
                            setLong={setLong}
                            // setDistance={setDistance}
                          />
                        </div>
                        <InputWithValidation
                          labelName="Street Address"
                          stateName="address"
                          error={touched.address && errors.address ? true : false}
                        />

                        <InputWithValidation
                          labelName="City"
                          stateName="city"
                          error={touched.city && errors.city ? true : false}
                          twoInput={true}
                        />

                        <InputWithValidation
                          labelName="State"
                          stateName="state"
                          error={touched.state && errors.state ? true : false}
                          twoInput={true}
                          // disabled={true}
                        />

                        <InputWithValidation
                          labelName="Zip"
                          stateName="zip"
                          error={touched.zip && errors.zip ? true : false}
                        />
                        <InputWithValidation
                          labelName="Main contact name"
                          stateName="mainContractorName"
                          error={touched.mainContractorName && errors.mainContractorName ? true : false}
                        />

                        <SharedStyled.TwoInputDiv>
                          <SharedPhone
                            labelName="Phone"
                            stateName="phone"
                            twoInput={true}
                            value={values.phone}
                            onChange={handleChange('phone')}
                            error={touched.phone && errors.phone ? true : false}
                          />
                          <InputWithValidation
                            labelName="Email"
                            stateName="email"
                            twoInput={true}
                            error={touched.email && errors.email ? true : false}
                          />
                        </SharedStyled.TwoInputDiv>
                        <SharedStyled.FlexBox
                          width="100%"
                          alignItems="center"
                          gap="5px"
                          marginTop="6px"
                          justifyContent="flex-start"
                        >
                          <Styled.CheckBox width="15px" height="20px" type="checkbox" name="agreementCompleted" />
                          <Styled.CheckBoxDescription>Agreement Completed & Signed</Styled.CheckBoxDescription>
                        </SharedStyled.FlexBox>
                        <Styled.HeaderDiv marginTop="12px" textAlign="left">
                          Pay Rates
                        </Styled.HeaderDiv>
                        <Styled.SubHeaderDiv marginTop="12px" textAlign="left">
                          Tear Off
                        </Styled.SubHeaderDiv>
                        <Styled.PayRatesContainer marginTop="10px">
                          {values.tearOff.map((item: any, index: number) => (
                            <Styled.NameValueUnitContainer key={index}>
                              <Styled.NameDiv>{item.name}:</Styled.NameDiv>
                              <SharedStyled.FlexBox alignItems="center">
                                <Styled.ValueInput type="number" name={`tearOff.${index}.value`} />
                                <Styled.UnitDiv>{item.unit}</Styled.UnitDiv>
                              </SharedStyled.FlexBox>
                            </Styled.NameValueUnitContainer>
                          ))}
                        </Styled.PayRatesContainer>
                        <Styled.SubHeaderDiv marginTop="12px" textAlign="left">
                          Roofing
                        </Styled.SubHeaderDiv>
                        <Styled.PayRatesContainer marginTop="10px">
                          {values.roofing.map((item: any, index: number) => (
                            <Styled.NameValueUnitContainer key={index}>
                              <Styled.NameDiv>{item.name}:</Styled.NameDiv>
                              <SharedStyled.FlexBox alignItems="center">
                                <Styled.ValueInput type="number" name={`roofing.${index}.value`} />
                                <Styled.UnitDiv>{item.unit}</Styled.UnitDiv>
                              </SharedStyled.FlexBox>
                            </Styled.NameValueUnitContainer>
                          ))}
                        </Styled.PayRatesContainer>
                        <Styled.SubHeaderDiv marginTop="12px" textAlign="left">
                          Miscellaneous
                        </Styled.SubHeaderDiv>
                        <Styled.PayRatesContainer marginTop="10px">
                          {values.miscellaneous.map((item: any, index: number) => (
                            <Styled.NameValueUnitContainer key={index}>
                              <Styled.NameDiv>{item.name}:</Styled.NameDiv>
                              <SharedStyled.FlexBox alignItems="center">
                                <Styled.ValueInput
                                  type="number"
                                  name={`miscellaneous.${index}.value`}
                                  maxWidth={!item.unit ? '94px' : ''}
                                  borderRadius={!item.unit ? '4px' : ''}
                                />
                                <Styled.UnitDiv display={!item.unit ? 'none' : ''}>{item.unit}</Styled.UnitDiv>
                              </SharedStyled.FlexBox>
                            </Styled.NameValueUnitContainer>
                          ))}
                        </Styled.PayRatesContainer>
                        <Styled.KeyValue marginTop="8px" textAlign="left">
                          Dump fees: reimbursed w/ receipt
                        </Styled.KeyValue>
                        <Styled.KeyValue marginTop="8px" textAlign="left">
                          Extra materials: reimbursed w/ receipt
                        </Styled.KeyValue>
                        <SharedStyled.ButtonContainer marginTop="26px">
                          <Button type="submit" isLoading={loading}>
                            {action !== 'Edit Subcontractor' ? 'Add' : 'Edit'}
                          </Button>
                          <Button type="button" onClick={() => setShowSubContractorModal(false)}>
                            Close
                          </Button>
                        </SharedStyled.ButtonContainer>
                      </SharedStyled.Content>
                    </Form>
                  )}
                </SharedStyled.SettingModalContentContainer>
              </>
            )
          }}
        </Formik>
      </Styled.SubcontractModalContainer>
      <CustomModal show={showInviteModal}>
        <InvitationForm
          setShowCompanyCreationModal={setShowInviteModal}
          setDataUpdate={setDataUpdate}
          onInviteSuccess={(id) => {
            setSelectedInviteId(id)
            setShowInviteModal(false)
            setCompensationModal(true)
          }}
        />
      </CustomModal>

      <CustomModal show={showCompensationModal}>
        <CompensationModal
          setCompensationModal={setCompensationModal}
          noData={false}
          setDataUpdated={setDataUpdate}
          isEdit={false}
          compensationData={{}}
          hireDateCompensation={''}
          isInviteModal
          isSubcontractorPositionProp
          subcontractorName={initialValues?.name}
          selectedInviteId={selectedInviteId}
        />
      </CustomModal>

      <CustomModal show={showConfirmModal}>
        <Modal
          title="Remove subcontractor"
          onClose={() => {
            setShowConfirmModal(false)
          }}
        >
          <SharedStyled.FlexCol alignItems="center">
            <p style={{ fontSize: '16px', lineHeight: '26px' }}>
              Are you sure you want to remove <b>{selectedMember?.name}</b> from the Subcontractor{' '}
              <b>{initialValues?.name}</b>?
            </p>

            <SharedStyled.FlexRow margin="16px 0 0 0">
              <Button className="delete" onClick={handleDeleteMember} isLoading={deleteLoading}>
                Yes
              </Button>
              <Button
                onClick={() => {
                  setShowConfirmModal(false)
                }}
                disabled={deleteLoading}
              >
                No
              </Button>
            </SharedStyled.FlexRow>
          </SharedStyled.FlexCol>
        </Modal>
      </CustomModal>
    </>
  )
}
