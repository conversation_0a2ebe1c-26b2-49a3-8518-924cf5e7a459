import * as Styled from './style'
import * as SharedStyled from '../../../../styles/styled'
import { colors } from '../../../../styles/theme'
import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import { useState } from 'react'
import { getDataFromLocalStorage, isSuccess, notify } from '../../../../shared/helpers/util'
import { deleteDepartment, restoreDepartment } from '../../../../logic/apis/department'
import { useSelector } from 'react-redux'
import { useParams } from 'react-router-dom'
import { restoreSubcontractor, retireOrDeleteSubcontractor } from '../../../../logic/apis/subcontractor'
import { StorageKey } from '../../../../shared/helpers/constants'

interface I_ConfirmationPopUp {
  setShowConfirmationPopUp: React.Dispatch<React.SetStateAction<boolean>>
  setShowEditSubContractorModal?: React.Dispatch<React.SetStateAction<boolean>>
  setDetailsUpdate: React.Dispatch<React.SetStateAction<boolean>>
  header: string
  subcontractorData: any
}

export const ConfirmationPopUp = (props: I_ConfirmationPopUp) => {
  const { setShowConfirmationPopUp, setDetailsUpdate, header, subcontractorData, setShowEditSubContractorModal } = props

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany } = globalSelector.company

  /**
   * loading will be the loading state when performing the operations
   */
  const [loading, setLoading] = useState<boolean>(false)

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async () => {
    try {
      // if (Object.keys(currentCompany).length > 0) {
      setLoading(true)

      let dataObj = {
        subcontractorId: subcontractorData.id,
      }

      if (header === 'Delete Subcontractor') {
        let response = await retireOrDeleteSubcontractor({ ...dataObj, deleted: true, retired: false })

        if (isSuccess(response)) {
          notify('Subcontractor Deleted Successfully', 'success')
          setDetailsUpdate((prev) => !prev)
          setLoading(false)
          setShowEditSubContractorModal && setShowEditSubContractorModal(false)
          setShowConfirmationPopUp(false)
        } else {
          setLoading(false)
          notify(response?.data?.message, 'error')
        }
      } else if (header === 'Retire Subcontractor') {
        let response = await retireOrDeleteSubcontractor({ ...dataObj, deleted: false, retired: true })
        if (isSuccess(response)) {
          notify('Subcontractor Retired Successfully', 'success')
          setDetailsUpdate((prev) => !prev)
          setLoading(false)
          setShowEditSubContractorModal && setShowEditSubContractorModal(false)
          setShowConfirmationPopUp(false)
        } else {
          setLoading(false)
          notify(response?.data?.message, 'error')
        }
      } else {
        let response = await restoreSubcontractor(dataObj)
        if (isSuccess(response)) {
          notify('Subcontractor Restored Successfully', 'success')
          setDetailsUpdate((prev) => !prev)
          setLoading(false)
          setShowConfirmationPopUp(false)
        } else {
          setLoading(false)
          notify(response?.data?.message, 'error')
        }
      }
      // }
    } catch (error) {
      console.error('Delete Department handleSubmit', error)
      setLoading(false)
    }
  }

  return (
    <Styled.ConfirmationContainer>
      {' '}
      <Styled.ModalHeaderContainer>
        <Styled.ModalHeader>{header}</Styled.ModalHeader>
        <Styled.CrossContainer onClick={() => setShowConfirmationPopUp(false)}>
          <CrossIcon />
        </Styled.CrossContainer>
      </Styled.ModalHeaderContainer>
      <Styled.ModalBodyContainer>
        <Styled.ModalDescription>
          {header === 'Delete Subcontractor'
            ? 'Are you sure you want to delete this Subcontractor ?'
            : header === 'Retire Subcontractor'
            ? 'Are you sure you want to retire this Subcontractor ?'
            : 'Are you sure you want to restore this Subcontractor ?'}
        </Styled.ModalDescription>
        <SharedStyled.FlexBox width="100%" alignItems="center" justifyContent="space-around" marginTop="20px" gap="5px">
          <SharedStyled.Button
            type="submit"
            bgColor={
              header === 'Delete Subcontractor' || header === 'Retire Subcontractor' ? colors.error : colors.blueLight
            }
            color={colors.white}
            maxWidth="200px"
            onClick={() => handleSubmit()}
          >
            {loading ? (
              <>
                {header === 'Delete Subcontractor'
                  ? 'Deleting..'
                  : header === 'Retire Subcontractor'
                  ? 'Retiring..'
                  : 'Restoring..'}
                <SharedStyled.Loader />
              </>
            ) : (
              'Yes'
            )}
          </SharedStyled.Button>
          <SharedStyled.Button type="submit" onClick={() => setShowConfirmationPopUp(false)} maxWidth="200px">
            No
          </SharedStyled.Button>
        </SharedStyled.FlexBox>
      </Styled.ModalBodyContainer>
    </Styled.ConfirmationContainer>
  )
}
