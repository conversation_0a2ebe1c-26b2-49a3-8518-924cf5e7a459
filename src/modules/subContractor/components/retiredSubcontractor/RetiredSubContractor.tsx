import { useCallback, useMemo, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate, useParams } from 'react-router-dom'

import { RevokeIcon } from '../../../../assets/icons/RevokeIcon'
import { getSubContractors, restoreSubcontractor } from '../../../../logic/apis/subcontractor'
import ProfileInfo from '../../../../shared/components/profileInfo/ProfileInfo'
import { getDataFromLocalStorage, isSuccess, notify } from '../../../../shared/helpers/util'
import { Table } from '../../../../shared/table/Table'
import * as SharedStyled from '../../../../styles/styled'
import * as Styled from './style'
import { StorageKey } from '../../../../shared/helpers/constants'

interface I_Data {
  name: string
  contact: string
  phone: string
  email: string
}

const RetiredSubContractor = () => {
  const [loading, setLoading] = useState<boolean>(false)

  const [showConfirmationPopUp, setShowConfirmationPopUp] = useState<boolean>(false)

  const [detailsUpdate, setDetailsUpdate] = useState(false)
  const [subcontractorData, setSubcontractorData] = useState<any>({})
  const [pageCount, setPageCount] = useState<number>(10)
  const [data, setData] = useState<I_Data[]>([])
  const fetchIdRef = useRef(0)
  const loadmoreRef = useRef(null)

  const navigate = useNavigate()

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany } = globalSelector.company

  const fetchData = useCallback(
    async ({ pageSize, pageIndex, search }: any) => {
      try {
        setLoading(true)
        let receivedData: any = []
        let currentCompanyData: any = localStorage.getItem('currentCompany')

        const statusResponse = await getSubContractors({
          skip: pageIndex,
          limit: pageSize,
          deleted: false,
          search,
          retired: true,
        })

        if (isSuccess(statusResponse)) {
          let statusRes = statusResponse?.data?.data?.subcontractor

          statusRes.forEach((res: any) => {
            receivedData.push({
              name: res?.name,
              contact: res?.mainContractorName ? res?.mainContractorName : '-',
              phone: res?.phone,
              email: res?.email,
              action: (
                <>
                  <SharedStyled.TooltipContainer width={'100px'}>
                    <span className="tooltip-content">Restore</span>
                    <SharedStyled.FlexBox width="100%" alignItems="center" gap="10px">
                      <Styled.IconContainer
                        content="Revoke"
                        className="restore"
                        onClick={() => {
                          hadleRestoreSub(res?._id)
                        }}
                      >
                        <RevokeIcon />
                      </Styled.IconContainer>
                    </SharedStyled.FlexBox>
                  </SharedStyled.TooltipContainer>
                </>
              ),
            })
          })
        } else {
          notify(statusResponse?.data?.message, 'error')
        }
        // Give this fetch an ID
        const fetchId = ++fetchIdRef.current

        // Set the loading state

        // We'll even set a delay to simulate a server here
        // setTimeout(() => {
        // Only update the data if this is the latest fetch
        if (fetchId === fetchIdRef.current) {
          const startRow = pageSize * pageIndex
          const endRow = startRow + pageSize
          setData(receivedData.slice(startRow, endRow))

          // Your server could send back total page count.
          // For now we'll just fake it, too
          setPageCount(Math.ceil(receivedData.length / pageSize))
          setLoading(false)
        }
        // }, 1000)
      } catch (error) {
        console.error('TeamTable fetchData error', error)
      }
    },
    [detailsUpdate]
  )

  const hadleRestoreSub = async (_id: string) => {
    try {
      // if (Object.keys(currentCompany).length > 0) {
      let dataObj = {
        subcontractorId: _id,
      }
      let response = await restoreSubcontractor(dataObj)
      if (isSuccess(response)) {
        notify('Subcontractor Restored Successfully', 'success')
        setDetailsUpdate((prev) => !prev)
        setLoading(false)
        setShowConfirmationPopUp(false)
      } else {
        setLoading(false)
        notify(response?.data?.message, 'error')
      }
      // }
    } catch (error) {
      console.log(error)
    }
  }

  const columns: any = useMemo(
    () => [
      {
        Header: 'Name',
        accessor: 'name',
        Cell: (props: any) => <ProfileInfo data={props?.row?.original} showImagePlaceholder />,
      },
      {
        Header: 'MAIN CONTRACTOR',
        accessor: 'contact',
      },
      {
        Header: 'Phone #',
        accessor: 'phone',
      },
      {
        Header: 'Action',
        accessor: 'action',
      },
    ],
    []
  )

  return (
    <>
      <Table
        columns={columns}
        data={data}
        loading={loading}
        pageCount={pageCount}
        fetchData={fetchData}
        noLink={true}
        ref={loadmoreRef}
        isLoadMoreLoading={loading}
      />

      {/* <CustomModal show={showConfirmationPopUp}>
        <ConfirmationPopUp
          setShowConfirmationPopUp={setShowConfirmationPopUp}
          setDetailsUpdate={setDetailsUpdate}
          header="Restore Subcontractor"
          subcontractorData={subcontractorData}
        />
      </CustomModal> */}
    </>
  )
}

export default RetiredSubContractor
