import styled from 'styled-components'
import { colors } from '../../styles/theme'
import { SettingsCont } from '../units/style'
import { ActionColors, Nue } from '../../shared/helpers/constants'
import { FlexCol } from '../../styles/styled'

export const FilterContainer = styled(FlexCol)`
  max-height: 500px;
  height: auto;
  overflow-y: auto;
  label {
    color: ${colors.black};
  }
`

export const DropDownOuterContainer = styled.div`
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
`
export const DropDownContainer = styled.div<any>`
  position: relative;
  width: 100%;
`

export const DropDownContentContainer = styled.div<{ visibility?: boolean; height?: string }>`
  display: ${(props) => (props.visibility ? 'block' : 'none')};
  position: absolute;
  border: 1px solid ${colors.darkGrey};
  width: 100%;
  max-height: ${(props) => (props.height ? props.height : 'fit-content')};
  top: 39px;
  left: 0px;
  border-radius: 8px;
  overflow-y: auto;
  background: ${colors.white};
  z-index: 12;

  &.height {
    max-height: 400px;
  }
`
export const DropDownItem = styled.div<any>`
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  width: 100%;
  background: ${(props) => (props.active ? `${colors.darkGrey}` : `${colors.white}`)};
  color: ${(props) => (props.active ? `${colors.white}` : `${colors.darkGrey}`)};
  :hover {
    background: ${(props) => (props.noHover ? '' : `${colors.darkGrey}`)};
    color: ${(props) => (props.noHover ? '' : ` ${colors.white}`)};
  }
`
export const ListItem = styled.li<any>`
  border-radius: 6px;
  list-style: none;
  padding: 10px;
  /* background: ${(props) => (props.projectTypeHexColor ? props.projectTypeHexColor : 'white')}; */
  background: ${(props) => (props.projectTypeHexColor ? props.projectTypeHexColor : 'white')};
  cursor: pointer;
  margin-right: 10px;
  margin-left: 10px;
  position: relative;
  min-height: 47px;

  border: 4px solid ${(props) => ActionColors[props.borderColor] || 'transparent'};

  box-shadow: 0 0 8px 2px ${(props) => ActionColors[props.borderColor] || 'transparent'};

  ::before {
    content: '';
    position: absolute;
    /* top: 0;
    left: 0; */
    /* -------------------changes------------------- */
    top: -2px;
    left: 2px;
    z-index: 1;
    border-radius: 25px;
    /* -------------------changes------------------- */

    width: ${(props) =>
      props.percent
        ? `${Math.min(props.percent, 100)}%`
        : '0%'}; /* Adjust this value to set the length of the border */
    height: 2px; /* Set the height of the border */
    background-color: green; /* Set the color of the border */
  }

  ${(props) =>
    props.percent >= 0 &&
    `
    ::after {
      content: '';
      position: absolute;
      top: -5px; /* Adjust this value to vertically position the circle */
      left: calc(${Math.min(props.percent, 100)}% - 2px); /* Adjust this value to horizontally position the circle */
      width: 8px; /* Diameter of the circle */
      height: 8px; /* Diameter of the circle */
      background-color: green;
      border-radius: 50%; /* Make the element a circle */
      z-index: 1; /* Ensure the circle appears above the progress line */
    }
  `}

  :hover {
    background: rgba(3, 3, 3, 0.1);
  }

  h3 {
    font-size: 12px;
    font-family: ${Nue.regular} !important;
    text-transform: capitalize;
  }

  p {
    color: ${colors.gray1};
    font-family: ${Nue.medium};
    font-size: 12px;
    line-height: 16px;
    margin-top: 4px;
    margin-bottom: 20px;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 200px;
    overflow-x: hidden;
  }

  input {
    border: none;
    width: 80px;
    background: transparent;
  }
`

export const SalesContainer = styled(SettingsCont)`
  display: grid;
  grid-template-columns: 1fr;
  place-items: start;
  /* max-width: 1280px; */
  width: 100%;
  overflow: hidden;
  @media (min-width: 2000px) {
    margin: 0 auto;
  }
  gap: 6px;

  max-height: calc(100vh - 88px);
  margin-top: -10px;
  margin-bottom: -24px;
`

export const OldCompletedCont = styled(SettingsCont)`
  .filter {
    margin-left: auto;
  }
`
export const SearchBarContainer = styled.div`
  width: 100%;
  display: flex;
  align-items: center;

  &.search-loader {
    position: relative;

    span {
      border: 3px solid #03a9f4;
      border-bottom-color: #75757550;
      left: unset;
      right: 2%;
    }
  }
`

export const SearchInput = styled.input`
  padding: 8px 16px;
  font-size: 16px;
  /* max-width: 320px; */
  width: 100%;
  border: none;
  outline: none;

  border-radius: 8px;
  border: 1px solid ${colors.lightGray};
  background: ${colors.lightGray};
`

export const SearchButton = styled.button`
  height: 40px;
  width: 48px;
  background: ${colors.darkGrey};
  border: none;
  border: 0.5px outset ${colors.grey3};
  border-left: none;
  cursor: pointer;
  svg {
    width: 16px;
    height: 16px;
    stroke: ${colors.white};
  }
  :hover {
    svg {
      transform: scale(1.2);
      transition: 0.3s ease-in-out;
    }
  }
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
`

export const BoardContainer = styled.div<any>`
  /* max-width: 1280px; */
  width: 100%;
  margin-top: ${(props) => props.marginTop};
  height: 100%;
  overflow-x: auto;

  @media (min-width: 2000px) {
    margin: 0 auto;
  }
`

export const StagesBoard = styled.div`
  min-width: 250px;
  padding: 13px 0px 10px 0px;
  border-radius: 6px;
  height: auto;
  background: ${colors.lightGray1};
  margin: 0 10px 10px 10px;

  max-height: calc(100vh - 200px);
  padding-top: 0;
  overflow-y: scroll;
  .heading {
    font-size: 14px;
    font-weight: bold;
    line-height: 40px;
    margin: 0;
    padding-left: 10px;

    position: sticky;
    top: 0;
    z-index: 8;
    background: #f6f6f6;

    font-family: ${Nue.bold};
  }
  .list-container {
    min-height: calc(100vh - 225px);
    overflow-y: auto;
    padding: 0px;
    margin: 0;
    /* margin-left: 10px; */

    display: flex;
    flex-direction: column;
    gap: 14px;

    padding-top: 14px;
    gap: 14px;
  }
  .list-item {
    border-radius: 6px;
    list-style: none;
    padding: 10px;
    /* background: white; */

    cursor: pointer;
    margin-right: 10px;
    :hover {
      background: rgba(3, 3, 3, 0.1);
    }
  }

  &:first-child {
    margin: 0 10px 0 0;
  }

  h3 {
    font-size: 12px;
    font-family: ${Nue.medium};
    text-transform: capitalize;
  }

  p {
    color: ${colors.gray1};
    font-family: ${Nue.medium};
    font-size: 12px;
    line-height: 16px;
    margin-top: 4px;
    margin-bottom: 20px;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 200px;
    overflow-x: hidden;
  }

  &.loading {
    padding: 10px;
  }
`

export const AddressWrap = styled.div`
  p {
    font-family: ${Nue.regular};
    font-size: 12px;
  }

  &.font {
    display: flex;
    flex-direction: column;
    gap: 2px;
    margin-top: 4px;
    p {
      font-family: ${Nue.regular};
      font-size: 16px;
    }
  }
`
