import { Form, Formik } from 'formik'
import { useCallback, useEffect, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate, useParams } from 'react-router-dom'
import * as Yup from 'yup'
import { getProfileInfo } from '../../logic/apis/profile'
import { getOptionApi, getPackageApi, getProjectTypes, getUnitsApi } from '../../logic/apis/projects'
import { CustomModal } from '../../shared/customModal/CustomModal'
import { Dropdown } from '../../shared/dropDown/Dropdown'
import { StorageKey, TIME_ZONES } from '../../shared/helpers/constants'
import { onlyText } from '../../shared/helpers/regex'
import { getDataFromLocalStorage, getNameFromId, isSuccess, notify } from '../../shared/helpers/util'
import { InputWithValidation } from '../../shared/inputWithValidation/InputWithValidation'
import { Table } from '../../shared/table/Table'
import * as SharedStyled from '../../styles/styled'
import { IType } from '../newProject/NewProject'
import * as Styled from './style'
import TabBar from '../../shared/components/tabBar/TabBar'
import { SettingsCont, ButtonCont } from '../units/style'
import Button from '../../shared/components/button/Button'
import OptionsModal from './optionsModal/OptionsModal'
import DeletedOptions from './deletedOptions/DeletedOptions'
import '../../shared/helpers/yupExtension'
import DropdownWithCheckboxesWithoutFormik from '../../shared/dropdownWithCheckboxesWithoutFormik/DropdownWithCheckboxesWithoutFormik'

/**
 * InitialValues is an interface declared here so that it can be used as a type for the useState hook
 */
interface InitialValues {
  firstName: string
  lastName: string
  preferredName: string
  email: string
  roles: string
}

const OptionsSettings = () => {
  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */
  const [showCreateOptions, setShowCreateOptions] = useState<boolean>(false)
  const [optionType, setOptionType] = useState('')
  const [types, setTypes] = useState<IType[]>([])
  const [packages, setPackages] = useState<any[]>([])
  const [selectedOptions, setSelectedOptions] = useState<any[]>([])
  const globalSelector = useSelector((state: any) => state)
  const { currentCompany } = globalSelector.company
  const [initialValues, setInitialValues] = useState<InitialValues>({
    firstName: '',
    lastName: '',
    preferredName: '',
    email: '',
    roles: '',
  })

  const [units, setUnits] = useState([])
  const [editOptionVals, setEditOptionVals] = useState<any>(null)
  const [loading, setLoading] = useState<boolean>(false)
  const loadmoreRef = useRef(null)
  const [timeZ, setTimeZ] = useState<string>(TIME_ZONES[0])
  const fetchIdRef = useRef(0)
  const [data, setData] = useState<any[]>([])
  const [dataDefaultTrue, setDataDefaultTrue] = useState<any>({})
  const [detailsUpdate, setDetailsUpdate] = useState(false)

  const navigate = useNavigate()

  /**
   * ProfileSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */
  const ProfileSchema = Yup.object().shape({
    firstName: Yup.string()
      .min(2, 'Too Short!')
      .max(50, 'Too Long!')
      .required('Required')
      .matches(onlyText, 'Enter Valid Name'),
    lastName: Yup.string()
      .min(2, 'Too Short!')
      .max(50, 'Too Long!')
      .required('Required')
      .matches(onlyText, 'Enter Valid Name'),
    preferredName: Yup.string().min(2, 'Too Short!').max(50, 'Too Long!').matches(onlyText, 'Enter Valid Name'),
    // username: Yup.string().min(2, 'Too Short!').max(50, 'Too Long!').required('Required'),
    email: Yup.string().trimEmail().email('Invalid email').required('Required'),
    roles: Yup.string(),
  })

  const inputColumns = [
    {
      Header: 'Option Name',
      accessor: 'name',
    },
    {
      Header: 'Order',
      accessor: 'order',
    },
    // {
    //   Header: 'Default',
    //   accessor: 'default',
    // },
    {
      Header: 'Project Type',
      accessor: 'type',
      Cell: ({ value }: { value: string }) => getNameFromId(value, types),
    },
    {
      Header: 'Group',
      accessor: 'group',
    },
  ]

  useEffect(() => {
    initFetch()
    fetchUnitsData()
    fetchpackages()
  }, [])

  const fetchpackages = async () => {
    try {
      const res = await getPackageApi({ deleted: false, limit: 1000 })
      if (isSuccess(res)) {
        const { packageDetail } = res?.data?.data
        setPackages(packageDetail)
      }
    } catch (error) {
      console.log(error)
    }
  }
  const fetchUnitsData = async () => {
    try {
      const res = await getUnitsApi({ deleted: false })
      if (isSuccess(res)) {
        const { unit } = res.data.data
        setUnits(unit)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('Failed init fetch', err)
    }
  }

  const initFetch = async () => {
    try {
      const res = await getProjectTypes({ deleted: false })
      if (isSuccess(res)) {
        const { projectType } = res.data.data
        const object = projectType.map(({ _id, name, groups }: { _id: string; name: string; groups: string[] }) => ({
          name: name,
          id: _id,
          value: _id,
          label: name,
          groups: groups,
        }))
        setTypes(object)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('Failed init fetch', err)
    }
  }

  // const handleSubmit = async (submittedValues: InitialValues) => {
  //   setLoading(true)
  //   try {
  //     let data: any = { ...submittedValues }
  //     delete data.roles
  //     const response = await updateProfile(id, data)
  //     if (isSuccess(response)) {
  //       notify('Updated Profile Successfully', 'success')
  //       setLoading(false)
  //     } else {
  //       notify(response?.data?.message, 'error')
  //       setLoading(false)
  //     }
  //   } catch (error) {
  //     console.error('Profile handleSubmit', error)
  //     setLoading(false)
  //   }
  // }

  const fetchData = useCallback(
    async ({ pageSize, pageIndex }: any) => {
      try {
        setLoading(true)
        let receivedData: any = []
        const currentCompany: any = getDataFromLocalStorage('currentCompany')

        const clientResponse = await getOptionApi(
          { deleted: false, limit: pageSize },
          selectedOptions?.map((v) => v._id)?.join(',')
        )

        if (isSuccess(clientResponse)) {
          const { options } = clientResponse?.data?.data
          setDataDefaultTrue(options.find((v: any) => v.default === true))

          const tableData = options.reduce((prev: any, cur: any) => {
            return [
              ...prev,
              {
                ...cur,
                name: cur.name,
                type: cur.type,
                order: cur.order,
                group: cur.group,
                // default: `${cur.default}`,
              },
            ]
          }, [])

          receivedData.push(...tableData)
        } else {
          notify(clientResponse?.data?.message, 'error')
        }
        const fetchId = ++fetchIdRef.current
        if (fetchId === fetchIdRef.current) {
          const startRow = pageSize * pageIndex
          const endRow = startRow + pageSize
          setData(receivedData.slice(startRow, endRow))
        }
      } catch (error) {
        console.error('TeamTable fetchData error', error)
      } finally {
        setLoading(false)
      }
    },
    [detailsUpdate, selectedOptions]
  )

  const getDetails = async () => {
    try {
      getProfileInfo()
      const response = await getProfileInfo()
      if (isSuccess(response)) {
        let user = response?.data?.data?.user
        let userObject = {
          firstName: user.firstName,
          lastName: user.lastName,
          preferredName: user.preferredName,
          // username: user.username,
          email: user.email,
          roles: 'madmin',
        }
        setInitialValues({ ...initialValues, ...userObject })
      } else {
        notify(response?.data?.message, 'error')
      }
    } catch (error) {
      console.error('getDetails error', error)
    }
  }

  useEffect(() => {
    getDetails()
  }, [])

  return (
    <SettingsCont gap="24px">
      <SharedStyled.FlexRow justifyContent="space-between">
        <SharedStyled.SectionTitle>Options</SharedStyled.SectionTitle>
        <ButtonCont>
          <Button
            onClick={() => {
              setShowCreateOptions(true)
              setOptionType('add')
            }}
          >
            Add Option
          </Button>
        </ButtonCont>
      </SharedStyled.FlexRow>

      <SharedStyled.FlexRow alignItems="flex-start">
        <SharedStyled.FlexCol gap="24px">
          <TabBar
            tabs={[
              {
                title: 'Active',
                render: () => (
                  <Table
                    noOverflow
                    columns={inputColumns}
                    data={data}
                    loading={loading}
                    fetchData={fetchData}
                    onRowClick={(vals) => {
                      setShowCreateOptions(true)
                      setEditOptionVals(vals)
                    }}
                    noSearch
                    minWidth=""
                    noBorder
                    ref={loadmoreRef}
                    isLoadMoreLoading={loading}
                  />
                ),
              },
              {
                title: 'Deleted',
                render: () => <DeletedOptions />,
              },
            ]}
            filterComponent={
              <>
                <SharedStyled.FlexRow justifyContent="flex-end">
                  <DropdownWithCheckboxesWithoutFormik
                    options={types?.map((v) => ({ name: v.name, _id: v.id })) || []}
                    selectedOptions={selectedOptions} // Pass local state
                    onChange={(selected) => setSelectedOptions(selected)} // Update local state
                  />
                </SharedStyled.FlexRow>
              </>
            }
          />
        </SharedStyled.FlexCol>
      </SharedStyled.FlexRow>

      <CustomModal show={showCreateOptions} className="top">
        <OptionsModal
          setShowCreateOptions={setShowCreateOptions}
          onClose={() => {
            setShowCreateOptions(false)
            setEditOptionVals(null)
          }}
          header={!!editOptionVals ? `Edit Option` : `Add Option`}
          inputData={editOptionVals}
          isEditing={!!editOptionVals}
          optionType={optionType}
          setOptionType={setOptionType}
          onComplete={() => {
            fetchData({
              pageIndex: 0,
              pageSize: 20,
            })
          }}
          types={types}
          units={units}
          packages={packages}
          //   dataDefaultTrue={dataDefaultTrue || {}}
        />
      </CustomModal>
    </SettingsCont>
  )
}

export default OptionsSettings
