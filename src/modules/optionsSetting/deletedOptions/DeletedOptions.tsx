import React, { useCallback, useEffect, useRef, useState } from 'react'
import * as Styled from './style'
import * as SharedStyled from '../../../styles/styled'
import { Navigate, useNavigate, useParams } from 'react-router-dom'
import { useSelector } from 'react-redux'
import { getOptionApi, getProjectTypes, permDeleteOption, restoreOption } from '../../../logic/apis/projects'
import { getDataFromLocalStorage, getNameFromId, isSuccess, notify } from '../../../shared/helpers/util'
import { Table } from '../../../shared/table/Table'
import { CustomModal } from '../../../shared/customModal/CustomModal'
import DeletedModal from '../../deleted/components/deletedModal/DeletedModal'
import { IType } from '../../newProject/NewProject'
import { StorageKey } from '../../../shared/helpers/constants'

const DeletedOptions = () => {
  const navigate = useNavigate()
  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company
  const [packageTableValues, setPackageTableValues] = useState([])
  const [deletedModal, setDeletedModal] = useState(false)
  const [inputData, setInputData] = useState<any>()
  const [types, setTypes] = useState<IType[]>([])

  const fetchIdRef = useRef(0)
  // const [pageCount, setPageCount] = useState<number>(10)
  const [data, setData] = useState<any[]>([])
  // const [detailsUpdate, setDetailsUpdate] = useState(false)

  const [loading, setLoading] = useState<boolean>(false)
  const loadmoreRef = useRef(null)

  const inputColumns = [
    {
      Header: 'Option Name',
      accessor: 'name',
    },
    // {
    //   Header: 'Default',
    //   accessor: 'default',
    // },
    {
      Header: 'Project Type',
      accessor: 'type',
      Cell: ({ value }: { value: string }) => getNameFromId(value, types),
    },
    {
      Header: 'Group',
      accessor: 'group',
    },
  ]

  const fetchData = useCallback(async ({ pageSize, pageIndex }: any) => {
    try {
      // This will get called when the table needs new data
      setLoading(true)
      let receivedData: any = []
      const currentCompany: any = getDataFromLocalStorage('currentCompany')

      const clientResponse = await getOptionApi({ deleted: true, limit: pageSize })

      if (isSuccess(clientResponse)) {
        const { options } = clientResponse?.data?.data
        const tableData = options.reduce((prev: any, cur: any) => {
          return [
            ...prev,
            {
              ...cur,
              name: cur.name,
              type: cur.type,
              order: cur.order,
              group: cur.group,
              // default: `${cur.default}`,
            },
          ]
        }, [])

        receivedData.push(...tableData)
      } else {
        notify(clientResponse?.data?.message, 'error')
      }

      // Give this fetch an ID
      const fetchId = ++fetchIdRef.current

      if (fetchId === fetchIdRef.current) {
        const startRow = pageSize * pageIndex
        const endRow = startRow + pageSize
        setData(receivedData.slice(startRow, endRow))
      }
      // }, 1000)
    } catch (error) {
      console.error('TeamTable fetchData error', error)
    } finally {
      setLoading(false)
    }
  }, [])

  const onComplete = () => {
    setInputData(null)
    setDeletedModal(false)
    fetchData({
      pageIndex: 0,
      pageSize: 20,
    })
  }

  useEffect(() => {
    initFetch()
  }, [])

  const initFetch = async () => {
    try {
      const res = await getProjectTypes({ deleted: false })
      if (isSuccess(res)) {
        const { projectType } = res.data.data
        const object = projectType.map(({ _id, name }: { _id: string; name: string }) => ({
          name: name,
          id: _id,
          value: _id,
          label: name,
        }))
        // setProjectTypes(projectType)
        setTypes(object)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('Failed init fetch', err)
    }
  }

  const onDelete = async () => {
    try {
      const res = await permDeleteOption({
        id: inputData?._id ?? '',
      })
      if (isSuccess(res)) {
        notify(`Deleted Option!`, 'success')
        onComplete()
      } else throw new Error(res?.data?.message)
    } catch (err) {
      notify('Failed to delete Option!', 'error')
      console.log('Input err', err)
    }
  }

  const onRestore = async () => {
    try {
      const res = await restoreOption({
        id: inputData?._id ?? '',
      })
      if (isSuccess(res)) {
        notify(`Restored Option!`, 'success')
        onComplete()
      } else throw new Error(res?.data?.message)
    } catch (err) {
      notify('Failed to restore Option!', 'error')
      console.log('Input err', err)
    }
  }

  return (
    <>
      <Table
        columns={inputColumns}
        data={data}
        loading={loading}
        fetchData={fetchData}
        onRowClick={(data) => {
          setInputData(data)
          setDeletedModal(true)
        }}
        noSearch
        minWidth=""
        noBorder
        // client={true}
        ref={loadmoreRef}
        isLoadMoreLoading={loading}
      />
      <CustomModal show={deletedModal}>
        <DeletedModal
          onDelete={onDelete}
          onRestore={onRestore}
          onClose={() => {
            setDeletedModal(false)
            setInputData(null)
          }}
          inputData={inputData}
          title="Deleted Options"
        />
      </CustomModal>
    </>
  )
}

export default DeletedOptions
