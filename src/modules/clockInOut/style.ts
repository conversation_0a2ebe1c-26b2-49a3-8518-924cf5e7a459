import styled from 'styled-components'
import { colors, screenSizes } from '../../styles/theme'
import { FlexCol } from '../../styles/styled'
import { Nue } from '../../shared/helpers/constants'
import { SettingsCont } from '../units/style'
import { DropDownContentContainer } from '../../shared/autoComplete/style'

export const ClockInCont = styled(SettingsCont)`
  @media (min-width: ${screenSizes.M}px) {
    width: 50%;
  }
`

export const ClockInTime = styled.p<any>`
  font-family: ${Nue.bold};
  color: ${colors.text};
  font-size: 14px;
  text-transform: capitalize;
`

export const InfoCont = styled(FlexCol)`
  span {
    color: ${colors.black};
    font-family: ${Nue.regular};
    font-size: 12px;
    text-transform: capitalize;
  }

  .light {
    color: #6a747e;
    font-family: ${Nue.regular};
    font-size: 10px;
  }

  &.passed-time {
    @media (max-width: ${screenSizes.XS}px) {
      display: none;
    }
  }
  &.mob-passed-time {
    display: block;
    margin-top: 30px;

    ${ClockInTime} {
      font-size: 18px;
    }

    p {
      font-family: ${Nue.bold};
    }

    /* @media (min-width: ${screenSizes.XS}px) {
      display: none;
    } */
  }

  &.desktop {
    @media (min-width: ${screenSizes.XS}px) {
      p {
        font-size: 24px;
      }
    }
  }
`

export const UpperPartContainer = styled.div`
  width: 100%;
`

export const LoaderContainer = styled.span<any>`
  .loading:after {
    content: ' : ';
    animation: ${(props) => (props.isClockedIn ? `dots 1.2s steps(5, end) infinite` : '')};
  }

  @keyframes dots {
    0%,
    20% {
      color: rgba(0, 0, 0, 0);
      text-shadow: 0.25em 0 0 rgba(0, 0, 0, 0), 0.5em 0 0 rgba(0, 0, 0, 0);
    }
    40% {
      color: white;
      text-shadow: 0.25em 0 0 rgba(0, 0, 0, 0), 0.5em 0 0 rgba(0, 0, 0, 0);
    }
    60% {
      text-shadow: 0.25em 0 0 white, 0.5em 0 0 rgba(0, 0, 0, 0);
    }
    80%,
    100% {
      text-shadow: 0.25em 0 0 white, 0.5em 0 0 white;
    }
  }
`

export const LoaderContent = styled.span`
  /* font-family: NunitoSansSemiBold; */
  margin: 0;
  font-size: 16px;
  @media (min-width: ${screenSizes.M}px) {
    font-size: 24px;
  }

  font-weight: bold;
  color: ${colors.darkGrey};
`
