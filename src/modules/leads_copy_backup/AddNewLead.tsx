import { Field, Form, Formik } from 'formik'
import { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate, useParams } from 'react-router-dom'
import * as Yup from 'yup'
import { getCampaigns, getLeadSources } from '../../logic/apis/leadSource'
import { getPosition } from '../../logic/apis/position'
import { getTeamMembers } from '../../logic/apis/team'
import AddressAutocomplete from '../../shared/addressInput/AddressInput'
import { CustomModal } from '../../shared/customModal/CustomModal'
import CustomSelect from '../../shared/customSelect/CustomSelect'
import { SharedDate } from '../../shared/date/SharedDate'
import {
  combineDateTime,
  dayjsFormat,
  formatPhoneNumber,
  getBooleanFromName,
  getDataFromLocalStorage,
  getIdFromName,
  getKeysFromObjects,
  getNameFrom_Id,
  getValueByKeyAndMatch,
  hasValues,
  isSuccess,
  notify,
  parseTimeString,
  splitFullName,
  startOfDate,
} from '../../shared/helpers/util'
import { InputWithValidation } from '../../shared/inputWithValidation/InputWithValidation'
import * as SharedStyled from '../../styles/styled'
import * as Styled from './style'
import { createLead, createOpportunity, getPositionMembersById, getStages } from '../../logic/apis/sales'
import AutoComplete from '../../shared/autoComplete/AutoComplete'
import { I_Stage } from '../opportunity/components/assessmentForm/AssessmentForm'
import { getProjectTypes } from '../../logic/apis/projects'
import AutoCompleteAddress from '../../shared/autoCompleteAdress/AutoCompleteAddress'
import { CrossContainer, ModalContainer, ModalHeader, ModalHeaderContainer, ModalHeaderInfo } from './style'
import { CrossIcon } from '../../assets/icons/CrossIcon'
import UnitSvg from '../../assets/newIcons/unitModal.svg'
import Button from '../../shared/components/button/Button'
import { getReferres } from '../../logic/apis/company'
import { Nue, StageGroupEnum, StorageKey, usStatesShortNames } from '../../shared/helpers/constants'
import { colors } from '../../styles/theme'
import ReferrerModal from '../Refferer/components/referrerModal/ReferrerModal'
import { getDistanceAndDuration } from '../../shared/helpers/map'
import { LoadScript } from '@react-google-maps/api'
import { getConfig } from '../../config'
import { SLoader } from '../../shared/components/loader/Loader'
import { getClients, getSalesPersonDetailsByClient } from '../../logic/apis/client'
import useDebounce from '../../shared/hooks/useDebounce'
import { NormalInput } from '../../shared/normalInput/NormalInput'
import dayjs from 'dayjs'
import { SharedPhone } from '../../shared/sharedPhone/SharedPhone'
import Toggle from '../../shared/toggle/Toggle'
import { SharedDateAndTime } from '../../shared/date/SharedDateAndTime'
import useFetch from '../../logic/apis/useFetch'
import { getFormattedLeadSrcData, getLeadSrcDropData, getLeadSrcDropdownId } from '../leadSource/LeadSource'
import AutoCompleteIndentation from '../../shared/autoCompleteIndentation/AutoCompleteIndentation'

// import AutoCompleteAddress from '../../shared/autoCompleteAdress/AutoCompleteAddress'

export interface I_LeadSource {
  _id: string
  name: string
  channelId: string
  deleted: boolean
  channelName: string[]
}

export interface I_SalesPerson {
  _id: string
  email: string
  name: string
  // username: string
  company: string
  user: string
  invited: boolean
  roleId: string
  deleted: boolean
}

export interface I_Position {
  _id: string
  position: string
  permissions: any
  deleted: boolean
  createdBy: string
}

interface ICategoryModal {
  onClose: () => void
  onComplete: () => void
  referrerDropdownData: any
  setReferrerValue: any
  refererres: any
  // addressLoading: any
  // clientDropdown: any
  setShowAddNewClientModal: any
  detailsUpdate: any
  addNewClientModal: any
  setClientName: any
  createdClient: any
  officeDrop: any
}
const AddNewLead = (props: ICategoryModal) => {
  const { onClose, referrerDropdownData, setReferrerValue, detailsUpdate, refererres, onComplete, officeDrop } = props

  const navigate = useNavigate()
  const globalSelector = useSelector((state: any) => state)
  const { currentMember, companySettingForAll } = globalSelector.company

  const [toggleGoogleAddressInput, setToggleGoogleAddressInput] = useState<boolean>(false)
  // const [showAddCityModal, setShowAddCityModal] = useState<boolean>(false)
  const [addressLoading, setAddressLoading] = useState<boolean>(true)
  // const [detailsUpdate, setDetailsUpdate] = useState(false)
  // const [addNewClientModal, setShowAddNewClientModal] = useState(false)
  const [projectTypesDrop, setProjectTypesDrop] = useState<any>([])
  const [projectTypes, setProjectTypes] = useState<any>([])
  // const [clientAutoFill, setClientAutoFill] = useState<any>({})
  // const [clientName, setClientName] = useState<any>([])
  const [lat, setLat] = useState('')
  const [long, setLong] = useState('')
  const [distance, setDistance] = useState(0)
  const [duration, setDuration] = useState(0)
  const [loading, setLoading] = useState(false)
  const [stages, setStages] = useState<I_Stage[]>([])
  const [leadsrcDrop, setLeadsrcDrop] = useState<I_LeadSource[]>([])
  const [leadSrcData, setLeadSrcData] = useState([])

  const [defaultStage, setDefaultStage] = useState('') // will be stage id
  const [selectedType, setSelectedType] = useState<string>('')
  const [salesPersonByClient, setSalesPersonByClient] = useState<any>([])
  const [addressInputType, setAddressInputType] = useState<'custom' | 'google'>()
  const [isBusiness, setIsBusiness] = useState(false)

  const [initialvalues, setInitialValues] = useState({
    newLeadDate: dayjsFormat(new Date(), 'YYYY-MM-DDTHH:mm'),
    // newLeadDate: dayjsFormat(new Date(), 'YYYY-MM-DD'),
    firstName: '',
    lastName: '',
    phone: '',
    email: '',
    street: '',
    city: '',
    zip: '',
    state: '',
    distance: 0,
    duration: 0,
    leadSource: '',
    referredBy: '',
    workType: '',
    CSRAssigned: '',
    notes: '',
    campaign: '',
  })
  console.log({ asdasda: getValueByKeyAndMatch('name', stages[0]?._id, '_id', officeDrop) })
  const getStagesData = async () => {
    try {
      const stagesRes = await getStages({}, false, StageGroupEnum.Leads)
      if (isSuccess(stagesRes)) {
        const { stage: stages } = stagesRes.data.data
        const formatStages: I_Stage[] = new Array(stages.length)
        setInitialValues((pre) => ({
          ...pre,
          CSRAssigned: getValueByKeyAndMatch('name', stages[0]?.defaultCsrId, '_id', officeDrop),
        }))

        stages.forEach((stage: I_Stage) => {
          formatStages[stage.sequence - 1] = stage
          if (stage.code === 'newLead') setDefaultStage(stage._id)
        })

        setStages(formatStages)
      } else throw new Error(stagesRes?.data?.message)
    } catch (err) {
      console.log('Err stages data', err)
    }
  }
  // useEffect(() => {
  //   if (stage?.length) {
  //     stage.forEach((stage: I_Stage) => {
  //       if (stage.code === 'newLead') setDefaultStage(stage._id)
  //     })
  //   }
  // }, [stage])
  useEffect(() => {
    initFetch()
  }, [])

  useEffect(() => {
    if (leadSrcData?.length) {
      const data = getLeadSrcDropData(leadSrcData)
      setLeadsrcDrop(data)
    }
  }, [leadSrcData?.length])

  const initFetch = async () => {
    try {
      const res = await getProjectTypes({ deleted: false })
      if (isSuccess(res)) {
        const { projectType } = res.data.data
        const object = projectType.map(({ _id, name }: { _id: string; name: string }) => ({
          name: name,
          id: _id,
          value: _id,
          label: name,
        }))
        setProjectTypes(projectType)
        setProjectTypesDrop([
          ...object,
          {
            name: 'Unknown',
            id: 'unknown',
            value: 'unknown',
            label: 'unknown',
          },
        ])
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('Failed init fetch', err)
    }
  }

  const handleSubmit = async (values: typeof initialvalues) => {
    const result = getLeadSrcDropdownId(values?.leadSource, leadSrcData)

    try {
      // const newLeadDateDayStartOf = values.newLeadDate
      const type = getIdFromName(values.workType, projectTypesDrop)
      const referredById = getIdFromName(values?.referredBy, refererres)
      // const leadSourceId = getIdFromName(values?.leadSource, leadsrcDrop)
      const leadSourceId = result?.leadSourceId
      const campaignId = result?.campaignId || undefined
      const CSRAssignedId = getIdFromName(values?.CSRAssigned, officeDrop)

      values.distance = values.distance ? Number(values.distance) : 0
      values.duration = values.duration ? Number(values.duration) : 0

      const data = {
        ...values,
        firstName: values?.firstName?.trim(),
        lastName: isBusiness ? '' : values?.lastName?.trim() || '',
        email: values.email || undefined,
        phone: values.phone || undefined,
        newLeadDate: new Date(values.newLeadDate),
        createdBy: currentMember._id,
        companyAddress: companySettingForAll?.address,
        workType: type,
        csrId: CSRAssignedId,
        leadSource: values?.leadSource || '',
        leadSourceId: leadSourceId != '' ? leadSourceId : undefined,
        referredBy: referredById !== '' ? referredById : undefined,
        campaignId,
        stage: defaultStage,
        isBusiness,
      }
      console.log({ data })
      const res = await createLead(data)
      if (isSuccess(res)) {
        notify('Created new lead!', 'success')
        onComplete()
      }
    } catch (error) {
      console.log({ error })
    }
  }

  const getLeadSrcData = async () => {
    try {
      const leadSourceResponse = await getLeadSources({ limit: '100', active: true }, false)
      if (isSuccess(leadSourceResponse)) {
        let statusRes = leadSourceResponse?.data?.data?.leadSource
        setLeadSrcData(statusRes)
      } else notify(leadSourceResponse?.data?.message, 'error')
    } catch (err) {
      // notify('Failed to fetch lead sources!', 'error')
      console.log('Lead source fetch error', err)
    }
  }

  useEffect(() => {
    getLeadSrcData()
    // getPositions()
    getStagesData()
  }, [detailsUpdate])

  const newLeadSchema = Yup.object().shape({
    newLeadDate: Yup.string().required('Required'),
    firstName: Yup.string().required('Required'),
    leadSource: Yup.string().required('Required'),
    workType: Yup.string().required('Required'),
    phone: Yup.string().test('phone-or-email', 'Either phone or email is required', function (value) {
      return value || this.parent.email
    }),
    email: Yup.string()
      .email('Invalid email')
      .test('phone-or-email', 'Either phone or email is required', function (value) {
        return value || this.parent.phone
      }),
  })

  return (
    <>
      <ModalContainer>
        <ModalHeaderContainer>
          <SharedStyled.FlexRow>
            <img src={UnitSvg} alt="modal icon" />
            <SharedStyled.FlexCol>
              <ModalHeader>New Lead</ModalHeader>
            </SharedStyled.FlexCol>
          </SharedStyled.FlexRow>
          <CrossContainer
            onClick={() => {
              onClose()
            }}
          >
            <CrossIcon />
          </CrossContainer>
        </ModalHeaderContainer>
        <SharedStyled.SettingModalContentContainer>
          <>
            <Formik
              initialValues={initialvalues}
              onSubmit={handleSubmit}
              validationSchema={newLeadSchema}
              validateOnChange={true}
              validateOnBlur={false}
              enableReinitialize={true}
            >
              {({ errors, touched, values, setFieldValue, handleChange }) => {
                useEffect(() => {
                  if (isBusiness) {
                    if (values?.firstName !== '' || values?.lastName !== '')
                      setFieldValue('firstName', `${values?.firstName?.trim()} ${values?.lastName?.trim() || ''}`)
                    setFieldValue('lastName', '')
                  } else {
                    const { firstName, lastName } = values?.lastName
                      ? splitFullName(`${values?.firstName} ${values?.lastName}`)
                      : splitFullName(values?.firstName)
                    setFieldValue('firstName', firstName)
                    setFieldValue('lastName', lastName)
                  }
                }, [isBusiness])
                return (
                  <LoadScript
                    googleMapsApiKey={getConfig().googleAddressApiKey}
                    //  @ts-ignore
                    libraries={['places']}
                    loadingElement={<SLoader height={35} width={100} isPercent />}
                  >
                    <Styled.NewLeadContainer>
                      {/* <SharedStyled.ContentContainer> */}
                      <Form className="form">
                        <SharedStyled.Content
                          overflow={'unset'}
                          disableBoxShadow={true}
                          noPadding={true}
                          gap="8px"
                          width="100%"
                        >
                          <SharedDateAndTime
                            value={values.newLeadDate}
                            labelName="Date/Time lead 
                            came in *"
                            stateName="newLeadDate"
                            setFieldValue={setFieldValue}
                            error={touched.newLeadDate && errors.newLeadDate ? true : false}
                          />
                          {/* <SharedDate
                            value={values.newLeadDate}
                            labelName="Date lead came in *"
                            stateName="newLeadDate"
                            setFieldValue={setFieldValue}
                            error={touched.newLeadDate && errors.newLeadDate ? true : false}
                          /> */}

                          <Toggle
                            title="Business"
                            customStyles={{ margin: '16px' }}
                            isToggled={isBusiness}
                            onToggle={() => {
                              setIsBusiness((prev) => !prev)
                            }}
                          />

                          {isBusiness ? (
                            <InputWithValidation
                              labelName="Business Name*"
                              stateName="firstName"
                              error={touched.firstName && errors.firstName ? true : false}
                              twoInput={true}
                            />
                          ) : (
                            <SharedStyled.TwoInputDiv>
                              <InputWithValidation
                                labelName="Primary First Name*"
                                stateName="firstName"
                                error={touched.firstName && errors.firstName ? true : false}
                                twoInput={true}
                              />
                              <InputWithValidation
                                labelName="Primary Last Name"
                                stateName="lastName"
                                error={touched.lastName && errors.lastName ? true : false}
                                twoInput={true}
                              />
                            </SharedStyled.TwoInputDiv>
                          )}

                          {/* <SharedStyled.TwoInputDiv>
                            <InputWithValidation
                              labelName="First Name *"
                              stateName="firstName"
                              error={touched.firstName && errors.firstName ? true : false}
                              twoInput={true}
                            />
                            <InputWithValidation
                              labelName="Last Name"
                              stateName="lastName"
                              error={touched.lastName && errors.lastName ? true : false}
                              twoInput={true}
                            />
                          </SharedStyled.TwoInputDiv> */}

                          <SharedStyled.TwoInputDiv>
                            <SharedPhone
                              labelName="Primary Phone"
                              stateName="phone"
                              onChange={handleChange('phone')}
                              error={touched.phone && errors.phone ? true : false}
                            />

                            <InputWithValidation
                              labelName="Primary Email"
                              stateName="email"
                              error={touched.email && errors.email ? true : false}
                            />
                          </SharedStyled.TwoInputDiv>
                          <Styled.AddressContainer>
                            {
                              <h2 className="sub-heading">
                                Address:{' '}
                                <SharedStyled.BlueEdit
                                  onClick={() => {
                                    setToggleGoogleAddressInput(true)
                                    setAddressInputType('google')
                                  }}
                                >
                                  {toggleGoogleAddressInput ? '' : 'Edit'}
                                </SharedStyled.BlueEdit>
                              </h2>
                            }
                            {console.log({ values }, addressLoading, toggleGoogleAddressInput)}
                            {toggleGoogleAddressInput ? (
                              <Styled.GoogleSearchBox>
                                {addressInputType === 'custom' ? (
                                  <Styled.AddressCont gap="12px" id="custom">
                                    <SharedStyled.FlexCol gap="4px">
                                      <SharedStyled.FlexRow className="input">
                                        <div title="Street" id="street">
                                          <InputWithValidation
                                            labelName="Street"
                                            stateName="street"
                                            error={touched.street && errors.street ? true : false}
                                            twoInput={true}
                                          />
                                        </div>
                                      </SharedStyled.FlexRow>

                                      <SharedStyled.FlexRow className="input" justifyContent="space-between">
                                        <div id="city">
                                          <InputWithValidation
                                            labelName="City"
                                            stateName="city"
                                            error={touched.city && errors.city ? true : false}
                                            twoInput={true}
                                          />
                                        </div>

                                        <div id="state">
                                          <CustomSelect
                                            dropDownData={companySettingForAll?.workingStates || []}
                                            setValue={() => {}}
                                            stateName="state"
                                            value={values.state}
                                            // error={touched.weekStartDay && errors.weekStartDay ? true : false}
                                            setFieldValue={setFieldValue}
                                            labelName="State"
                                            innerHeight="52px"
                                            margin="10px 0 0 0"
                                          />
                                        </div>

                                        <div id="zip">
                                          <InputWithValidation
                                            labelName="Zip"
                                            stateName="zip"
                                            error={touched.zip && errors.zip ? true : false}
                                            twoInput={true}
                                          />
                                        </div>
                                      </SharedStyled.FlexRow>

                                      <SharedStyled.FlexRow
                                        style={{ width: '100%' }}
                                        margin="4px 0 0 0"
                                        flexWrap="wrap"
                                        justifyContent="space-between"
                                      >
                                        <SharedStyled.FlexRow width="max-content">
                                          <Button
                                            // className="delete"
                                            type="button"
                                            width="max-content"
                                            onClick={() => {
                                              setToggleGoogleAddressInput(false)
                                              setAddressInputType('google')
                                            }}
                                          >
                                            Save
                                          </Button>
                                          <Button
                                            className="delete"
                                            type="button"
                                            width="max-content"
                                            onClick={() => {
                                              setToggleGoogleAddressInput(false)
                                              setAddressInputType('google')
                                            }}
                                          >
                                            Cancel
                                          </Button>
                                        </SharedStyled.FlexRow>
                                        <Button
                                          type="button"
                                          onClick={() => {
                                            setAddressInputType('google')
                                            setToggleGoogleAddressInput(true)
                                          }}
                                          className="gray"
                                          width="max-content"
                                        >
                                          Google
                                        </Button>
                                      </SharedStyled.FlexRow>
                                    </SharedStyled.FlexCol>
                                  </Styled.AddressCont>
                                ) : (
                                  <Styled.AddressCont gap="12px" className="google" id="google">
                                    <SharedStyled.FlexCol margin="10px 0 0 0" width="100%">
                                      <AutoCompleteAddress
                                        setFieldValue={setFieldValue}
                                        street={'street'}
                                        city={'city'}
                                        state={'state'}
                                        zip={'zip'}
                                        distance={'distance'}
                                        duration={'duration'}
                                        sourceAddress={companySettingForAll?.address}
                                        companyLatLong={companySettingForAll}
                                        setLat={setLat}
                                        setLong={setLong}
                                        setDistance={setDistance}
                                        setDuration={setDuration}
                                        noLoadScript={true}
                                      />
                                      <SharedStyled.FlexRow
                                        style={{ width: '100%' }}
                                        margin="4px 0 0 0"
                                        flexWrap="wrap"
                                        justifyContent="space-between"
                                      >
                                        <SharedStyled.FlexRow width="max-content">
                                          <Button
                                            // className="delete"
                                            type="button"
                                            width="max-content"
                                            onClick={() => {
                                              setToggleGoogleAddressInput(false)
                                              setAddressInputType('google')
                                            }}
                                          >
                                            Save
                                          </Button>
                                          <Button
                                            className="delete"
                                            type="button"
                                            width="max-content"
                                            onClick={() => {
                                              setToggleGoogleAddressInput(false)
                                            }}
                                          >
                                            Cancel
                                          </Button>
                                        </SharedStyled.FlexRow>
                                        <Button
                                          type="button"
                                          onClick={() => {
                                            setAddressInputType('custom')
                                            setToggleGoogleAddressInput(true)
                                          }}
                                          className="gray"
                                          width="max-content"
                                        >
                                          Custom
                                        </Button>
                                      </SharedStyled.FlexRow>
                                    </SharedStyled.FlexCol>
                                  </Styled.AddressCont>
                                )}
                              </Styled.GoogleSearchBox>
                            ) : (
                              (values.street !== '' ||
                                values.city !== '' ||
                                values.state !== '' ||
                                values.zip !== '') && (
                                <div style={{ width: '100%' }}>
                                  <SharedStyled.Text fontWeight="400">
                                    <span style={{ fontFamily: Nue.regular }}>
                                      {values.street !== '' ? values.street : ''}
                                    </span>
                                  </SharedStyled.Text>
                                  <br />
                                  <SharedStyled.Text fontWeight="400">
                                    {/* <b>City : </b> */}
                                    <span style={{ fontFamily: Nue.regular }}>{values.city},&nbsp;</span>
                                    {/* <b>State : </b> */}
                                    <span style={{ fontFamily: Nue.regular }}>{values.state},&nbsp;</span>
                                    {/* <b>Zip : </b> */}
                                    <span style={{ fontFamily: Nue.regular }}>{values.zip}</span>
                                  </SharedStyled.Text>
                                  &emsp;
                                </div>
                              )
                            )}
                          </Styled.AddressContainer>

                          {values.street && values.city && values.state && values.zip && (
                            <SharedStyled.TwoInputDiv>
                              <InputWithValidation
                                labelName="Drive Time"
                                stateName="duration"
                                twoInput={true}
                                disabled={addressInputType !== 'custom'}
                                error={touched.duration && errors.duration ? true : false}
                              />
                              <InputWithValidation
                                labelName="Distance"
                                stateName="distance"
                                twoInput={true}
                                disabled={addressInputType !== 'custom'}
                                error={touched.distance && errors.distance ? true : false}
                              />
                            </SharedStyled.TwoInputDiv>
                          )}
                          {/* <CustomSelect
                            labelName="Lead source *"
                            stateName="leadSource"
                            error={touched.leadSource && errors.leadSource ? true : false}
                            setFieldValue={setFieldValue}
                            value={values.leadSource}
                            dropDownData={getKeysFromObjects(leadsrcDrop, 'name')?.sort()}
                            setValue={() => {}}
                            innerHeight="52px"
                            className="top"
                            // testId
                          /> */}

                          {leadSrcData?.length ? (
                            <AutoCompleteIndentation
                              labelName="Lead Source*"
                              stateName={`leadSource`}
                              isLeadSource
                              dropdownHeight="300px"
                              error={touched.leadSource && errors.leadSource ? true : false}
                              borderRadius="0px"
                              setFieldValue={setFieldValue}
                              options={mergeSourceAndCampaignNames(leadSrcData)}
                              formatedOptions={getFormattedLeadSrcData(leadSrcData)}
                              value={values.leadSource!}
                              setValueOnClick={(val: string) => {
                                setFieldValue('leadSource', val)
                              }}
                              className="material-autocomplete"
                              isIndentation={true}
                            />
                          ) : null}

                          {/* hardcoded code */}
                          {values.leadSource === 'Referral' && (
                            <SharedStyled.FlexBox width="100%" justifyContent="end">
                              <CustomSelect
                                labelName="Referrer"
                                stateName="referredBy"
                                error={touched.referredBy && errors.referredBy ? true : false}
                                setFieldValue={setFieldValue}
                                setValue={setReferrerValue}
                                value={values.referredBy}
                                dropDownData={referrerDropdownData}
                                innerHeight="52px"
                                className="top"
                                maxWidth="95%"
                              />
                            </SharedStyled.FlexBox>
                          )}

                          <CustomSelect
                            labelName="Type of work requested *"
                            stateName="workType"
                            error={touched.workType && errors.workType ? true : false}
                            setFieldValue={setFieldValue}
                            value={values.workType}
                            dropDownData={[...projectTypesDrop.map(({ name }: { name: string }) => name)]}
                            setValue={setSelectedType}
                            innerHeight="52px"
                            margin="10px 0 0 0"
                          />
                          <CustomSelect
                            labelName="CSR Assigned"
                            stateName="CSRAssigned"
                            error={touched.CSRAssigned && errors.CSRAssigned ? true : false}
                            setFieldValue={setFieldValue}
                            value={values.CSRAssigned}
                            dropDownData={getKeysFromObjects(officeDrop, 'name')}
                            setValue={() => {}}
                            innerHeight="52px"
                            margin="10px 0 0 0"
                          />
                          <Styled.TextArea
                            component="textarea"
                            placeholder="Comments/Notes"
                            as={Field}
                            name="notes"
                            marginTop="8px"
                            height="52px"
                            stateName="notes"
                            labelName="Opportunity Notes"
                            error={touched.notes && errors.notes ? true : false}
                          />

                          {/* TODO: Change later */}
                          <SharedStyled.FlexRow width="100%" alignItems="flex-start" gap="20px"></SharedStyled.FlexRow>

                          <SharedStyled.FlexBox
                            width="100%"
                            alignItems="center"
                            gap="20px"
                            wrap="wrap"
                            marginTop="25px"
                          >
                            <Button disabled={loading} isLoading={loading} type="submit" className="fit">
                              Save New Lead
                            </Button>
                            <Button type="button" className="fit delete" onClick={onClose}>
                              Cancel
                            </Button>
                          </SharedStyled.FlexBox>
                        </SharedStyled.Content>
                      </Form>
                      {/* </SharedStyled.ContentContainer> */}
                    </Styled.NewLeadContainer>
                  </LoadScript>
                )
              }}
            </Formik>

            {/* <CustomModal show={showAddCityModal}>
              <AddCityModal
                setShowAddCityModal={setShowAddCityModal}
                action="Add City"
                setDetailsUpdate={setDetailsUpdate}
              />
            </CustomModal> */}
          </>
        </SharedStyled.SettingModalContentContainer>
      </ModalContainer>
    </>
  )
}

export default AddNewLead
