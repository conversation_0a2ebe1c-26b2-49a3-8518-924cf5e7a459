import { Form, Formik } from 'formik'
import { useEffect, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useParams } from 'react-router-dom'
import * as Yup from 'yup'

import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import { sendInvitation } from '../../../../logic/apis/invitation'
import { getProfileInfo } from '../../../../logic/apis/profile'
import { setJustInvited } from '../../../../logic/redux/actions/invitation'
import { onlyText } from '../../../../shared/helpers/regex'
import { getDataFromLocalStorage, isSuccess, notify, startOfDate } from '../../../../shared/helpers/util'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import * as SharedStyled from '../../../../styles/styled'
import { ModalHeaderInfo } from '../../../units/components/newUnitModal/style'
import * as Styled from './style'
import UnitSvg from '../../../../assets/newIcons/unitModal.svg'
import Button from '../../../../shared/components/button/Button'
import { SharedPhone } from '../../../../shared/sharedPhone/SharedPhone'
import { SharedDate } from '../../../../shared/date/SharedDate'
import '../../../../shared/helpers/yupExtension'
import { StorageKey } from '../../../../shared/helpers/constants'
/**
 * InitialValues is an interface declared here so that it can be used as a type for the useState hook
 */
interface InitialValues {
  firstName: string
  lastName: string
  email: string
  phone?: string
  // hireDate: string
  preferredName?: string
}

/**
 * I_InvitationForm is an interface which defines the props type which we receive from our parent component to this component
 */
interface I_InvitationForm {
  setShowCompanyCreationModal: React.Dispatch<React.SetStateAction<boolean>>
  setDataUpdate: React.Dispatch<React.SetStateAction<boolean>>
  onInviteSuccess: (id: string) => void
}

/**
 *
 * @returns A InvitationForm component with all the validations to its input fields
 */
export const InvitationForm = (props: I_InvitationForm) => {
  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */
  const [initialValues, setInitialValues] = useState<InitialValues>({
    firstName: '',
    lastName: '',
    email: '',
    // hireDate: '',
  })

  /**
   * loading will be the loading state when performing the operations
   */
  const [loading, setLoading] = useState<boolean>(false)

  const [senderEmail, setSenderEmail] = useState<string>('')

  const dispatch = useDispatch()

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany } = globalSelector.company
  const { justInvited } = globalSelector.invitation

  /**
   * Destructuring the values from the props received
   */
  const { setShowCompanyCreationModal, setDataUpdate, onInviteSuccess } = props

  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [inputRef])
  /**
   * InvitationFormSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */
  const InvitationFormSchema = Yup.object().shape({
    firstName: Yup.string()
      .min(1, 'Too Short!')
      .max(50, 'Too Long!')
      .required('Required')
      .matches(onlyText, 'Enter Valid Name'),
    lastName: Yup.string().min(1, 'Too Short!').max(50, 'Too Long!').matches(onlyText, 'Enter Valid Name'),
    email: Yup.string().trimEmail().email('Invalid email').required('Required'),
  })

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async (submittedValues: InitialValues, { resetForm }: any) => {
    try {
      // if (Object.keys(currentCompany).length > 0) {
      setLoading(true)
      let data: any = { ...submittedValues }
      delete data.email
      data = {
        ...data,
        recipientEmail: submittedValues.email.trim(),
        senderEmail,
        // hireDate: submittedValues.hireDate ? startOfDate(submittedValues.hireDate) : undefined,
      }

      const response = await sendInvitation(data)

      if (isSuccess(response)) {
        notify('Invitation Sent Successfully', 'success')
        setShowCompanyCreationModal(false)
        resetForm()
        setDataUpdate((prev) => !prev)
        setLoading(false)
        onInviteSuccess?.(response?.data?.data?.id)
        dispatch(setJustInvited(!justInvited))
      } else {
        const errorMessage = Array?.isArray(response?.data?.message)
          ? response?.data?.message?.join(',')
          : response?.data?.message
        notify(errorMessage, 'error')
        setLoading(false)
      }
      // }
    } catch (error) {
      console.error('InvitationForm handleSubmit error', error)
      setLoading(false)
    }
  }

  const getSendersDetails = async () => {
    try {
      const response = await getProfileInfo()
      if (isSuccess(response)) {
        let user = response?.data?.data?.user
        setSenderEmail(user.email)
      } else {
        notify(response?.data?.message, 'error')
      }
    } catch (error) {
      console.error('getDetails error', error)
    }
  }

  useEffect(() => {
    getSendersDetails()
  }, [])

  return (
    <Styled.InvitationFormContainer>
      <Formik
        initialValues={initialValues}
        onSubmit={handleSubmit}
        validationSchema={InvitationFormSchema}
        validateOnChange={true}
        validateOnBlur={false}
      >
        {({ touched, errors, resetForm, handleChange, values, setFieldValue }) => {
          return (
            <>
              <SharedStyled.FlexCol alignItems="center">
                <Styled.ModalHeaderContainer style={{ width: '100%' }}>
                  <SharedStyled.FlexRow>
                    <img src={UnitSvg} alt="modal icon" />
                    <SharedStyled.FlexCol alignItems="center">
                      <Styled.ModalHeader>Invite New Member</Styled.ModalHeader>
                    </SharedStyled.FlexCol>
                  </SharedStyled.FlexRow>

                  <Styled.CrossContainer
                    onClick={() => {
                      setShowCompanyCreationModal(false)
                      resetForm()
                    }}
                  >
                    <CrossIcon />
                  </Styled.CrossContainer>
                </Styled.ModalHeaderContainer>
                <SharedStyled.Text fontSize="18px">Step 1</SharedStyled.Text>
              </SharedStyled.FlexCol>
              <SharedStyled.SettingModalContentContainer>
                <Form className="form">
                  <SharedStyled.Content
                    maxWidth="706px"
                    width="100%"
                    disableBoxShadow={true}
                    noPadding={true}
                    gap="8px"
                  >
                    <SharedStyled.FlexRow alignItems="flex-start">
                      <InputWithValidation
                        labelName="First Name*"
                        stateName="firstName"
                        error={touched.firstName && errors.firstName ? true : false}
                        passRef={inputRef}
                      />
                      <InputWithValidation
                        labelName="Preferred Name"
                        stateName="preferredName"
                        error={touched.preferredName && errors.preferredName ? true : false}
                      />
                    </SharedStyled.FlexRow>
                    <InputWithValidation
                      labelName="Last Name"
                      stateName="lastName"
                      error={touched.lastName && errors.lastName ? true : false}
                    />
                    <InputWithValidation
                      labelName="Email*"
                      stateName="email"
                      error={touched.email && errors.email ? true : false}
                    />

                    <SharedPhone
                      labelName="Phone"
                      stateName="phone"
                      value={values.phone}
                      onChange={handleChange('phone')}
                      error={touched.phone && errors.phone ? true : false}
                    />

                    {/* <SharedDate
                      value={values.hireDate}
                      labelName="Hire Date"
                      stateName="hireDate"
                      setFieldValue={setFieldValue}
                      error={touched.hireDate && errors.hireDate ? true : false}
                    /> */}

                    <SharedStyled.ButtonContainer marginTop="26px">
                      <Button type="submit" isLoading={loading}>
                        Send Invitation
                      </Button>
                    </SharedStyled.ButtonContainer>
                  </SharedStyled.Content>
                </Form>
              </SharedStyled.SettingModalContentContainer>
            </>
          )
        }}
      </Formik>
    </Styled.InvitationFormContainer>
  )
}
