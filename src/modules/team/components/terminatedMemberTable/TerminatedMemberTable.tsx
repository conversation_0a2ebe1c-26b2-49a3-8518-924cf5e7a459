import * as Styled from './style'
import * as SharedStyled from '../../../../styles/styled'
import { Table } from '../../../../shared/table/Table'
import { useCallback, useMemo, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { dayjsFormat, formatDate, getDataFromLocalStorage, isSuccess, notify } from '../../../../shared/helpers/util'
import { getTeamMembers } from '../../../../logic/apis/team'
import { useNavigate, useParams } from 'react-router-dom'
import { CustomModal } from '../../../../shared/customModal/CustomModal'
import DeletedModal from '../../../deleted/components/deletedModal/DeletedModal'
import { deleteMember, restoreMember } from '../../../../logic/apis/company'
import ProfileInfo from '../../../../shared/components/profileInfo/ProfileInfo'
import { StorageKey } from '../../../../shared/helpers/constants'

const TerminatedMemberTable = () => {
  const [loading, setLoading] = useState<boolean>(false)
  // const [pageCount, setPageCount] = useState<number>(10)
  const [data, setData] = useState<any>([])
  const [inputData, setInputData] = useState<any>()
  const [deletedModal, setDeletedModal] = useState(false)
  const fetchIdRef = useRef(0)

  const [btnLoadingRestore, setBtnLoadingRestore] = useState(false)
  const [btnLoadingDelete, setBtnLoadingDelete] = useState(false)

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company
  const navigate = useNavigate()
  const loadMoreRef = useRef(null)

  const fetchData = useCallback(async ({ pageIndex, pageSize, search }: any) => {
    try {
      setLoading(true)
      // This will get called when the table needs new data

      let receivedData: any = []
      let currentCompanyData: any = localStorage.getItem('currentCompany')

      const statusResponse = await getTeamMembers({
        skip: pageIndex,
        limit: pageSize,
        deleted: true,
        search,
      })

      if (isSuccess(statusResponse)) {
        let statusRes = statusResponse?.data?.data?.memberData

        statusRes.forEach((res: any, index: number) => {
          receivedData.push({
            name: res.name,
            // username: res.username,
            email: res.email,
            id: res._id,
            memberId: res._id,
            managerId: res?.managerId ? res?.managerId : '0',
            position: res?.positionName ? res?.positionName : '0',
            terminateDate: res?.terminateDate,
            hireDate: res?.hireDate,
            terminatedReason: res?.message,
            phone: res?.phone,
          })
        })
      } else {
        notify(statusResponse?.data?.message, 'error')
      }

      // Give this fetch an ID
      const fetchId = ++fetchIdRef.current

      // Set the loading state

      // setData(receivedData)

      // We'll even set a delay to simulate a server here
      // setTimeout(() => {
      // Only update the data if this is the latest fetch
      if (fetchId === fetchIdRef.current) {
        const startRow = pageSize * pageIndex
        const endRow = startRow + pageSize
        setData(receivedData.slice(startRow, endRow))

        // Your server could send back total page count.
        // For now we'll just fake it, too
        // setPageCount(Math.ceil(receivedData.length / pageSize))

        // setLoading(false)
      }
      // }, 1000)
    } catch (error) {
      console.error('TeamTable fetchData error', error)
    } finally {
      setLoading(false)
    }
  }, [])

  const columns: any = useMemo(
    () => [
      {
        Header: 'Name',
        accessor: 'name', // accessor is the "key" in the data
        Cell: (props: any) => (
          <ProfileInfo
            data={{
              ...props?.row?.original,
              id: props?.row?.original?.id,
              users: {
                firstName: props?.row?.original?.name?.split('  ')?.[0],
                lastName: props?.row?.original?.name?.split('  ')?.[1] || '',
              },
            }}
            showImagePlaceholder
          />
        ),
      },
      // {
      //   Header: 'Username',
      //   accessor: 'username',
      // },

      {
        Header: 'Terminated At',
        accessor: 'terminateDate',
        Cell: (props: any) => dayjsFormat(props?.row?.original?.terminateDate, 'M/D/YY'),
      },
    ],
    []
  )

  const onRestore = async () => {
    setBtnLoadingRestore(true)
    try {
      const res = await restoreMember({ memberId: inputData?.memberId ?? '' })
      if (isSuccess(res)) {
        notify(`Restored member!`, 'success')
        setDeletedModal(false)
        setInputData(null)
        fetchData({
          pageIndex: 0,
          pageSize: 20,
        })
        setBtnLoadingRestore(false)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      setBtnLoadingRestore(false)

      console.error('Unit err', err)
    }
  }
  const onDelete = async () => {
    setBtnLoadingDelete(true)

    try {
      const res = await deleteMember({ memberId: inputData?.memberId ?? '' })
      if (isSuccess(res)) {
        notify(`Deleted member!`, 'success')
        setDeletedModal(false)
        setInputData(null)
        fetchData({
          pageIndex: 0,
          pageSize: 20,
        })
        setBtnLoadingDelete(false)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      notify('Failed to delete member!', 'error')
      console.error('member delete err', err)
      setBtnLoadingDelete(false)
    }
  }
  return (
    <Styled.TerminatedCont>
      <Table
        columns={columns}
        data={data}
        loading={loading}
        // pageCount={pageCount}
        fetchData={fetchData}
        onRowClick={(value) => {
          setInputData(value)
          setDeletedModal(true)
        }}
        isLoadMoreLoading={loading}
        ref={loadMoreRef}
      />

      <CustomModal show={deletedModal}>
        <DeletedModal
          isTeamSection
          onDelete={onDelete}
          onRestore={onRestore}
          onClose={() => {
            setDeletedModal(false)
            setInputData(null)
          }}
          inputData={inputData}
          btnLoadingDelete={btnLoadingDelete}
          btnLoadingRestore={btnLoadingRestore}
          title="Terminated Member"
        />
      </CustomModal>
    </Styled.TerminatedCont>
  )
}

export default TerminatedMemberTable
