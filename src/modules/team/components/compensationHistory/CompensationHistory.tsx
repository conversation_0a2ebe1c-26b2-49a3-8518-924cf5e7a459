import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useLocation, useNavigate, useParams } from 'react-router-dom'
import { getCompensationHistory } from '../../../../logic/apis/compensation'
import { PER_OBJ, StorageKey } from '../../../../shared/helpers/constants'
import { formatNumberToCommaS, getDataFromLocalStorage, isSuccess, notify } from '../../../../shared/helpers/util'
import * as SharedStyled from '../../../../styles/styled'
import { Table } from '../../../../shared/table/Table'
import * as Styled from './style'
import Button from '../../../../shared/components/button/Button'
import { CustomModal } from '../../../../shared/customModal/CustomModal'
import DeletedModal from '../../../deleted/components/deletedModal/DeletedModal'

interface I_Data {
  effectiveDate: string
  wage: string
  pwRate: string
  commission: string
  paySchedule: string
  changeReason: string
}

interface I_Column {
  name: string
  // username: string
  email: string
}

const CompensationHistory = () => {
  const [skipPageReset, setSkipPageReset] = useState(false)
  const [loading, setLoading] = useState<boolean>(false)
  const [pageCount, setPageCount] = useState<number>(10)
  const [data, setData] = useState<I_Data[]>([])
  const [allData, setAllData] = useState([])
  const fetchIdRef = useRef(0)
  const [showDetailModal, setShowDetailModal] = useState(false)
  const [selectedData, setSelectedData] = useState<any>()
  const { state } = useLocation()

  const allPayScheduleData = state?.allPayScheduleData
  const pwVersions = state?.pwVersions

  const { memberId } = useParams()
  const navigate = useNavigate()

  const columns: any = useMemo(
    () => [
      {
        Header: 'Effective Date',
        accessor: 'effectiveDate', // accessor is the "key" in the data
      },
      {
        Header: 'Wage',
        accessor: 'wage',
      },
      // {
      //   Header: 'PW Rate',
      //   accessor: 'pwRate',
      // },
      // {
      //   Header: 'Commission',
      //   accessor: 'commission',
      // },
      // {
      //   Header: 'Pay Schedule',
      //   accessor: 'paySchedule',
      // },
      {
        Header: 'Change Reason',
        accessor: 'changeReason',
      },
    ],
    []
  )

  const EditableCell = ({
    value: initialValue,
    row: { index },
    column: { id },
    updateMyData, // This is a custom function that we supplied to our table instance
  }: any) => {
    // We need to keep and update the state of the cell normally
    const [value, setValue] = useState(initialValue)

    const onChange = (e: any) => {
      setValue(e.target.value)
    }

    // We'll only update the external data when the input is blurred
    const onBlur = () => {
      updateMyData(index, id, value)
    }

    // If the initialValue is changed external, sync it up with our state
    useEffect(() => {
      setValue(initialValue)
    }, [initialValue])

    return <Styled.EditableInput value={value} onChange={onChange} onBlur={onBlur} />
  }

  // Set our editable cell renderer as the default Cell renderer
  const defaultColumn = {
    Cell: EditableCell,
  }

  const fetchData = useCallback(async ({ pageSize, pageIndex }: any) => {
    try {
      setLoading(true)
      // This will get called when the table needs new data
      let receivedData: any = []
      let currentCompanyData: any = getDataFromLocalStorage('currentCompany')
      const statusResponse = await getCompensationHistory({
        skip: pageIndex,
        limit: pageSize,
        type: 1,
        memberId: memberId!,
      })

      if (isSuccess(statusResponse)) {
        let statusRes = statusResponse?.data?.data?.compensation?.wageHistory
        setAllData(statusResponse?.data?.data?.compensation?.wageHistory)
        statusRes.forEach((res: any) => {
          receivedData.push({
            ...res,
            effectiveDate: res?.effectivePayPeriod?.slice(0, 10),
            wage:
              res?.wageAmount && res?.wageInterval
                ? `$${Number(res?.wageAmount)?.toFixed(2)}/${PER_OBJ[res?.wageInterval]}`
                : '-',
            pwRate: res?.pieceWorkHourlyRate ? `$${res?.pieceWorkHourlyRate}/hour` : '-',
            commission: res?.saleCommission ? `${res?.saleCommission}%` : `-`,
            paySchedule: res?.payScheduleName ? res?.payScheduleName : '-',
            changeReason: res?.reason ? res?.reason : '-',
            modifiedAt: res?.modifiedAt,
          })
        })
      } else {
        notify(statusResponse?.data?.message, 'error')
      }
      // Give this fetch an ID

      // Set the loading state

      // We'll even set a delay to simulate a server here
      // setTimeout(() => {
      // Only update the data if this is the latest fetch

      setData(receivedData)

      // Your server could send back total page count.
      // For now we'll just fake it, too

      // }, 1000)
    } catch (error) {
      console.error('Compensation History fetchData error', error)
    } finally {
      setLoading(false)
    }
  }, [])

  // We need to keep the table from resetting the pageIndex when we
  // Update data. So we can keep track of that flag with a ref.

  // When our cell renderer calls updateMyData, we'll use
  // the rowIndex, columnId and new value to update the
  // original data
  const updateMyData = (rowIndex: any, columnId: any, value: any) => {
    // We also turn on the flag to not reset the page
    setSkipPageReset(true)
    setData((old) =>
      old.map((row, index) => {
        if (index === rowIndex) {
          return {
            ...old[rowIndex],
            [columnId]: value,
          }
        }
        return row
      })
    )
  }

  // After data chagnes, we turn the flag back off
  // so that if data actually changes when we're not
  // editing it, the page is reset
  useEffect(() => {
    setSkipPageReset(false)
  }, [data])

  // Let's add a data resetter/randomizer to help
  // illustrate that flow...
  const resetData = () => {
    fetchData({ pageSize: 10, pageIndex: 0 })
  }

  return (
    <>
      <Styled.CompensationHistoryDiv>
        <SharedStyled.SectionTitle>Compensation History</SharedStyled.SectionTitle>
        <Table
          columns={columns}
          data={data}
          loading={loading}
          // pageCount={pageCount}
          fetchData={fetchData}
          searchPlaceholder="Search Wage.."
          onRowClick={(row) => {
            setSelectedData(row)
            setShowDetailModal(true)
          }}
        />

        <Button type="button" width="max-content" onClick={() => navigate(-1)}>
          Back
        </Button>
      </Styled.CompensationHistoryDiv>

      <CustomModal show={showDetailModal}>
        <DeletedModal
          isTeamSection
          noButton
          onDelete={() => {}}
          onRestore={() => {}}
          onClose={() => {
            setShowDetailModal(false)
          }}
          title="Compensation detail"
        >
          <Styled.InfoCont gap="16px">
            <SharedStyled.FlexRow>
              <p>Position:</p> <span>{selectedData?.positionName}</span>
            </SharedStyled.FlexRow>
            <SharedStyled.FlexRow>
              <p>Pay Schedule:</p>{' '}
              <span>
                {allPayScheduleData?.find((itm: { _id: string }) => itm?._id === selectedData?.payScheduleId)?.name}
              </span>
            </SharedStyled.FlexRow>

            <SharedStyled.FlexRow>
              <p>Effective Date:</p>{' '}
              <span>{selectedData?.effectivePayPeriod?.slice(0, 10) || selectedData?.effectiveDate}</span>
            </SharedStyled.FlexRow>
            <SharedStyled.FlexRow>
              <p>Base Wage:</p>{' '}
              <span>
                ${formatNumberToCommaS(selectedData?.wageAmount)}/{PER_OBJ[selectedData?.wageInterval]}
              </span>
            </SharedStyled.FlexRow>
            {selectedData?.ownPieceWork ? (
              <SharedStyled.FlexRow>
                <p>Self Bonus:</p> <span>{selectedData?.ownPieceWork * 100}%</span>
              </SharedStyled.FlexRow>
            ) : null}
            {selectedData?.crewPieceWork ? (
              <SharedStyled.FlexRow>
                <p>Crew Bonus:</p> <span>{selectedData?.crewPieceWork * 100}%</span>
              </SharedStyled.FlexRow>
            ) : null}

            {selectedData?.versionId ? (
              <SharedStyled.FlexRow>
                <p>Piece Work version:</p>{' '}
                <span style={{ textTransform: 'capitalize' }}>
                  {pwVersions?.find((itm: { _id: string }) => itm?._id === selectedData?.versionId)?.name}
                </span>
              </SharedStyled.FlexRow>
            ) : null}
            {selectedData?.pieceWorkHourlyRate ? (
              <SharedStyled.FlexRow>
                <p>Piece Work Hourly Rate:</p>{' '}
                <span>
                  ${formatNumberToCommaS(selectedData?.pieceWorkHourlyRate)}/{PER_OBJ[selectedData?.wageInterval]}
                </span>
              </SharedStyled.FlexRow>
            ) : null}
            {selectedData?.saleCommission ? (
              <SharedStyled.FlexRow>
                <p>Sales Commission:</p> <span>{selectedData?.saleCommission * 100}%</span>
              </SharedStyled.FlexRow>
            ) : null}
            {selectedData?.selfLead ? (
              <SharedStyled.FlexRow>
                <p>Self Gen Commission:</p> <span>{selectedData?.selfLead * 100}%</span>
              </SharedStyled.FlexRow>
            ) : null}
            <SharedStyled.FlexRow>
              <p>Change Reason:</p> <span>{selectedData?.reason}</span>
            </SharedStyled.FlexRow>
            {/* <p>Modified At: {selectedData?.modifiedAt}</p> */}
          </Styled.InfoCont>
        </DeletedModal>
      </CustomModal>
    </>
  )
}

export default CompensationHistory
