import { useCallback, useMemo, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate, useParams } from 'react-router-dom'

import { ResendIcon } from '../../../../assets/icons/ResendIcon'
import { RevokeIcon } from '../../../../assets/icons/RevokeIcon'
import { resendInvitation, revokeInvitation } from '../../../../logic/apis/invitation'
import { getInvitationStatus } from '../../../../logic/apis/team'
import { dayjsFormat, getDataFromLocalStorage, getStatus, isSuccess, notify } from '../../../../shared/helpers/util'
import { Table } from '../../../../shared/table/Table'
import * as SharedStyled from '../../../../styles/styled'
import * as Styled from './style'
import ProfileInfo from '../../../../shared/components/profileInfo/ProfileInfo'
import { StorageKey } from '../../../../shared/helpers/constants'
import { CustomModal } from '../../../../shared/customModal/CustomModal'
import { CompensationModal } from '../teamMember/components/CompensationModal/CompensationModal'

/**
 * I_InvitationStatus is an interface which defines the props type which we receive from our parent component to this component
 */
interface I_InvitationStatus {
  dataUpdate: boolean
  setDataUpdate: React.Dispatch<React.SetStateAction<boolean>>
  companyData?: any
  setInviteUpdate: React.Dispatch<React.SetStateAction<boolean>>
  inviteUpdate: boolean
}

interface I_ResendInvitation {
  _id: string
  email: string
  company: string
  status: number
}
interface I_RevokeInvitation {
  _id: string
  email: string
  company: string
  status: number
}

interface I_Data {
  name: string
  // username: string
  email: string
}

interface I_Column {
  Header: string
  accessor: string
}

const InvitationStatus = (props: I_InvitationStatus) => {
  const [loading, setLoading] = useState<boolean>(false)
  const [actionLoading, setActionLoading] = useState<boolean>(false)
  // const [pageCount, setPageCount] = useState<number>(10)
  const [data, setData] = useState<I_Data[]>([])
  const fetchIdRef = useRef(0)
  const loadMoreRef = useRef(null)

  const { dataUpdate, setDataUpdate, companyData, setInviteUpdate, inviteUpdate } = props
  const [showCompensationModal, setCompensationModal] = useState<boolean>(false)
  const [selectedInviteId, setSelectedInviteId] = useState('')
  const [compensationData, setCompensationData] = useState<any>({})

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany } = globalSelector.company
  const navigate = useNavigate()

  const fetchData = useCallback(
    async ({ pageSize, pageIndex, search }: any) => {
      try {
        setLoading(true)
        let receivedData: any = []

        const statusResponse = await getInvitationStatus({ skip: pageIndex, limit: pageSize, deleted: false, search })

        if (isSuccess(statusResponse)) {
          let statusRes = statusResponse?.data?.data?.invitations?.sort(
            (a: { createdAt: string }, b: { createdAt: string }) =>
              new Date(b?.createdAt)?.getTime() - new Date(a?.createdAt)?.getTime()
          )

          statusRes.forEach((res: any) => {
            let status = getStatus(res.status)
            let dataObj = {
              _id: res._id,
              email: res.email,
              company: res.company,
              status: res.status,
            }

            receivedData.push({
              ...res,
              name: `${res.firstName} ${res?.preferredName ? `"${res?.preferredName}"` : ''} ${
                res?.lastName ? res?.lastName : ''
              }`?.trim(),
              sent: res.createdAt.slice(0, 10),
              email: res.email,
              senderEmail: res?.senderEmail,
              status: status,
              link: status !== 'Accepted',
              id: 1,
              actions:
                status === 'Accepted' ? (
                  '-'
                ) : (
                  <SharedStyled.FlexBox width="100%" alignItems="center" gap="10px">
                    {status !== 'Rejected' && status !== 'Revoked' && (
                      <>
                        <SharedStyled.TooltipContainer className="crew">
                          <span className="tooltip-content">Revoke invite</span>
                          <Styled.IconContainer
                            className="revoke"
                            content="Revoke"
                            onClick={(e: any) => {
                              e.stopPropagation()
                              revokeFunction(dataObj)
                            }}
                          >
                            <RevokeIcon />
                          </Styled.IconContainer>
                        </SharedStyled.TooltipContainer>
                      </>
                    )}
                    <>
                      <SharedStyled.TooltipContainer className="crew">
                        <span className="tooltip-content">Resend invite</span>
                        <Styled.IconContainer
                          className="resend"
                          content="Resend"
                          onClick={(e: any) => {
                            e.stopPropagation()
                            resendFunction(dataObj)
                          }}
                        >
                          <ResendIcon />
                        </Styled.IconContainer>
                      </SharedStyled.TooltipContainer>
                    </>
                  </SharedStyled.FlexBox>
                ),
            })
          })
        } else {
          notify(statusResponse?.data?.message, 'error')
        }
        // const fetchId = ++fetchIdRef.current

        // if (fetchId === fetchIdRef.current) {
        //   const startRow = pageSize * pageIndex
        //   const endRow = startRow + pageSize
        // }
        setData(receivedData)
      } catch (error) {
        console.error('InvitationStatus fetchData error', error)
      } finally {
        setLoading(false)
      }
    },
    [dataUpdate, inviteUpdate]
  )

  const columns: any = useMemo(
    () => [
      {
        Header: 'Name',
        accessor: 'name', // accessor is the "key" in the data
        Cell: (props: any) => {
          const lastName = props?.row?.original?.name?.split(' ')?.[1]?.includes('"')
            ? props?.row?.original?.name?.split(' ')?.[1]?.replace('"', '')
            : props?.row?.original?.name?.split('  ')?.[1]

          return (
            <ProfileInfo
              data={{
                ...props?.row?.original,
                id: props?.row?.original?._id,
                users: {
                  firstName: props?.row?.original?.name?.split(' ')?.[0],
                  lastName: lastName || '',
                },
              }}
              showImagePlaceholder
            />
          )
        },
      },
      {
        Header: 'Sender email',
        accessor: 'senderEmail', // accessor is the "key" in the data
      },
      {
        Header: 'Sent',
        accessor: 'sent',
        Cell: (props: any) => dayjsFormat(props?.row?.original?.sent, 'M/D/YY'),
      },
      {
        Header: 'Status',
        accessor: 'status',
      },
      {
        Header: 'Actions',
        accessor: 'actions',
      },
    ],
    []
  )

  const revokeFunction = async (data: I_ResendInvitation) => {
    try {
      setActionLoading(true)
      const response = await revokeInvitation({ ...data })

      if (isSuccess(response)) {
        setDataUpdate((prev) => !prev)
        setActionLoading(false)

        notify('Successfully revoked', 'success')
      } else {
        notify(response?.data?.message, 'error')
        setActionLoading(false)
      }
    } catch (error) {
      console.error('revokeFunction error', error)
      setActionLoading(false)
    }
  }

  const resendFunction = async (data: I_RevokeInvitation) => {
    try {
      const response = await resendInvitation({ ...data })

      if (isSuccess(response)) {
        setDataUpdate((prev) => !prev)
        notify('Successfully resent the invitation', 'success')
      } else {
        notify(response?.data?.message, 'error')
      }
    } catch (error) {
      console.error('Resend function error', error)
    }
  }

  return (
    <>
      <Table
        columns={columns}
        data={data}
        invite
        loading={loading}
        // pageCount={pageCount}
        fetchData={fetchData}
        onRowClick={(val) => {
          if (val?.status === 'Accepted') {
            return
          }
          setSelectedInviteId(val?._id)
          setCompensationData(val)
          setCompensationModal(true)
          // navigate(`/team/member/${val.id}/${val.memberId}/${val.managerId}`)
        }}
        ref={loadMoreRef}
        isLoadMoreLoading={loading}
      />

      <CustomModal show={showCompensationModal}>
        <CompensationModal
          setCompensationModal={setCompensationModal}
          noData={false}
          setDataUpdated={setDataUpdate}
          isEdit={false}
          compensationData={{
            ...compensationData?.wage,
            managerId: compensationData?.managerId,
            departmentId: compensationData?.departmentId,
          }}
          hireDateCompensation={compensationData?.hireDate}
          pwVersions={companyData?.versions}
          isInviteModal
          commissionPosition={companyData?.salesPositions}
          pwPosition={companyData?.pieceWorkPositions}
          selectedInviteId={selectedInviteId}
          setInviteUpdate={setInviteUpdate}
        />
      </CustomModal>
    </>
  )
}

export default InvitationStatus
