import { Field, Form, Formik } from 'formik'
import { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate, useParams } from 'react-router-dom'
import * as Yup from 'yup'
import { deleteMember, getDirectReport, terminateMember, updateCompanyMemberInfo } from '../../../../logic/apis/company'
import { deleteCompensation, getCompensation } from '../../../../logic/apis/compensation'
import { getDepartments } from '../../../../logic/apis/department'
import { getProfileInfoById, updateProfile } from '../../../../logic/apis/profile'
import {
  getTeamMemberDetail,
  getTeamMemberRole,
  getTeamMembers,
  updateTeamMemberRole,
} from '../../../../logic/apis/team'
import { CustomModal } from '../../../../shared/customModal/CustomModal'
import CustomSelect from '../../../../shared/customSelect/CustomSelect'
import { SharedDate } from '../../../../shared/date/SharedDate'
import { Dropdown } from '../../../../shared/dropDown/Dropdown'
import {
  Nue,
  PER_OBJ,
  PERIOD_OBJ,
  PERIOD_OBJ1,
  ROLES,
  ROLES_NAME,
  ROLES_OBJ,
  StorageKey,
} from '../../../../shared/helpers/constants'
import { onlyMmDdYyyy, onlyNumber, onlyText, onlyUsNumber, twoDecimal } from '../../../../shared/helpers/regex'
import {
  camelCaseToSentenceCase,
  dayjsFormat,
  endOfDate,
  formatDateymd,
  formatNumberToCommaS,
  getDataFromLocalStorage,
  getDigitsFromPhone,
  getFormattedDate,
  getNameFrom_Id,
  isSuccess,
  notify,
  startOfDate,
} from '../../../../shared/helpers/util'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import { KeyValue } from '../../../../shared/keyValue/KeyValue'
import { SharedPhone } from '../../../../shared/sharedPhone/SharedPhone'
import * as SharedStyled from '../../../../styles/styled'
import { colors } from '../../../../styles/theme'
import { CompensationModal, getPayPeriods2NHR } from './components/CompensationModal/CompensationModal'
import TerminateModal from './components/CompensationModal/TerminateModal'
import * as Styled from './style'
import { SettingsCont } from '../../../units/style'
import Button from '../../../../shared/components/button/Button'
import { getCompanyPayData } from '../../../../logic/apis/pieceWork'
import { IVersion } from '../../../pieceWorkSettingsSalariedCrew/components/AddPieceworkVersion/AddPieceworkVersion'
import '../../../../shared/helpers/yupExtension'
import Toggle from '../../../../shared/toggle/Toggle'
import Modal from '../../../../shared/customModal/Modal'
import { Title } from '../../../subscription/components/planCard/style'
import { Info } from '../../../auth/resetPassword/style'
import { getPTODates } from '../../../../logic/apis/approveTimeCard'
import TimeOffBreakdown from './components/TimeOffBreakdown'
import DeletedModal from '../../../deleted/components/deletedModal/DeletedModal'
import { getPaySchedule } from '../../../../logic/apis/paySchedule'
import useFetch from '../../../../logic/apis/useFetch'
import { getSubContractors } from '../../../../logic/apis/subcontractor'

/**
 * InitialValues is an interface declared here so that it can be used as a type for the useState hook
 */
interface InitialValues {
  firstName: string
  lastName: string
  notes: string
  preferredName: string
  mobileAccess?: boolean
  // username: string
  email: string
  phone: string
  DOB: string
  hireDate: any
  department: string
  manager: string
  // roles: string
  // checkRoles: object
  reasonForChange: string
}

/**
 *
 * @returns A TeamMember component with all the validations to its input fields
 */
const TeamMember = () => {
  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */
  let currentDate: any = new Date()

  const [initialValues, setInitialValues] = useState<InitialValues>({
    firstName: '',
    lastName: '',
    notes: '',
    preferredName: '',
    mobileAccess: false,
    // username: '',
    email: '',
    phone: '',
    DOB: '',
    hireDate: formatDateymd(currentDate) + '',
    department: '',
    manager: '',
    // roles: '',
    // checkRoles: [],
    reasonForChange: '',
  })
  const [currentUserRole, setCurrentUserRole] = useState('')
  const [showCompensationModal, setCompensationModal] = useState<boolean>(false)
  /**
   * loading will be the loading state when performing the operations
   */
  const [loading, setLoading] = useState<boolean>(false)
  const [terminateLoading, setTerminateLoading] = useState<boolean>(false)
  const [deleteLoading, setDeleteLoading] = useState<boolean>(false)
  const [isEdit, setIsEdit] = useState<boolean>(true)
  const [pwVersions, setPwVersions] = useState<IVersion[]>([])
  const [showConfirmModal, setShowConfirmModal] = useState(false)
  const [pwCommissionPosition, setPwCommissionPosition] = useState<Record<string, string[]>>({
    pieceWorkPositions: [],
    salesPositions: [],
  })

  const [noData, setNoData] = useState<boolean>(true)
  const { data: allSubcontractorData } = useFetch({
    fetchFn: () => getSubContractors({ deleted: false, retired: false, limit: '200', active: true }),
  })

  const [futureCompentasion, setFutureCompentasion] = useState<boolean>(false)
  const [compensationData, setCompensationData] = useState<any>({})
  const [dataUpdated, setDataUpdated] = useState<boolean>(false)
  const [terminateModal, setTerminateModal] = useState<boolean>(false)
  const [teamMember, setTeamMember] = useState<any>([])
  const [hireDateCompensation, sethireDateCompensation] = useState('')
  const [currentPosition, setCurrentPosition] = useState<any>('')
  const [positionText, setPositionText] = useState('')
  const [upcomingPosition, setUpcomingPosition] = useState<any>({})
  const [managerData, setManagerData] = useState<any>([])
  const [managerIdData, setManagerIdData] = useState<any>({})
  const [departmentData, setDepartmentData] = useState<any>([])
  const [departmentIdData, setDepartmentIdData] = useState<any>({})
  const [directReportsData, setDirectReportsData] = useState<any>([])
  const [shimmerLoading, setShimmerLoading] = useState<boolean>(true)
  const [ptoData, setPtoData] = useState([])
  const [allCompensationData, setAllCompensationData] = useState<any>(null)
  const [showCancelConfirmModal, setShowCancelConfirmModal] = useState(false)
  const [selectedRange, setSelectedRange] = useState<any>({})

  const isEffectivePayPeriodOver = new Date()?.getTime() >= new Date(selectedRange?.periodPayday)?.getTime()

  const [payScheduleDetail, setPayScheduleDetail] = useState<any>([])
  const [allPayScheduleData, setAllPayScheduleData] = useState([])

  const navigate = useNavigate()
  let { memberUserId, memberId, managerId } = useParams()

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember, position } = globalSelector.company
  const { profileInfo } = globalSelector.auth
  const userId = getDataFromLocalStorage(StorageKey.id)
  /**
   * TeamMemberSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */
  const TeamMemberSchema = Yup.object().shape({
    firstName: Yup.string()
      .min(1, 'Too Short!')
      .max(50, 'Too Long!')
      .required('Required')
      .matches(onlyText, 'Enter Valid Name'),
    lastName: Yup.string()
      .min(1, 'Too Short!')
      .max(50, 'Too Long!')
      // .required('Required')
      .matches(onlyText, 'Enter Valid Name'),
    preferredName: Yup.string().min(2, 'Too Short!').max(50, 'Too Long!').matches(onlyText, 'Enter Valid Name'),
    // username: Yup.string().min(2, 'Too Short!').max(50, 'Too Long!').required('Required'),
    email: Yup.string().trimEmail().email('Invalid email').required('Required'),
    phone: Yup.string(),
    pieceWorkHourlyRate: Yup.string().matches(onlyNumber, 'Invalid'),
    salesCommision: Yup.string().matches(onlyNumber, 'Invalid'),
    wage: Yup.string().matches(twoDecimal, 'Only numbers with upto two decimals are allowed'),
    per: Yup.string(),
    paySchedule: Yup.string(),
    effectivePayPeriod: Yup.string(),
    hireDate: Yup.string(),
    position: Yup.string(),
    department: Yup.string(),
    manager: Yup.string(),
    // roles: Yup.string(),
    // checkRoles: Yup.array().of(Yup.boolean().required('This field is required')),
    reasonForChange: Yup.string(),
  })

  const TempRolesData = [
    {
      id: 1,
      role: 'Admin',
      check: false,
    },
    {
      id: 2,
      role: 'Manager',
      check: false,
    },
    {
      id: 3,
      role: 'Crew Member',
      check: false,
    },
    {
      id: 4,
      role: 'Crew Lead',
      check: true,
    },
  ]

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleUpdateProfile = async (submittedValues: InitialValues) => {
    try {
      const res = await updateProfile(submittedValues, memberUserId!)
      if (isSuccess(res)) {
        notify('Mobile access updated', 'success')
      } else {
        notify('Mobile access fail to update', 'error')
        window.location.reload()
      }
    } catch (error) {
      console.error({ error })
    }
  }

  const handleSubmit = async (submittedValues: InitialValues) => {
    try {
      // if (Object.keys(currentCompany).length > 0) {
      setLoading(true)
      let currentUserId: any = localStorage.getItem('id')
      let id: any = memberUserId
      let memId: any = memberId

      let phone = submittedValues.phone ? getDigitsFromPhone(submittedValues.phone) : undefined
      let profileData = {
        firstName: submittedValues.firstName,
        lastName: submittedValues.lastName,
        preferredName: submittedValues.preferredName ? submittedValues.preferredName : undefined,
        DOB: submittedValues?.DOB ? submittedValues?.DOB : undefined,
        // username: submittedValues.username,
        email: initialValues?.email?.trim() === submittedValues.email.trim() ? undefined : submittedValues.email.trim(),
        phone: phone,
      }

      let compInfo = {
        name: `${submittedValues.firstName} ${submittedValues.lastName}`,
        preferredName: profileData.preferredName,
        memberId: memId,
        notes: submittedValues?.notes ? submittedValues?.notes : '',
        hireDate: submittedValues.hireDate ? startOfDate(submittedValues.hireDate) : '',
        managerId: managerIdData[submittedValues.manager],
        departmentId: departmentIdData[submittedValues.department],

        email: submittedValues.email.trim(),
      }
      // let roleInfo = {
      //   _id: JSON.parse(currentUserId),
      //   memberId: memId,
      //
      //   role: Number(ROLES_OBJ[submittedValues.roles]),
      //   createdBy: currentMember._id,
      // }

      // const isRoleChanged = submittedValues?.roles !== currentUserRole

      let apiCalls

      // if (isRoleChanged) {
      //   apiCalls = [
      //     updateProfile(id, profileData, memberUserId!),
      //     updateCompanyMemberInfo(compInfo, JSON.parse(currentUserId)),
      //     // updateTeamMemberRole(roleInfo, JSON.parse(currentUserId)),
      //   ]
      // } else {
      apiCalls = [updateProfile(profileData, memberUserId!), updateCompanyMemberInfo(compInfo)]
      // }

      const [profileResponse, companyInfoResponse] = await Promise.all(apiCalls)

      const checkStatus = isSuccess(profileResponse) && isSuccess(companyInfoResponse)

      if (checkStatus) {
        getDetails()
        notify('Updated Data Successfully', 'success')
        setLoading(false)
        getSingleMemberDetail()
      } else {
        notify(profileResponse?.data?.message, 'error')
        notify(companyInfoResponse?.data?.message, 'error')
        // notify(roleInfoResponse?.data?.message, 'error')
        setLoading(false)
      }
      // }
    } catch (error) {
      console.error('TeamMember handleSubmit error', error)
      setLoading(false)
    }
  }

  const getDetails = async () => {
    try {
      setShimmerLoading(true)
      if (Object.keys(currentMember).length > 0) {
        let memId: any = memberId
        let currentUserId: any = localStorage.getItem('id')

        let id: any = memberUserId

        const apiCalls = [
          getProfileInfoById(id),
          getTeamMemberDetail({ memberId: memId }),
          getTeamMemberRole({ memberId: memId }),
          getTeamMemberRole({ memberId: currentMember._id }),
          getPTODates(memberId!),
        ]
        const [response, responseComp, responseRole, responseCurrentRole, ptoResponse] = await Promise.all(apiCalls)

        if (isSuccess(ptoResponse)) {
          setPtoData(ptoResponse?.data?.data?.timeOffList)
        }
        if (
          isSuccess(response) &&
          isSuccess(responseComp) &&
          isSuccess(responseRole) &&
          isSuccess(responseCurrentRole)
        ) {
          let user = response?.data?.data?.user
          let data = responseComp?.data?.data?.memberInfo
          let singleMemObj = responseRole?.data?.data?.role
          let singleRoleObj = responseCurrentRole?.data?.data?.role
          let compObj = {
            hireDate: data?.hireDate ? formatDateymd(data?.hireDate) : '',
            department: data?.departmentDetail[0]?.name ? data?.departmentDetail[0]?.name : '',
            manager: data?.managerName ? data?.managerName : '',
            notes: data?.notes || '',
          }
          let userObject = {
            firstName: user.firstName,
            lastName: user.lastName,
            mobileAccess: user.mobileAccess ?? false,
            notes: user.notes ?? '',
            // username: user.username,
            DOB: user.DOB || '',
            email: user.email,
            preferredName: user.preferredName,
            phone: `${user.phone}`,
          }
          sethireDateCompensation(data?.hireDate)

          setInitialValues({
            ...initialValues,
            ...userObject,
            ...compObj,
            // roles: ROLES[singleMemObj.role]
          })
          setCurrentUserRole(ROLES[singleRoleObj.role])
          setShimmerLoading(false)
        } else {
          notify(response?.data?.message, 'error')
          notify(responseComp?.data?.message, 'error')
          setShimmerLoading(false)
        }
      }
    } catch (error) {
      console.error('getDetails error', error)
      setShimmerLoading(false)
    }
  }

  const getCompensationInfo = async () => {
    try {
      let id: any = memberUserId
      let memId: any = memberId
      // if (Object.keys(currentCompany).length > 0) {
      const response: any = await getCompensation({ memberId: memId })

      if (isSuccess(response)) {
        let compensation: any = response?.data?.data?.compensation ?? {}
        setUpcomingPosition(response?.data?.data?.upcomingComp ?? {})

        if (compensation && Object.keys(compensation).length > 0) {
          setNoData(false)
          setAllCompensationData(compensation)
          setCurrentPosition(compensation?.position?.symbol)
          setPositionText(compensation?.position?.position)
          setCompensationData({
            wage: `${formatNumberToCommaS(compensation?.wageAmount)}/${PER_OBJ[
              compensation?.wageInterval
            ].toLowerCase()}`,
            paySchedule: compensation?.paySchedule?.name ? compensation?.paySchedule?.name : '',
            effectivePayPeriod: compensation?.effectivePayPeriod ? compensation?.effectivePayPeriod : '',
            salesCommision: compensation?.saleCommission ? `${compensation?.saleCommission * 100}%` : '',
            selfLead: compensation?.selfLead ? `${compensation?.selfLead * 100}%` : '',
            pieceWorkHourlyRate: compensation?.pieceWorkHourlyRate
              ? `${formatNumberToCommaS(compensation?.pieceWorkHourlyRate)}/hour`
              : '',
          })
        }
      } else {
        notify(response?.data?.message, 'error')
      }
      // }
    } catch (error) {
      console.error('getCompensationInfo error', error)
    }
  }

  const getTeamDetails = async () => {
    // if (Object.keys(currentMember).length > 0) {
    try {
      let id: any = memberUserId
      let memId: any = memberId
      const memberResponse = await getTeamMembers({ deleted: false, limit: 1000 })
      if (isSuccess(memberResponse)) {
        let memberObj: any = []
        let memberIdObj: any = {}
        let memberData = memberResponse?.data?.data?.memberData
        memberData.forEach((member: any) => {
          if (member._id !== memId) {
            memberObj.push(member.name)
            memberIdObj = { ...memberIdObj, [`${member.name}`]: member._id }
          }
        })

        setManagerData(memberObj)
        setManagerIdData(memberIdObj)
      } else {
        notify(memberResponse?.data?.message, 'error')
      }
    } catch (error) {
      console.error('getTeamDetails error', error)
    }
    // }
  }

  const getDepartmentDetails = async () => {
    // if (Object.keys(currentCompany).length > 0) {
    try {
      let id: any = memberUserId
      const departmentResponse = await getDepartments({ deleted: false }, false)
      if (isSuccess(departmentResponse)) {
        let departmentObj: any = []
        let departmentIdObj: any = {}
        let deptData = departmentResponse?.data?.data?.department
        deptData.forEach((dept: any) => {
          departmentObj.push(dept.name)
          departmentIdObj = { ...departmentIdObj, [dept.name]: dept._id }
        })
        setDepartmentData(departmentObj)
        setDepartmentIdData(departmentIdObj)
      } else {
        notify(departmentResponse?.data?.message, 'error')
      }
    } catch (error) {
      console.error('getTeamDetails error', error)
    }
    // }
  }

  const getPaySceduleInfo = async () => {
    try {
      // if (Object.keys(currentCompany).length > 0) {
      const response = await getPaySchedule({ deleted: false })
      if (isSuccess(response)) {
        let payScheduleNamePeriodObj: any = {}
        let paySchedule = response?.data?.data?.paySchedule

        paySchedule.forEach((pay: any) => {
          payScheduleNamePeriodObj = {
            ...payScheduleNamePeriodObj,
            [pay.name]: {
              period: PERIOD_OBJ1[pay.period],
              periodEnd: pay.payPeriodEndsOn
                ? new Date(pay.payPeriodEndsOn).toISOString().split('-').join('-').split('T')[0]
                : null,
              payDay: pay.paydayOn,
              periodEnd2: pay.payPeriodEndsOn2
                ? new Date(pay.payPeriodEndsOn2).toISOString().split('-').join('-').split('T')[0]
                : null,
              payDay2: pay.paydayOn2,
            },
          }
        })

        setPayScheduleDetail(payScheduleNamePeriodObj)
        setAllPayScheduleData(paySchedule)
      } else {
        notify(response?.data?.message, 'error')
      }
      // }
    } catch (error) {
      console.error('getPositionInfo error', error)
    }
  }

  const getCompanyInfoDetails = async () => {
    try {
      // if (Object.keys(currentCompany).length > 0) {
      let memId: any = memberId
      let id: any = memberUserId

      const response = await getTeamMemberDetail({ memberId: memId })
      if (isSuccess(response)) {
        let data = response?.data?.data?.memberInfo
        let compObj = {
          hireDate: data?.hireDate ? formatDateymd(data?.hireDate) : '',
          department: data?.departmentDetail[0]?.name ? data?.departmentDetail[0]?.name : '',
          manager: data?.managerName ? data?.managerName : '',
        }

        setInitialValues({ ...initialValues, ...compObj })

        getDetails()
      } else {
        notify(response?.data?.message, 'error')
        setTerminateLoading(false)
      }
      // }
    } catch (error) {
      console.error('getCompanyInfoDetails error', error)
    }
  }

  const getDirectReports = async () => {
    try {
      // if (Object.keys(currentCompany).length > 0) {
      let id: any = memberUserId
      let memId: any = memberId

      const response = await getDirectReport({ memberId: memId })
      if (isSuccess(response)) {
        let directReports = response?.data?.data?.directReports
        if (directReports.length > 0) {
          setDirectReportsData(directReports)
        } else {
          setDirectReportsData([])
        }
      } else {
        notify(response?.data?.message, 'error')
      }
      // }
    } catch (error) {
      console.error('getDirectReports error', error)
    }
  }

  const onTerminate = async (msg: string, date: string) => {
    setTerminateLoading(true)
    try {
      // if (Object.keys(currentCompany).length > 0) {
      let id: any = memberUserId
      let currentUserId: any = localStorage.getItem('id')

      let memId: any = memberId

      const response = await terminateMember({ memberId: memId, message: msg, terminateDate: startOfDate(date) })
      if (isSuccess(response)) {
        const formatedDate = formatDateymd(response?.data?.data?.date || startOfDate(date))
        const message = response?.data?.data?.message.replace('$Date', formatedDate)
        notify(message, 'success')
        setTerminateLoading(false)
        navigate(`/team`)
      } else {
        notify(response?.data?.message, 'error')
        setTerminateLoading(false)
      }
      // }
    } catch (error) {
      console.error('terMinateButton error', error)
      setTerminateLoading(false)
    }
  }

  // const onDelete = async () => {
  //   try {
  //     if (Object.keys(currentCompany).length > 0) {
  //       setDeleteLoading(true)
  //       let currentUserId: any = localStorage.getItem('id')

  //       let memId: any = memberId
  //       console.log('currentUserId', currentUserId)
  //       const response = await deleteMember(
  //         {  memberId: memId },
  //         JSON.parse(currentUserId)
  //       )
  //       if (isSuccess(response)) {
  //         notify('Deleted Member Successfully', 'success')
  //         setDeleteLoading(false)
  //         navigate(`/team/${id}`)
  //       } else {
  //         notify(response?.data?.message, 'error')
  //         setDeleteLoading(false)
  //       }
  //       console.log('onDelete response', response)
  //     }
  //   } catch (error) {
  //     console.error('terMinateButton error', error)
  //     setDeleteLoading(false)
  //   }
  // }

  const getSingleMemberDetail = async () => {
    try {
      // if (Object.keys(currentCompany).length > 0) {
      let id: any = memberUserId
      let memId: any = memberId
      const res = await getTeamMemberDetail({ memberId: memId })
      if (isSuccess(res)) {
        const { memberInfo } = res?.data?.data
        setTeamMember(memberInfo)
      } else throw new Error(res?.data?.message)
      // }
    } catch (error) {
      console.error('terMinateButton error', error)
    }
  }

  useEffect(() => {
    if (memberId && memberUserId) {
      // setCheckRoles()
      getCompanyInfoDetails()
      getCompensationInfo()
      getTeamDetails()
      getSingleMemberDetail()
      getDepartmentDetails()
      getPaySceduleInfo()

      // getDetails()
    }
  }, [currentMember, dataUpdated, memberUserId, memberId, managerId])

  useEffect(() => {
    if (payScheduleDetail && Object.keys(payScheduleDetail)?.length && allCompensationData) {
      const currentPaySchedule = allCompensationData?.paySchedule?.name

      const perio = payScheduleDetail[currentPaySchedule]?.period
      const payPeriod = Number(PERIOD_OBJ[perio])
      const payEnd = payScheduleDetail[currentPaySchedule]?.periodEnd
      const payDay = payScheduleDetail[currentPaySchedule]?.payDay
      const payEnd2 = payScheduleDetail[currentPaySchedule]?.periodEnd2
      const payDay2 = payScheduleDetail[currentPaySchedule]?.payDay2

      try {
        if (allCompensationData && Object.keys(allCompensationData)?.length) {
          const data = getPayPeriods2NHR(
            PERIOD_OBJ1[payPeriod],
            new Date(payEnd).toISOString().split('-').join('-').split('T')[0],
            typeof payDay === 'number' ? payDay : new Date(payDay).toISOString().split('-').join('-').split('T')[0],
            payEnd2 ? new Date(payEnd2).toISOString().split('-').join('-').split('T')[0] : 0,
            payDay2 ?? 0
          )

          const currentRange = data?.filter((itm: any, idx) => {
            return (
              dayjsFormat(itm?.periodStart, 'MM/DD/YYYY') ===
              dayjsFormat(allCompensationData?.effectivePayPeriod, 'MM/DD/YYYY')
            )
          })

          setSelectedRange(currentRange?.[0])
        }
      } catch (error) {
        console.error('Compensation error======>', error)
      }
    }
  }, [payScheduleDetail, allCompensationData])

  // useEffect(() => {
  // }, [currentCompany, currentMember, dataUpdated])

  useEffect(() => {
    getDirectReports()

    fetchCompanyPayData()
  }, [])

  const fetchCompanyPayData = async () => {
    try {
      setLoading(true)
      const res = await getCompanyPayData()

      if (isSuccess(res)) {
        const { versions, pieceWorkPositions, salesPositions } = res.data.data
        setPwVersions(versions)
        setPwCommissionPosition({ pieceWorkPositions, salesPositions })
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.error('Failed init fetch', err)
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteCompensation = async () => {
    try {
      setDeleteLoading(true)
      const res = await deleteCompensation({
        memberId: memberId,
        effectivePayPeriod: upcomingPosition?.effectivePayPeriod,
      })
      if (isSuccess(res)) {
        notify(res?.data?.data?.message, 'success')
        setShowCancelConfirmModal(false)
        setDataUpdated((prev) => !prev)
      }
    } catch (error) {
      console.error('handleDeleteCompensation error', error)
    } finally {
      setDeleteLoading(false)
    }
  }

  return (
    <>
      {shimmerLoading ? (
        <>
          <SharedStyled.Skeleton custWidth="100%" custHeight={'51px'} />
          <SharedStyled.Skeleton custWidth="100%" custHeight="50%" custMarginTop="10px" />
          <SharedStyled.Skeleton custWidth="100%" custHeight="50%" custMarginTop="10px" />
        </>
      ) : (
        <Styled.TeamMemberContainer>
          <SharedStyled.SectionTitle>Team Member</SharedStyled.SectionTitle>

          <Formik
            initialValues={initialValues}
            onSubmit={handleSubmit}
            validationSchema={TeamMemberSchema}
            validateOnChange={true}
            validateOnBlur={false}
            enableReinitialize={true}
          >
            {({ values, errors, touched, setFieldValue, handleChange }) => {
              return (
                <>
                  <Form className="form">
                    <SharedStyled.Content width="100%" disableBoxShadow={true} noPadding={true}>
                      <SharedStyled.TwoInputDiv>
                        <InputWithValidation
                          labelName="First Name"
                          stateName="firstName"
                          twoInput={true}
                          error={touched.firstName && errors.firstName ? true : false}
                        />
                        <InputWithValidation
                          labelName="Last Name"
                          stateName="lastName"
                          twoInput={true}
                          error={touched.lastName && errors.lastName ? true : false}
                        />
                      </SharedStyled.TwoInputDiv>
                      <SharedStyled.TextArea
                        component="textarea"
                        placeholder="Notes"
                        as={Field}
                        name="notes"
                        marginTop="8px"
                        height="52px"
                        stateName="notes"
                        labelName="Opportunity Notes"
                        error={touched.notes && errors.notes ? true : false}
                      />
                      <InputWithValidation
                        labelName="Preferred Name"
                        stateName="preferredName"
                        error={touched.preferredName && errors.preferredName ? true : false}
                      />

                      <InputWithValidation
                        labelName="Email"
                        stateName="email"
                        error={touched.email && errors.email ? true : false}
                      />
                      <SharedPhone
                        labelName="Phone"
                        stateName="phone"
                        value={values.phone}
                        onChange={handleChange('phone')}
                        error={touched.phone && errors.phone ? true : false}
                      />

                      <SharedDate
                        value={values.DOB}
                        labelName="Date Of Birth"
                        stateName="DOB"
                        error={touched.DOB && errors.DOB ? true : false}
                        setFieldValue={setFieldValue}
                      />
                      <Styled.HeadeContainer>
                        {' '}
                        <Toggle
                          title="Mobile Access: "
                          // customStyles={{ margin: '16px' }}
                          isToggled={initialValues?.mobileAccess ?? false}
                          onToggle={() => {
                            handleUpdateProfile({ mobileAccess: !initialValues?.mobileAccess })
                            setInitialValues((prev) => ({
                              ...prev,
                              mobileAccess: !initialValues?.mobileAccess,
                            }))
                          }}
                        />
                      </Styled.HeadeContainer>
                      <Styled.HeadeContainer>
                        <SharedStyled.SectionTitle>Compensation</SharedStyled.SectionTitle>
                      </Styled.HeadeContainer>
                      {noData ? (
                        <>
                          {/* <Styled.CompensationInfoDiv>
                            <KeyValue keyText="Position" valueText={''} />
                            <KeyValue keyText="Base Wage" valueText="" />
                            /~ <KeyValue keyText="Commission" valueText="" />
                        <KeyValue keyText="Piece Work Hourly Rate" valueText="" /> ~/
                            <KeyValue keyText="Effective Pay Period" valueText="" />
                            <KeyValue keyText="Pay Schedule" valueText="" />
                          </Styled.CompensationInfoDiv>*/}
                        </>
                      ) : (
                        <Styled.CompensationInfoDiv>
                          <KeyValue keyText="Position" valueText={positionText} />

                          {currentPosition === 'subContractor' ? (
                            <KeyValue
                              keyText="Subcontractor"
                              valueText={getNameFrom_Id(
                                teamMember?.subContractorId,
                                allSubcontractorData?.subcontractor || []
                              )}
                            />
                          ) : null}

                          {currentPosition === 'subContractor' ? null : (
                            <KeyValue
                              keyText="Base Wage"
                              valueText={compensationData.wage ? `$${compensationData.wage}` : '-'}
                            />
                          )}
                          {(currentPosition === 'SalesPerson' || currentPosition === 'SalesManager') && (
                            <KeyValue keyText="Commission" valueText={compensationData.salesCommision} />
                          )}
                          {(currentPosition === 'SalesPerson' || currentPosition === 'SalesManager') && (
                            <KeyValue keyText="Self Gen Commission" valueText={compensationData.selfLead} />
                          )}
                          {(currentPosition === 'CrewMember' || currentPosition === 'Foreman') &&
                            compensationData.pieceWorkHourlyRate && (
                              <KeyValue
                                keyText="Piece Work Hourly Rate"
                                valueText={`$${compensationData.pieceWorkHourlyRate}`}
                              />
                            )}

                          {allCompensationData?.versionId ? (
                            <KeyValue
                              keyText="Piece Work version"
                              valueText={camelCaseToSentenceCase(
                                pwVersions?.find((pwVersion) => pwVersion?._id === allCompensationData?.versionId)
                                  ?.name!
                              )}
                            />
                          ) : null}
                          {currentPosition === 'Foreman' && (
                            <KeyValue
                              keyText="Self Bonus"
                              valueText={`${Number(allCompensationData?.ownPieceWork) * 100}%`}
                            />
                          )}
                          {currentPosition === 'Foreman' && (
                            <KeyValue
                              keyText="Crew Bonus"
                              valueText={`${Number(allCompensationData?.crewPieceWork) * 100}%`}
                            />
                          )}
                          {currentPosition === 'subContractor' ? null : (
                            <KeyValue
                              keyText="Effective Pay Period"
                              valueText={dayjsFormat(compensationData?.effectivePayPeriod, 'M/D/YY')}
                            />
                          )}

                          {currentPosition === 'subContractor' ? null : (
                            <KeyValue keyText="Pay Schedule" valueText={compensationData.paySchedule} />
                          )}
                        </Styled.CompensationInfoDiv>
                      )}
                      {/* upcomingPosition */}
                      {Object.keys(upcomingPosition).length !== 0 && (
                        <>
                          <Styled.ShowDetailsContainer>
                            {/* {futureCompentasion && ( */}
                            <SharedStyled.FlexCol>
                              <Styled.ShowDetailsText marginTop="10px" className="upcoming">
                                Upcoming Change
                              </Styled.ShowDetailsText>

                              <hr />
                              <Styled.CompensationInfoDiv>
                                <KeyValue keyText="Position" valueText={upcomingPosition?.position?.position} />
                                <KeyValue
                                  keyText="Base Wage"
                                  valueText={`$${
                                    upcomingPosition?.wageAmount && upcomingPosition?.wageInterval
                                      ? `${Number(upcomingPosition?.wageAmount)?.toFixed(2)}/${PER_OBJ[
                                          upcomingPosition?.wageInterval
                                        ].toLowerCase()}`
                                      : ''
                                  }`}
                                />
                                {(currentPosition === 'SalesPerson' || currentPosition === 'SalesManager') && (
                                  <KeyValue keyText="Commission" valueText={upcomingPosition.salesCommision} />
                                )}
                                {(currentPosition === 'SalesPerson' || currentPosition === 'SalesManager') && (
                                  <KeyValue keyText="Self Gen Commission" valueText={compensationData.selfLead} />
                                )}
                                {(currentPosition === 'CrewMember' || currentPosition === 'Foreman') && (
                                  <KeyValue
                                    keyText="Piece Work Hourly Rate"
                                    valueText={`$${upcomingPosition.pieceWorkHourlyRate}/hour`}
                                  />
                                )}

                                {upcomingPosition?.versionId ? (
                                  <KeyValue
                                    keyText="Piece Work version"
                                    valueText={camelCaseToSentenceCase(
                                      pwVersions?.find((pwVersion) => pwVersion?._id === upcomingPosition?.versionId)
                                        ?.name!
                                    )}
                                  />
                                ) : null}
                                <KeyValue
                                  keyText="Effective Pay Period"
                                  valueText={dayjsFormat(upcomingPosition?.effectivePayPeriod, 'M/D/YY')}
                                />
                                <KeyValue
                                  keyText="Pay Schedule"
                                  valueText={upcomingPosition?.paySchedule?.name ?? ''}
                                />
                                <KeyValue keyText="Reason for change" valueText={upcomingPosition?.reason ?? ''} />
                              </Styled.CompensationInfoDiv>

                              <SharedStyled.FlexRow
                                gap="0px"
                                width="190px"
                                margin="16px auto 0 0"
                                justifyContent="flex-end"
                              >
                                <Button
                                  className="delete"
                                  type="button"
                                  width="max-content"
                                  onClick={() => {
                                    setShowCancelConfirmModal(true)
                                  }}
                                >
                                  Cancel
                                </Button>
                              </SharedStyled.FlexRow>
                            </SharedStyled.FlexCol>
                            {/* // )} */}

                            {/* <Styled.ShowDetailsText
                              marginTop="10px"
                              onClick={() => setFutureCompentasion(!futureCompentasion)}
                            >
                              {futureCompentasion ? 'Back' : 'View future compensation'}
                            </Styled.ShowDetailsText> */}
                          </Styled.ShowDetailsContainer>
                        </>
                      )}
                      <SharedStyled.FlexRow width="190px" gap="0px" margin="0 auto 0 0">
                        <SharedStyled.FlexBox width="100%" justifyContent="end" gap="10px" marginTop="20px">
                          {!noData && !Object.keys(upcomingPosition).length && (
                            <Button
                              type="button"
                              width="max-content"
                              onClick={() => {
                                setIsEdit(noData ? false : true)
                                setCompensationModal(true)
                              }}
                            >
                              Change
                              {/* {noData ? 'Add' : 'Edit'} */}
                            </Button>
                          )}
                          {teamMember?.hireDate ? (
                            <>
                              {noData && (
                                <Button
                                  type="button"
                                  className="fit"
                                  onClick={() => {
                                    setIsEdit(false)
                                    setCompensationModal(true)
                                  }}
                                >
                                  Add
                                </Button>
                              )}
                            </>
                          ) : (
                            <>
                              <SharedStyled.Button
                                type="button"
                                maxWidth="150px"
                                onClick={() => notify('Must enter the Hire Date before adding compensation', 'error')}
                              >
                                Add
                              </SharedStyled.Button>
                            </>
                          )}
                        </SharedStyled.FlexBox>
                      </SharedStyled.FlexRow>
                      <Styled.ShowDetailsContainer>
                        <Styled.ShowDetailsText
                          marginTop="10px"
                          onClick={() =>
                            navigate(`/team/member/compensation/history/${teamMember?._id}`, {
                              state: {
                                allPayScheduleData,
                                pwVersions,
                              },
                            })
                          }
                        >
                          View compensation history
                        </Styled.ShowDetailsText>
                      </Styled.ShowDetailsContainer>
                      {/* ==================== Time-off ==================== */}

                      <TimeOffBreakdown data={ptoData} />

                      {/* ==================== Time-off ==================== */}
                      <Styled.HeadeContainer>
                        <SharedStyled.SectionTitle>Company Information</SharedStyled.SectionTitle>
                      </Styled.HeadeContainer>
                      <SharedDate
                        value={values.hireDate}
                        labelName="Hire Date"
                        stateName="hireDate"
                        error={touched.hireDate && errors.hireDate ? true : false}
                        setFieldValue={setFieldValue}
                      />
                      <SharedStyled.TwoInputDiv>
                        <CustomSelect
                          value={values.manager}
                          labelName="Manager"
                          stateName="manager"
                          dropDownData={managerData}
                          setFieldValue={setFieldValue}
                          margin="10px 0 0 0"
                          setValue={() => {}}
                          error={touched.manager && errors.manager ? true : false}
                          showInitialValue
                        />
                        <CustomSelect
                          value={values.department}
                          labelName="Department"
                          stateName="department"
                          dropDownData={departmentData}
                          setFieldValue={setFieldValue}
                          margin="10px 0 0 0"
                          setValue={() => {}}
                          error={touched.department && errors.department ? true : false}
                        />
                      </SharedStyled.TwoInputDiv>
                      <Styled.ShowDetailsContainer>
                        <KeyValue marginTop="10px" keyText="Direct Reports" valueText={' '} />
                        {directReportsData.length > 0 ? (
                          <>
                            {directReportsData.map((report: any) => (
                              <Styled.ShowDetailsText
                                marginTop="5px"
                                onClick={() => {
                                  window.open(`/team/member/${report.memberId}/${report.managerId}`, '_blank')
                                }}
                              >
                                {report.name}
                              </Styled.ShowDetailsText>
                            ))}
                          </>
                        ) : (
                          '-'
                        )}
                      </Styled.ShowDetailsContainer>
                      {/* <Styled.HeadeContainer>
                        <SharedStyled.SectionTitle>Roles</SharedStyled.SectionTitle>
                      </Styled.HeadeContainer>
                      <CustomSelect
                        value={values.roles}
                        labelName="Roles"
                        stateName="roles"
                        setValue={() => {}}
                        dropDownData={ROLES_NAME}
                        setFieldValue={setFieldValue}
                        disabled={currentUserRole === 'Owner' || currentUserRole === 'Admin' ? false : true}
                        error={touched.roles && errors.roles ? true : false}
                        className="roles"
                      /> */}

                      <SharedStyled.ButtonContainer marginTop="20px">
                        <Button type="submit" maxWidth="200px" isLoading={loading}>
                          Update
                        </Button>
                        {currentPosition === 'Owner' ? null : (
                          <Button
                            type="button"
                            maxWidth="200px"
                            className="delete"
                            onClick={() => setShowConfirmModal(true)}
                          >
                            Terminate
                          </Button>
                        )}
                      </SharedStyled.ButtonContainer>
                    </SharedStyled.Content>
                  </Form>
                  <CustomModal show={showCompensationModal}>
                    <CompensationModal
                      setCompensationModal={setCompensationModal}
                      noData={noData}
                      isFromTeamMemberSection
                      setDataUpdated={setDataUpdated}
                      isEdit={isEdit}
                      compensationData={compensationData}
                      hireDateCompensation={hireDateCompensation}
                      pwVersions={pwVersions}
                      subContractorId={teamMember?.subContractorId}
                      isEffectivePayPeriodOver={isEffectivePayPeriodOver}
                      commissionPosition={pwCommissionPosition?.salesPositions}
                      pwPosition={pwCommissionPosition.pieceWorkPositions}
                    />
                  </CustomModal>
                  <CustomModal show={terminateModal}>
                    <TerminateModal
                      onClose={() => {
                        setTerminateModal(false)
                      }}
                      onDelete={onTerminate}
                      name={teamMember?.name}
                      terminateLoading={terminateLoading}
                    />
                  </CustomModal>
                </>
              )
            }}
          </Formik>
        </Styled.TeamMemberContainer>
      )}

      <CustomModal show={showConfirmModal}>
        <Modal title={`Terminate ${teamMember?.name}`} hideCloseButton>
          <SharedStyled.FlexCol alignItems="flex-start" padding="0 0 0 8px">
            <Title style={{ fontSize: '18px', padding: '0px' }}>
              Are you sure you want to Terminate {teamMember?.name}?
            </Title>

            <Info style={{ justifySelf: 'flex-start', margin: '16px 0 24px 0' }}>
              <span style={{ color: 'black', fontWeight: 900 }}>&#9432;</span> Member restoration will be restricted for
              10 days after termination. If you proceed, you won't be able to restore this member for the next 10 days.
              .
            </Info>
            <SharedStyled.FlexRow>
              <Button
                className="delete"
                onClick={() => {
                  setShowConfirmModal(false)
                  setTerminateModal(true)
                }}
              >
                Yes
              </Button>
              <Button
                onClick={() => {
                  setShowConfirmModal(false)
                }}
              >
                No
              </Button>
            </SharedStyled.FlexRow>
          </SharedStyled.FlexCol>
        </Modal>
      </CustomModal>

      <CustomModal show={showCancelConfirmModal}>
        <DeletedModal
          isTeamSection
          inputData={{ name: 'hello' }}
          onClose={() => {
            setShowCancelConfirmModal(false)
          }}
          onDelete={handleDeleteCompensation}
          onRestore={() => {
            setShowCancelConfirmModal(false)
          }}
          title="Cancel Upcoming Compensation"
          restoreString="No"
          deleteString="Yes"
          btnLoadingDelete={deleteLoading}
        >
          <p
            style={{
              fontFamily: `${Nue.medium}`,
            }}
          >
            Are you sure you want to cancel the upcoming compensation for {teamMember?.name}?
          </p>
        </DeletedModal>
      </CustomModal>
    </>
  )
}

export default TeamMember
