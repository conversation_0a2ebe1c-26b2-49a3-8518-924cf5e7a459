import { useMemo, useState, useRef, useCallback, lazy, useEffect } from 'react'
import { useSelector } from 'react-redux'
import { Link, useNavigate } from 'react-router-dom'

import { getInvitationStatus, getTeamMembers } from '../../logic/apis/team'
import { CustomModal } from '../../shared/customModal/CustomModal'
import {
  dayjsFormat,
  extractPermissionByName,
  formatPhoneNumber,
  getDataFromLocalStorage,
  isCustomTruthy,
  isSuccess,
  notify,
} from '../../shared/helpers/util'
import * as SharedStyled from '../../styles/styled'
import { InvitationForm } from './components/invitationForm/InvitationForm'
const InvitationStatus = lazy(() => import('./components/invitationStatus/InvitationStatus'))
import { Table } from '../../shared/table/Table'
import { ButtonCont, SettingsCont } from '../units/style'
import Button from '../../shared/components/button/Button'
import TabBar from '../../shared/components/tabBar/TabBar'
import ProfileInfo from '../../shared/components/profileInfo/ProfileInfo'
import AddUserModal from './components/addUserModal/AddUserModal'
import { getMemberMetrics, getUserSubscriptionPlan } from '../../logic/apis/pieceWorkPro'
import useFetch from '../../logic/apis/useFetch'
import {
  Permissions,
  StorageKey,
  SubscriptionPlanType,
  SubscriptionRenewalPeriod,
} from '../../shared/helpers/constants'
import Modal from '../../shared/customModal/Modal'
import { plansPath } from '../../logic/paths'
import { TooltipPortal } from '../../shared/components/tooltip'
import { CompensationModal } from './components/teamMember/components/CompensationModal/CompensationModal'
import { getCompanyPayData } from '../../logic/apis/pieceWork'
import { ProfileCont } from '../../shared/components/profileInfo/style'
const TerminatedMemberTable = lazy(() => import('./components/terminatedMemberTable/TerminatedMemberTable'))

interface I_Data {
  name: string
  // username: string
  email: string
}

const Team = () => {
  const [showInviteModal, setShowInviteModal] = useState<boolean>(false)
  const [shimmerLoading, setShimmerLoading] = useState<boolean>(true)
  const [dataUpdate, setDataUpdate] = useState<boolean>(false)
  const [showPlanModal, setShowPlanModal] = useState(false)
  const [showCompensationModal, setCompensationModal] = useState<boolean>(false)
  const [selectedInviteId, setSelectedInviteId] = useState('')
  const [inviteUpdate, setInviteUpdate] = useState(false)

  const { data: memberMetrics, loading: metricsLoading } = useFetch({
    fetchFn: getMemberMetrics,
    refetchTrigger: dataUpdate,
  })

  const [teamMembers, setTeamMembers] = useState([])
  const [invitationStatusMembers, setInvitationStatusMembers] = useState([])

  const { data: userPlanData } = useFetch({
    fetchFn: () => getUserSubscriptionPlan(),
    refetchTrigger: dataUpdate,
  })

  const navigate = useNavigate()
  const loadMoreRef = useRef(null)

  const [loading, setLoading] = useState<boolean>(false)
  // const [pageCount, setPageCount] = useState<number>(20)
  const [data, setData] = useState<I_Data[]>([])
  const fetchIdRef = useRef(0)
  const planType = memberMetrics?.planType
  const [showAddUserModal, setShowAddUserModal] = useState(false)

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, positionDetails, positionPermissions } = globalSelector.company
  const { justInvited } = globalSelector.invitation
  const position = positionDetails?.symbol
  const { data: companyData } = useFetch({
    fetchFn: () => getCompanyPayData(),
  })

  const teamPermissions =
    Object.keys(positionPermissions)?.length && extractPermissionByName(positionDetails, 'team')?.permissions

  const hasInviteAccess = teamPermissions === Permissions['Full'] || teamPermissions === Permissions['Managed']

  const currentTeamSize = memberMetrics?.memberCount + memberMetrics?.inviteCount

  const isProPlusPlan = planType === SubscriptionPlanType.PROPLUS

  const fetchData = useCallback(
    async ({ pageSize, pageIndex, search }: { pageSize: number; pageIndex: number; search: string }) => {
      try {
        setLoading(true)

        // This will get called when the table needs new data
        let receivedData: any = []
        let currentCompanyData: any = localStorage.getItem('currentCompany')

        const statusResponse = await getTeamMembers({ skip: pageIndex, limit: pageSize, deleted: false, search })

        if (isSuccess(statusResponse)) {
          let statusRes = statusResponse?.data?.data?.memberData

          statusRes.forEach((res: any, index: number) => {
            receivedData.push({
              name: res.name,
              // username: res.username,
              email: res.email,
              id: res.user,
              memberId: res._id,
              managerId: res?.managerId ? res.managerId : '0',
              position: res?.positionName ? res?.positionName : '--',
              createdAt: res?.createdAt ? res?.createdAt : '0',
              hireDate: res?.hireDate ? res?.hireDate : '',
              phone: res?.phone || '',
              DOB: res?.DOB,
              users: res?.users,
              departmentName: res?.departmentName,
              managerName: res?.managerName,
              imageUrl: res?.imageUrl,
            })
          })
        } else {
          notify(statusResponse?.data?.message, 'error')
        }

        // Give this fetch an ID
        const fetchId = ++fetchIdRef.current

        // Set the loading state

        // We'll even set a delay to simulate a server here
        // Only update the data if this is the latest fetch
        if (fetchId === fetchIdRef.current) {
          const startRow = pageSize * pageIndex
          const endRow = startRow + pageSize
          setData(receivedData.slice(startRow, endRow))
        }
      } catch (error) {
        console.error('TeamTable fetchData error', error)
      } finally {
        setLoading(false)
      }
    },
    []
  )

  const columns: any = useMemo(
    () => [
      {
        Header: 'Name',
        accessor: 'name', // accessor is the "key" in the data
        Cell: (props: any) => {
          return <ProfileInfo data={{ ...props?.row?.original }} isTeam />
        },
      },
      // {
      //   Header: 'Username',
      //   accessor: 'username',
      // },
      {
        Header: 'Position',
        accessor: 'position',
      },
      {
        Header: 'Phone',
        accessor: 'phone',
        Cell: (props: any) =>
          props?.row?.original?.phone ? formatPhoneNumber(String(props?.row?.original?.phone), '') : '--',
      },
      {
        Header: 'Hire date',
        accessor: 'hireDate',
        Cell: (props: any) =>
          props?.row?.original?.hireDate ? dayjsFormat(props?.row?.original?.hireDate, 'M/D/YY') : '--',
      },
      {
        Header: 'DOB',
        accessor: 'DOB',
        Cell: (props: any) => (props?.row?.original?.DOB ? dayjsFormat(props?.row?.original?.DOB, 'M/D/YY') : '--'),
      },
      {
        Header: 'Department',
        accessor: 'departmentName',
        Cell: (props: any) => {
          return (
            <ProfileCont>
              <SharedStyled.FlexCol>
                <h1>{props?.row?.original?.departmentName ?? '--'}</h1>
                <p>{props?.row?.original?.managerName ?? '--'}</p>
              </SharedStyled.FlexCol>
            </ProfileCont>
          )
        },
      },
    ],
    []
  )

  const getTeamDetails = async () => {
    // if (Object.keys(currentCompany).length > 0) {
    try {
      // const memberResponse = await getTeamMembers(
      //   { skip: 0, limit: 20, deleted: false, companyId: currentCompany._id },
      //   id
      // )
      let statusResponse
      let dontCheck = true
      let currentPos: any = localStorage.getItem('position')
      if (JSON.parse(currentPos) !== 'ProjectManager') {
        statusResponse = await getInvitationStatus({ skip: 0, limit: 10, deleted: false })
        dontCheck = false
      }

      if (dontCheck || isSuccess(statusResponse)) {
        let statusRes = statusResponse?.data?.data?.invitations

        setInvitationStatusMembers(statusRes)
        setShimmerLoading(false)
      } else {
        notify(statusResponse?.data?.message, 'error')
        setShimmerLoading(false)
      }
    } catch (error) {
      console.error('getTeamDetails error', error)
      setShimmerLoading(false)
    }
    // }
  }

  useEffect(() => {
    getTeamDetails()
    // fetchData({ pageIndex: 0, pageSize: 20 })
  }, [justInvited])

  const handleInviteClick = () => {
    console.log('INVITE CLICK=====>', {
      planType,
      currentTeamSize: memberMetrics?.memberCount + memberMetrics?.inviteCount,
      memberMetrics,
      subscribedTeamSize: userPlanData?.userSubscriptions?.subscribedTeamSize,
    })
    if (planType === SubscriptionPlanType.FREE) {
      setShowPlanModal(true)
    } else if (memberMetrics?.interval === SubscriptionRenewalPeriod.MONTH) {
      setShowInviteModal(true)
    } else if (currentTeamSize >= userPlanData?.userSubscriptions?.subscribedTeamSize) {
      setShowAddUserModal(true)
    } else {
      setShowInviteModal(true)
    }
  }

  return (
    <>
      <SettingsCont gap="24px">
        <SharedStyled.FlexRow justifyContent="space-between" flexWrap="wrap">
          <SharedStyled.SectionTitle>Team Members</SharedStyled.SectionTitle>

          <ButtonCont>
            {hasInviteAccess && (
              <Button onClick={handleInviteClick} disabled={metricsLoading}>
                Invite Member
              </Button>
            )}
          </ButtonCont>
        </SharedStyled.FlexRow>

        <SharedStyled.FlexRow alignItems="flex-start">
          <SharedStyled.FlexCol gap="24px">
            <TabBar
              tabs={[
                {
                  title: (
                    <>
                      Active
                      {isCustomTruthy(memberMetrics?.memberCount) && !isProPlusPlan ? (
                        <span className="count">{memberMetrics?.memberCount}</span>
                      ) : null}{' '}
                    </>
                  ),
                  render: () => (
                    <Table
                      columns={columns}
                      data={data}
                      loading={loading}
                      // pageCount={pageCount}
                      fetchData={fetchData}
                      member={true}
                      isLoadMoreLoading={loading}
                      ref={loadMoreRef}
                    />
                  ),
                },
                {
                  title: position !== 'ProjectManager' ? 'Terminated' : '',
                  render: () => <TerminatedMemberTable />,
                },
                {
                  title: (
                    <>
                      Invited
                      {isCustomTruthy(memberMetrics?.inviteCount) && !isProPlusPlan ? (
                        <span className="count">{memberMetrics?.inviteCount}</span>
                      ) : null}
                    </>
                  ),
                  render: () => (
                    <InvitationStatus
                      dataUpdate={dataUpdate}
                      setDataUpdate={setDataUpdate}
                      setInviteUpdate={setInviteUpdate}
                      inviteUpdate={inviteUpdate}
                      companyData={{
                        ...companyData,
                      }}
                    />
                  ),
                },
              ]}
              filterComponent={<></>}
            />
          </SharedStyled.FlexCol>
        </SharedStyled.FlexRow>
      </SettingsCont>

      <CustomModal show={showInviteModal}>
        <InvitationForm
          setShowCompanyCreationModal={setShowInviteModal}
          setDataUpdate={setDataUpdate}
          onInviteSuccess={(id) => {
            setSelectedInviteId(id)

            setShowInviteModal(false)
            setCompensationModal(true)
          }}
        />
      </CustomModal>
      <CustomModal show={showAddUserModal}>
        <AddUserModal
          currentTeamSize={currentTeamSize}
          setDataUpdate={setDataUpdate}
          onClose={() => {
            setShowAddUserModal(false)
          }}
        />
      </CustomModal>
      <CustomModal show={showPlanModal}>
        <Modal title="Subscribe to a plan">
          <SharedStyled.Text fontSize="16px" fontWeight={'500'}>
            No available user slots. Please contact your Administrator to purchase more
          </SharedStyled.Text>
          <SharedStyled.FlexRow margin="20px 0 0 0">
            <Button
              onClick={() => {
                navigate(plansPath)
              }}
            >
              Check plans
            </Button>
            <Button
              className="gray"
              onClick={() => {
                setShowPlanModal(false)
              }}
            >
              Maybe later
            </Button>
          </SharedStyled.FlexRow>
        </Modal>
      </CustomModal>

      <CustomModal show={showCompensationModal}>
        <CompensationModal
          setCompensationModal={setCompensationModal}
          noData={false}
          setDataUpdated={setDataUpdate}
          isEdit={false}
          compensationData={{}}
          hireDateCompensation={''}
          pwVersions={companyData?.versions}
          isInviteModal
          commissionPosition={companyData?.salesPositions}
          selectedInviteId={selectedInviteId}
          pwPosition={companyData?.pieceWorkPositions}
          setInviteUpdate={setInviteUpdate}
        />
      </CustomModal>
    </>
  )
}

export default Team
