import { useEffect, useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { InvitationIcon } from '../../assets/icons/InvitationIcon'
import { getInvitationDetails, getUserByEmail } from '../../logic/apis/invitation'
import { isSuccess, notify } from '../../shared/helpers/util'
import { colors } from '../../styles/theme'
import * as Styled from './style'
import * as SharedStyled from '../../styles/styled'
import { companySignupPath } from '../../logic/paths'

const Invitation = () => {
  const { invitationId } = useParams()

  const [data, setData] = useState<any>({})

  const [companyName, setCompanyName] = useState<string>('')

  const [acceptloading, setAcceptLoading] = useState<boolean>(false)

  const [rejectloading, setRejectLoading] = useState<boolean>(false)

  const navigate = useNavigate()

  // const onAcceptingInvite = async () => {
  //   try {
  //     const id: any = invitationId
  //     setAcceptLoading(true)
  //     const response = await acceptRejectInvitation({ ...data, status: '2' })
  //     if (isSuccess(response)) {
  //       notify('Accepted invitation successfully', 'success')
  //       setAcceptLoading(false)
  //       if (response?.data?.data?.message === 'User does not exist!') {
  //         navigate('/signup', {
  //           state: {
  //             invitationId: id,
  //           },
  //         })
  //       } else if (response?.data?.data?.message === 'The user has already been registered with this company!') {
  //         navigate('/signin')
  //       }
  //       // if ('User does not exist!') {
  //       //   navigate('/signup')
  //       // }
  //     } else {
  //       notify(response?.data?.message, 'error')
  //       setAcceptLoading(false)
  //     }
  //   } catch (error) {
  //     console.error('onAcceptingInvite error', error)
  //     setAcceptLoading(false)
  //   }
  // }

  // const onRejectingInvite = async () => {
  //   try {
  //     setRejectLoading(true)
  //     const response = await acceptRejectInvitation({ ...data, status: '3' })
  //     if (isSuccess(response)) {
  //       notify('Invitation rejected successfully', 'success')
  //       setRejectLoading(false)
  //     } else {
  //       notify(response?.data?.message, 'error')
  //       setRejectLoading(false)
  //     }
  //   } catch (error) {
  //     console.error('onDecliningInvite error', error)
  //     setRejectLoading(false)
  //   }
  // }

  const getInviteDetail = async () => {
    try {
      let id: any = invitationId
      const response = await getInvitationDetails(id)
      if (isSuccess(response)) {
        let res = response?.data?.data?.invitation
        setData({
          senderEmail: res.senderEmail,
          recipientEmail: res.email,
          company: res.company,
          status: res.status,
        })
        setCompanyName(res.companyName)
      } else {
        notify(response?.data?.message, 'error')
      }
    } catch (error) {
      console.error('getInviteDetail error', error)
    }
  }

  const checkIsAlreadyUser = async () => {
    try {
      const id: any = invitationId

      if (Object.keys(data).length > 0) {
        const response = await getUserByEmail(data.recipientEmail)

        if (isSuccess(response)) {
          let res = response?.data?.data?.message

          if (res === 'The user has already been registered!') {
            notify(res, 'info')
            setTimeout(() => {
              navigate(
                '/signin'
                //   {
                //   state: {
                //     invitationId: invitationId,
                //   },
                // }
              )
            }, 2000)
          } else if (res === 'User does not exist!') {
            setTimeout(() => {
              navigate(companySignupPath, {
                state: {
                  invitationId: invitationId,
                },
              })
            }, 2000)
          }
        } else {
          notify(response?.data?.message, 'error')
        }
      }
    } catch (error) {
      console.error('checkIsAlreadyUser error', error)
    }
  }

  useEffect(() => {
    getInviteDetail()
    // setTimeout(() => {
    //   navigate('/signup', {
    //     state: {
    //       invitationId: invitationId,
    //     },
    //   })
    // }, 2000)
  }, [])

  useEffect(() => {
    checkIsAlreadyUser()
  }, [data])

  return (
    <Styled.InvitationContainer>
      <Styled.InvitationCard>
        <Styled.ImageContainer>
          <InvitationIcon />
        </Styled.ImageContainer>
        <Styled.InvitationDescription>
          You have been invited to join the company: {companyName ? companyName : '...'}
        </Styled.InvitationDescription>
        {/* <Styled.ActionButtonContainer>
          <Styled.ActionButton color={colors.white} background={colors.darkGrey} onClick={() => onAcceptingInvite()}>
            {acceptloading ? (
              <>
                Accepting Invitation
                <SharedStyled.Loader />
              </>
            ) : (
              'Accept Invitation'
            )}
          </Styled.ActionButton>
          <Styled.ActionButton color={colors.darkGrey} background={colors.white} onClick={() => onDecliningInvite()}>
            {rejectloading ? (
              <>
                Declining
                <SharedStyled.Loader color={colors.darkGrey} />
              </>
            ) : (
              'Decline'
            )}
          </Styled.ActionButton>
        </Styled.ActionButtonContainer> */}
        <Styled.LoaderContainer>
          <Styled.LoaderContent className="loading">Please wait</Styled.LoaderContent>
        </Styled.LoaderContainer>
      </Styled.InvitationCard>
    </Styled.InvitationContainer>
  )
}

export default Invitation
