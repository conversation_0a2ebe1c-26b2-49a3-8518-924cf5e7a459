import { Fragment, useEffect, useState } from 'react'
import { useSelector } from 'react-redux'

import Grid from '../../shared/components/grid/Grid'
import { SettingsCont } from './style'
import CalendarSvg from '../../assets/newIcons/calendar.svg'
import CategorySVG from '../../assets/newIcons/category.svg'
import CompanySVG from '../../assets/newIcons/company.svg'
import CrewSVG from '../../assets/newIcons/crewPosition.svg'
import CrmSVG from '../../assets/newIcons/crm.svg'
import DepartmentSVG from '../../assets/newIcons/department.svg'
import LeadsSVG from '../../assets/newIcons/leads.svg'
import MarketingSVG from '../../assets/newIcons/marketing.svg'
import MaterialSVG from '../../assets/newIcons/material.svg'
import PositionSVG from '../../assets/newIcons/position.svg'
import ProjectSVG from '../../assets/newIcons/project.svg'
import Referrer from '../../assets/newIcons/referrer.svg'
import TaskSVG from '../../assets/newIcons/task.svg'
import TaxSVG from '../../assets/newIcons/tax.svg'
import UnitsSVG from '../../assets/newIcons/units.svg'
import WorkSvg from '../../assets/newIcons/work.svg'
import * as Paths from '../../logic/paths'
import { ROLES_OBJ, SubscriptionPlanType } from '../../shared/helpers/constants'
import { FlexCol, SectionSubHeading, SectionTitle } from '../../styles/styled'
import SettingsCard from './components/SettingsCard/SettingsCard'
import { getTeamMemberRole } from '../../logic/apis/team'

const Settings = () => {
  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, roleData } = globalSelector.company

  const isOwnerRole = roleData?.role === Number(ROLES_OBJ.Owner)
  const isProPlusPlan = currentCompany?.planType === SubscriptionPlanType.PROPLUS

  const companyTab = [
    {
      title: 'Company',
      info: 'You can update the company settings over here',
      image: CompanySVG,
      path: Paths.companySettingsPath,
    },
    {
      title: 'Admin',
      info: 'You can add and update the Admin over here',
      image: CrmSVG,
      path: Paths.adminSettingsPath,
    },
    {
      title: 'Plan & Billing',
      info: 'You can view and update the billing details over here',
      image: CompanySVG,
      path: Paths.subscriptionPath,
    },
    {
      title: 'Department',
      info: 'You can view, create, delete or restore departments in here',
      image: DepartmentSVG,
      path: Paths.departmentSettingsPath,
    },
    // {
    //   title: 'Leads Position',
    //   info: 'You can view, create, delete or restore leads positions in here',
    //   image: PositionSVG,
    //   path: Paths.leadsPositionSettingsPath,
    // },
    {
      title: 'Pay Schedule',
      info: 'You can edit or create pay schedule here',
      image: CalendarSvg,
      path: Paths.paySchedulePath,
    },
    {
      title: 'Position',
      info: 'You can view, create, delete or restore positions in here',
      image: PositionSVG,
      path: Paths.positionSettingsPath,
    },

    {
      title: 'Units',
      info: 'You can edit units here',
      image: UnitsSVG,
      path: Paths.unitsSettingsPath,
    },
    {
      title: 'Media',
      info: 'You can edit media here',
      image: MarketingSVG,
      path: Paths.mediaSettingsPath,
      isProPlusPlan: isProPlusPlan,
    },
    {
      title: 'Company Actions',
      info: 'You can see all members actions and company defaults here',
      image: CrmSVG,
      path: Paths.membersAction,
    },
    {
      title: 'Form Builder',
      info: 'You can see all company Forms here',
      image: CrmSVG,
      path: Paths.formsBuilder,
    },
  ]

  const salesTab = [
    {
      title: 'CRM',
      info: 'You can view, create, delete or restore CRM in here',
      image: CrmSVG,
      path: Paths.crmSettingsPath,
    },
    {
      title: 'Contracts',
      info: 'You can view, create, delete or restore Contracts in here',
      image: TaskSVG,
      path: Paths.contractsSettingsPath,
    },
    {
      title: 'Inputs',
      info: 'You can edit inputs here',
      image: TaxSVG,
      path: Paths.inputsSettingsPath,
    },
    {
      title: 'Advertising Cost',
      info: 'You can view, create, delete or restore Advertising cost in here',
      image: MarketingSVG,
      path: Paths.advertiseSettingsPath,
    },
    {
      title: 'Marketing Settings',
      info: 'You can view, create, delete or restore lead source in here',
      image: LeadsSVG,
      path: Paths.leadSourceSettingsPath,
    },

    {
      title: 'Options',
      info: 'You can view, create, delete or restore options in here',
      image: MarketingSVG,
      path: Paths.optionsSettingsPath,
    },
    {
      title: 'Packages',
      info: 'You can edit or create Package here',
      image: CompanySVG,
      path: Paths.packagesPath,
    },
    {
      title: 'Project Types',
      info: 'You can edit project types here',
      image: ProjectSVG,
      path: Paths.projectTypeSettingsPath,
    },
    {
      title: 'Project Tasks',
      info: 'You can edit or create Task here',
      image: CompanySVG,
      path: Paths.tasksPath,
    },
    {
      title: 'Referrer',
      info: 'You can view, create, delete or restore referrer here',
      image: Referrer,
      path: Paths.reffererSettingsPath,
    },
    {
      title: 'Sales Commission',
      info: 'You can edit Sales Commission here.',
      image: MaterialSVG,
      path: Paths.salesCommission,
    },
    {
      title: 'Tax Jurisdiction',
      info: 'You can edit tax jurisdiction here',
      image: TaxSVG,
      path: Paths.taxJurisdiction,
    },
  ]

  const operationsTab = [
    {
      title: 'Category',
      info: 'You can edit category here',
      image: CategorySVG,
      path: Paths.categorySettingsPath,
      isProPlusPlan: isProPlusPlan,
    },
    {
      title: 'Material List',
      info: 'You can edit materials here',
      image: MaterialSVG,
      path: Paths.materialsSettingsPath,
      isProPlusPlan: isProPlusPlan,
    },
    {
      title: 'Piece Work',
      info: 'You can edit or create a piece of work settings for the salaried crew',
      image: WorkSvg,
      path: Paths.pieceWorkSettingsSalariedCrewPath,
      isProPlusPlan: true,
    },
    {
      title: 'Work Tasks',
      info: 'You can view, create, delete or restore task in here',
      image: TaskSVG,
      path: Paths.taskSettingsPath,
      isProPlusPlan: true,
    },
  ]

  return (
    <SettingsCont>
      <FlexCol gap="16px">
        <SectionTitle>Settings</SectionTitle>

        <FlexCol gap="20px" margin="24px 0 0 0">
          <SectionSubHeading>Company</SectionSubHeading>

          <Grid gridGap="16px" gridItemMinWidth="272px">
            {companyTab?.map((item, idx) => (
              <Fragment key={idx}>
                {item.title === 'Admin' ? (
                  isOwnerRole && <SettingsCard data={item} />
                ) : item.title === 'Media' ? (
                  item?.isProPlusPlan && <SettingsCard data={item} />
                ) : (
                  <SettingsCard data={item} />
                )}
              </Fragment>
            ))}
          </Grid>
        </FlexCol>
        {isProPlusPlan ? (
          <FlexCol gap="20px" margin="20px 0 0 0">
            <SectionSubHeading>Sales</SectionSubHeading>

            <Grid gridGap="16px" gridItemMinWidth="272px">
              {salesTab?.map((item, idx) => (
                <SettingsCard data={item} key={idx} />
              ))}
            </Grid>
          </FlexCol>
        ) : null}
        <FlexCol gap="20px" margin="20px 0 0 0">
          <SectionSubHeading>Operations</SectionSubHeading>

          <Grid gridGap="16px" gridItemMinWidth="272px">
            {operationsTab?.map((item, idx) => (
              <Fragment key={idx}>{item?.isProPlusPlan ? <SettingsCard data={item} /> : null}</Fragment>
            ))}
          </Grid>
        </FlexCol>

        {/* <Grid gridGap="16px" gridItemMinWidth="272px">
          {SETTINGS?.map((item, idx) => (
            <SettingsCard data={item} key={idx} />
          ))}
        </Grid> */}
      </FlexCol>
    </SettingsCont>
  )
}

export default Settings
