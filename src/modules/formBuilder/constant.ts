export const disabledAttributes = [
  'className',
  'placeholder',
  'multiple',
  'access',
  'description',
  'name',
  'min',
  'max',
  'step',
  'accept',
]

export const controlOrder = [
  'header',
  'textarea',
  'text',
  'number',
  'date',
  'paragraph',
  'button',
  'file',
  'autocomplete',
  'select',
  'radio-group',
  'checkbox-group',
  'hidden',
  'starRating',
]

export const fields = [
  {
    label: 'Dropdown',
    attrs: {
      type: 'select',
      options: [{ label: 'Option 1' }, { label: 'Option 2' }],
    },
    icon: '▼',
    field: 'select',
  },
  {
    label: 'Paragraph',
    attrs: {
      type: 'textarea',
    },
    icon: 'P',
    field: 'select',
  },
  {
    label: 'Audio',
    attrs: {
      type: 'file',
    },
    icon: '🎧',
    accept: 'audio/*', // audio only
    field: 'audio',
    tag: '',
  },
  {
    label: 'Video',
    attrs: {
      type: 'file',
    },
    accept: 'video/*', // video only
    icon: '🎥',
    field: 'video',
    tag: '',
  },
  {
    label: 'Multiple Photos',
    attrs: {
      type: 'file',
    },
    accept: 'image/*',
    icon: '🎞️',
    field: 'image',
    tag: '',
  },
  {
    label: 'Single Photo',
    attrs: {
      type: 'file',
    },
    accept: 'image/*',
    multiple: false,
    icon: '🖼️',
    field: 'image',
    tag: '',
  },
  {
    label: 'Weather',
    attrs: {
      type: 'weather',
    },
    icon: '🌤️',
    value: '',
  },

  {
    label: 'Location',
    attrs: {
      type: 'location',
    },
    icon: '📍',
    value: 'No Location information available',
  },
]

export let templates = {
  weather: function () {
    return {
      // field: `<input type="text" id="weather" class="form-control" placeholder="Enter weather">`,
    }
  },
  location: function () {
    return {
      // field: ``,
    }
  },
}

export const userDefinedAttrs = {
  header: {
    subtype: {
      label: 'Font Size',
      options: {
        h1: '32px',
        h2: '24px',
        h3: '18.72px',
        h4: '16px',
        h5: '13.28px',
        h6: '10.72px',
      },
    },
  },
  weather: {
    value: {
      label: 'Default value',
      value: 'No weather information available',
    },
  },
  location: {
    value: {
      label: 'Default value',
      value: 'No location information available',
    },
  },
  date: {
    today: {
      label: 'Today',
      value: false,
    },
    value: {
      label: false, // hides the label
      type: 'hidden', // hides the input
    },
  },
  file: {
    accept: {
      label: 'Accepted types',
      value: '', // default value (optional)
      disabled: true,
    },
    multiple: {
      label: 'Allow multiple',
      value: true,
      disabled: true, // 👈 disables editing in field editor
    },
    tag: {
      label: 'Tags',
      value: '',
    },
  },
}

export const disabledFields = ['hidden', 'button', 'select', 'paragraph', 'textarea']

export const disabledFieldButtons = {
  // location: ['edit'],
  // weather: ['edit'],
}

export const replaceFields = [
  {
    type: 'date',
    label: 'Date Field',
    // CheckBox: false,
    // value: new Date().toISOString().split('T')[0],
    attributes: {
      min: '1900-01-01',
      max: '2100-12-31',
    },
  },
]
