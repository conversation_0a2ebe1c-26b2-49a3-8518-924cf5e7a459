import * as Styled from './style'
import * as SharedStyled from '../../../../styles/styled'
import * as Yup from 'yup'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { onlyMmDdYyyy, onlyText } from '../../../../shared/helpers/regex'
import {
  dayjsFormat,
  daysInMonth,
  formatDateymd,
  getDataFromLocalStorage,
  getDaysInMonthFromDate,
  getFormattedDate,
  isDateDiffGreaterThan,
  isSuccess,
  notify,
  resetDateToUTC,
  startOfDate,
} from '../../../../shared/helpers/util'
import { Form, Formik } from 'formik'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import { Dropdown } from '../../../../shared/dropDown/Dropdown'
import { PERIOD, PERIOD_OBJ, PERIOD_OBJ1, St<PERSON><PERSON><PERSON> } from '../../../../shared/helpers/constants'
import { SharedDate } from '../../../../shared/date/SharedDate'
import { colors } from '../../../../styles/theme'
import { useSelector } from 'react-redux'
import { useNavigate, useParams } from 'react-router-dom'
import { deletePaySchedule, editPaySchedule, getPayScheduleById } from '../../../../logic/apis/paySchedule'
import dayjs from 'dayjs'
import 'dayjs/locale/en'
import Button from '../../../../shared/components/button/Button'
import CustomSelect from '../../../../shared/customSelect/CustomSelect'
import { AddPayScheduleTableContainer, TableDetail } from '../addPaySchedule/style'
import { Table } from '../../../../shared/table/Table'
import { generatePayPeriodsOne, generatePayPeriodsTwo } from '../addPaySchedule/AddPaySchedule'
/**
 * InitialValues is an interface declared here so that it can be used as a type for the useState hook
 */
interface InitialValues {
  name: string
  period: string
  periodEnd: string
  payDay: string
  periodEnd2: string
  payDay2: string
}

export const validateDateRange = (startDate: any, endDate: any) => {
  return isDateDiffGreaterThan(resetDateToUTC(startDate), resetDateToUTC(endDate), 20)
}

const EditPaySchedule = () => {
  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */
  let currentDate: any = new Date()
  const [initialValues, setInitialValues] = useState<InitialValues>({
    name: '',
    period: '',
    periodEnd: '',
    payDay: formatDateymd(currentDate) + '',
    periodEnd2: '',
    payDay2: formatDateymd(currentDate) + '',
  })

  const [nextPayPeriods, setNextPayPeriods] = useState<any>([])
  const [formValues, setFormValues] = useState<any>({})
  const [loading, setLoading] = useState<boolean>(false)
  const [getLoading, setgetLoading] = useState<boolean>(true)
  const [deleteLoading, setDeleteLoading] = useState<boolean>(false)
  const [data, setData] = useState<any>([])
  const fetchIdRef = useRef(0)

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company
  const { payScheduleId } = useParams()
  const navigate = useNavigate()
  const [pageCount, setPageCount] = useState<number>(10)

  const [payDayOneDropdown, setPayDayOneDropdown] = useState<number[]>([])
  const [payDayTwoDropdown, setPayDayTwoDropdown] = useState<number[]>([])

  /**
   * EditPayScheduleSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */
  const EditPayScheduleSchema = Yup.object().shape({
    name: Yup.string().min(2, 'Too Short!').max(50, 'Too Long!').required('Required'),
    period: Yup.string().min(2, 'Too Short!').max(50, 'Too Long!').required('Required'),
    periodEnd: Yup.string().min(2, 'Too Short!').max(50, 'Too Long!').required('Required'),
    payDay: Yup.string().required('Required'),
    periodEnd2: Yup.string()
      .min(2, 'Too Short!')
      .max(50, 'Too Long!')
      .test('periodEnd2', 'Date should be after first pay period', function (value: any) {
        const { periodEnd } = this.parent
        return formValues?.period === 'Twice per month' ? new Date(value) > new Date(periodEnd) : true
      }),
    payDay2: Yup.string(),
  })

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async (submittedValues: InitialValues) => {
    setLoading(true)
    try {
      const pDayOn = Number(new Date(submittedValues.payDay).getDate())

      const pDayOn2 = Number(new Date(submittedValues.payDay2).getDate())

      // if (Object.keys(currentCompany).length > 0) {
      if (Number(PERIOD_OBJ[submittedValues.period]) === 3) {
        if (validateDateRange(submittedValues?.periodEnd2, submittedValues.payDay2)) {
          notify(
            'You’ve entered a pay date that is over 20 days after the pay period ends, please choose a date closer',
            'error'
          )
          return
        }
      }

      if (validateDateRange(submittedValues?.periodEnd, submittedValues.payDay)) {
        notify(
          'You’ve entered a pay date that is over 20 days after the pay period ends, please choose a date closer',
          'error'
        )
        return
      }

      let payId: any = payScheduleId
      let dataObj = {
        name: submittedValues.name,
        period: Number(PERIOD_OBJ[submittedValues.period]),
        payPeriodEndsOn: startOfDate(dayjsFormat(submittedValues?.periodEnd, 'YYYY-MM-DD')),
        paydayOn: pDayOn,
        payPeriodEndsOn2:
          submittedValues.periodEnd2 && submittedValues?.period === 'Twice per month'
            ? startOfDate(dayjsFormat(submittedValues?.periodEnd2, 'YYYY-MM-DD'))
            : undefined,
        paydayOn2: submittedValues.payDay2 ? pDayOn2 : undefined,
        createdBy: currentMember._id,
      }

      debugger

      let response = await editPaySchedule(dataObj, payId)
      if (isSuccess(response)) {
        notify('Edited payschedule successfully', 'success')
        setLoading(false)
      } else {
        notify(response?.data?.message, 'error')
        setLoading(false)
      }
      // }
    } catch (error) {
      console.error('EditPaySchedule handleSubmit', error)
      setLoading(false)
    } finally {
      setLoading(false)
    }
  }

  const onDelete = async () => {
    try {
      // if (Object.keys(currentCompany).length > 0) {
      setDeleteLoading(true)

      let id: any = payScheduleId

      const response = await deletePaySchedule({ id })
      if (isSuccess(response)) {
        notify('Deleted Pay Schedule Successfully', 'success')
        setDeleteLoading(false)
        navigate(`/settings/pay-schedule`)
      } else {
        notify(response?.data?.message, 'error')
        setDeleteLoading(false)
      }
      // }
    } catch (error) {
      console.error('onDelete error', error)
      setDeleteLoading(false)
    }
  }

  const getNextPayPeriodsAndPayDays = async () => {
    try {
      if (Object.keys(formValues).length > 0 && formValues.period !== '') {
        let periodEndDate1 = new Date(formValues.periodEnd + 'T23:59:59.999')
        let increment: any
        let payPeriods: any = []
        if (formValues.period === 'Every week' || formValues.period === 'Every other week') {
          formValues.period === 'Every week' ? (increment = 7) : (increment = 14)

          let startDateInitial = new Date(periodEndDate1)
          startDateInitial.setDate(startDateInitial.getDate() - (increment - 1))
          startDateInitial.setHours(0, 0, 0, 0)
          let data = await Promise.all(
            Array(4)
              .fill(0)
              .map((period: number, index: number) => {
                let startDate = new Date(startDateInitial)
                let endDate = new Date(periodEndDate1)
                let payDate = new Date(formValues.payDay + 'T00:00')

                let payPeriodObj: any = {}
                let payPeriodStart = dayjs(new Date(startDate.setDate(startDate.getDate() + increment * index))).format(
                  'MM/DD/YYYY'
                )
                let payPeriodEnd = dayjs(new Date(endDate.setDate(endDate.getDate() + increment * index))).format(
                  'MM/DD/YYYY'
                )
                let payDayDate = dayjs(new Date(payDate.setDate(payDate.getDate() + increment * index))).format(
                  'MM/DD/YYYY'
                )
                payPeriodObj.payPeriod = `${payPeriodStart} - ${payPeriodEnd}`
                payPeriodObj.payDay = `${payDayDate}`
                return payPeriodObj
                // setNextPayPeriods([...nextPayPeriods, payPeriodObj])
              })
          )
          setNextPayPeriods(data)
        } else if (formValues.period === 'Once per month') {
          let thisMonth = periodEndDate1.getMonth()
          let start = new Date(periodEndDate1)
          start.setDate(start.getDate() + 1)
          let data = await Promise.all(
            Array(4)
              .fill(0)
              .map((period: number, index: number) => {
                let startDate = start.getDate()
                let endDate

                if (daysInMonth(periodEndDate1.getFullYear(), thisMonth) === periodEndDate1.getDate()) {
                  endDate = 31
                } else {
                  endDate = periodEndDate1.getDate()
                }

                let payDate = new Date(formValues.payDay)?.getDate()
                let payPeriodObj: any = {}
                let thisMonthDays = daysInMonth(periodEndDate1.getFullYear(), thisMonth + index)

                let startMonth = thisMonth - 1
                let payMonth = thisMonth

                if (thisMonthDays < endDate) {
                  endDate = thisMonthDays
                }
                if (thisMonthDays === endDate) {
                  startMonth++
                }
                if (thisMonthDays < payDate) {
                  payDate = thisMonthDays
                }
                if (payDate <= endDate && payMonth === thisMonth) {
                  payMonth++
                }

                let payPeriodStart = dayjs(
                  new Date(periodEndDate1.getFullYear(), startMonth + index, startDate, 0, 0, 0, 0)
                ).format('MM/DD/YYYY')
                let payPeriodEnd = dayjs(
                  new Date(periodEndDate1.getFullYear(), thisMonth + index, endDate, 23, 59, 59, 999)
                ).format('MM/DD/YYYY')

                payPeriodObj.payPeriod = `${payPeriodStart} - ${payPeriodEnd}`
                payPeriodObj.payDay = dayjs(
                  new Date(periodEndDate1.getFullYear(), payMonth + index, payDate, 0, 0, 0, 0)
                ).format('MM/DD/YYYY')

                return payPeriodObj
              })
          )

          setNextPayPeriods(data)
        } else if (formValues.period === 'Twice per month') {
          const isSecondHalf = periodEndDate1?.getDate() > 15
          const range = isSecondHalf ? generatePayPeriodsTwo(formValues) : generatePayPeriodsOne(formValues)
          let payDay1 = formValues.payDay
          let periodEndDate2 = new Date(formValues.periodEnd2 + 'T23:59:59.999')
          let payDay2 = formValues.payDay2
          let payDate1 = payDay1
          let payDate2 = payDay2
          let endDate1 = periodEndDate1.getDate()
          let endDate2 = periodEndDate2.getDate()
          let startDate1 = new Date(periodEndDate2)
          startDate1.setDate(startDate1.getDate() + 1)
          let startDate11 = startDate1.getDate()
          let startDate2 = new Date(periodEndDate1)
          startDate2.setDate(startDate2.getDate() + 1)
          let startDate22 = startDate2.getDate()
          await Promise.all(
            Array(2)
              .fill(0)
              .map((period: number, index: number) => {
                let thisMonth = periodEndDate1.getMonth()
                let thisYear = periodEndDate1.getFullYear()
                let thisMonthDays = daysInMonth(thisYear, thisMonth + index)
                let nextMonthDays = daysInMonth(thisYear, thisMonth + 1 + index)

                let s1 = startDate11
                let s2 = startDate22
                let e1 = endDate1
                let e2 = endDate2
                let p1 = payDate1
                let p2 = payDate2
                let start1Month = thisMonth
                let end1Month = thisMonth + 1
                let pay1Month = thisMonth + 2

                let payPeriodObj: any = {}
                let payPeriodStart = dayjs(new Date(thisYear, start1Month + index, s1, 0, 0, 0, 0)).format('MM/DD/YYYY')
                if (thisMonthDays < e1) {
                  e1 = thisMonthDays
                }
                if (e1 > s1) {
                  end1Month--
                  pay1Month--
                }
                let payPeriodEnd = dayjs(new Date(thisYear, end1Month + index, e1, 23, 59, 59, 999)).format(
                  'MM/DD/YYYY'
                )

                if (thisMonthDays < p1) {
                  p1 = thisMonthDays
                }

                if (payDate1 > endDate1) {
                  pay1Month--
                }

                payPeriodObj.payPeriod = `${payPeriodStart} - ${payPeriodEnd}`
                payPeriodObj.payDay = dayjs(new Date(thisYear, pay1Month + index, p1, 0, 0, 0, 0)).format('MM/DD/YYYY')
                // setNextPayPeriods([...nextPayPeriods, payPeriodObj])
                payPeriods.push(payPeriodObj)

                let payPeriodObj2: any = {}

                if (nextMonthDays < s2) {
                  s2 = nextMonthDays
                }

                start1Month++
                if (s2 < e1) {
                  end1Month++
                }

                let payPeriodStart2 = dayjs(new Date(thisYear, start1Month + index, s2, 0, 0, 0, 0)).format(
                  'MM/DD/YYYY'
                )
                if (nextMonthDays < e2) {
                  e2 = nextMonthDays
                }
                let payPeriodEnd2 = dayjs(new Date(thisYear, end1Month + index, e2, 23, 59, 59, 999)).format(
                  'MM/DD/YYYY'
                )
                if (nextMonthDays < p2) {
                  p2 = nextMonthDays
                }
                if (payDate2 < endDate2) {
                  pay1Month++
                }
                payPeriodObj2.payPeriod = `${payPeriodStart2} - ${payPeriodEnd2}`
                payPeriodObj2.payDay = dayjs(new Date(thisYear, pay1Month + index, p2, 0, 0, 0, 0)).format('MM/DD/YYYY')
                // setNextPayPeriods([...nextPayPeriods, payPeriodObj2])

                payPeriods.push(payPeriodObj2)
              })
          )
          setNextPayPeriods(range)
        }
      }
    } catch (error) {
      console.error('getNextPayPeriodsAndPayDays error', error)
    }
  }
  const fetchData = useCallback(
    async ({ pageSize, pageIndex }: any) => {
      try {
        // Give this fetch an ID
        const fetchId = ++fetchIdRef.current

        // We'll even set a delay to simulate a server here
        setTimeout(() => {
          // Only update the data if this is the latest fetch
          if (fetchId === fetchIdRef.current) {
            const startRow = pageSize * pageIndex
            const endRow = startRow + pageSize
            setData(nextPayPeriods.slice(startRow, endRow))

            // Your server could send back total page count.
            // For now we'll just fake it, too
            setPageCount(Math.ceil(nextPayPeriods.length / pageSize))
            // setLoading(false)
          }
        }, 1000)
      } catch (error) {
        console.error('PaySchedule fetchData error', error)
      }
    },
    [nextPayPeriods]
  )

  const getDetails = async () => {
    try {
      // if (Object.keys(currentCompany).length > 0) {
      let payId: any = payScheduleId

      let dataObj = {
        deleted: false,
      }
      const response = await getPayScheduleById(dataObj, payId)
      if (isSuccess(response)) {
        let paySched = response?.data?.data?.paySchedule

        const firstPayPeriodMonth =
          Math.sign(new Date(paySched?.payPeriodEndsOn).getDate() - paySched?.paydayOn) > 0
            ? new Date(paySched?.payPeriodEndsOn)?.getMonth() + 1
            : new Date(paySched?.payPeriodEndsOn)?.getMonth()

        const secondPayPeriodMonth =
          Math.sign(new Date(paySched?.payPeriodEndsOn2).getDate() - paySched?.paydayOn2) > 0
            ? new Date(paySched?.payPeriodEndsOn2)?.getMonth() + 1
            : new Date(paySched?.payPeriodEndsOn2)?.getMonth()

        const firstPayPeriodYear = new Date(paySched?.payPeriodEndsOn).getFullYear()
        const secondPayPeriodYear = new Date(paySched?.payPeriodEndsOn2).getFullYear()

        let userObject = {
          name: paySched.name,
          period: PERIOD_OBJ1[paySched.period],
          periodEnd: dayjsFormat(paySched?.payPeriodEndsOn, 'YYYY-MM-DD'),
          payDay: paySched?.paydayOn
            ? dayjsFormat(
                dayjs(new Date())
                  .set('date', paySched?.paydayOn)
                  .set('month', firstPayPeriodMonth)
                  .set('year', firstPayPeriodYear),
                'YYYY-MM-DD'
              )
            : '',
          periodEnd2: dayjsFormat(paySched?.payPeriodEndsOn2, 'YYYY-MM-DD'),
          payDay2: paySched?.paydayOn2
            ? dayjsFormat(
                dayjs(new Date())
                  .set('date', paySched?.paydayOn2)
                  .set('month', secondPayPeriodMonth)
                  .set('year', secondPayPeriodYear),
                'YYYY-MM-DD'
              )
            : '',
        }
        setInitialValues({ ...initialValues, ...userObject })
        setgetLoading(false)
      } else {
        notify(response?.data?.message, 'error')
        setgetLoading(false)
      }
      // }
    } catch (error) {
      console.error('getDetails error', error)
      setgetLoading(false)
    }
  }

  useEffect(() => {
    getNextPayPeriodsAndPayDays()
  }, [formValues])

  useEffect(() => {
    getDetails()
  }, [])

  const getPayDayTwoVal = () => {
    const array = []
    for (var i = 13; i < 31; i++) {
      array.push(i + 1)
    }
    return array
  }

  const columns: any = useMemo(
    () => [
      {
        Header: 'Pay Period',
        accessor: 'payPeriod', // accessor is the "key" in the data
      },
      {
        Header: 'Pay Day',
        accessor: 'payDay',
      },
    ],
    []
  )

  return getLoading ? (
    <>
      <SharedStyled.Skeleton custWidth="100%" custHeight={'51px'} />
      <SharedStyled.Skeleton custWidth="100%" custHeight="50%" custMarginTop="10px" />
      <SharedStyled.Skeleton custWidth="100%" custHeight="50%" custMarginTop="10px" />
    </>
  ) : (
    <Styled.EditPayScheduleContainer>
      <SharedStyled.SectionTitle>Edit Pay Schedule</SharedStyled.SectionTitle>
      <Formik
        initialValues={initialValues}
        onSubmit={handleSubmit}
        validationSchema={EditPayScheduleSchema}
        validateOnChange={true}
        validateOnBlur={false}
        enableReinitialize={true}
      >
        {({ values, errors, touched, setFieldValue }: any) => {
          useEffect(() => {
            const days = getDaysInMonthFromDate(values?.periodEnd) || 31
            const date = new Date(values?.periodEnd)?.getDate()
            if (values.period === 'Twice per month') {
              const array = []
              for (var i = 0; i < 31; i++) {
                array.push(i + 1)
              }

              setPayDayOneDropdown(array)
            } else {
              const array = []
              const startDay = days === date ? 0 : date
              for (var i = startDay; i < days; i++) {
                array.push(i + 1)
              }
              setPayDayOneDropdown(array)
            }

            setPayDayTwoDropdown(getPayDayTwoVal())
          }, [values])

          setFormValues(values)

          return (
            <Form className="form">
              <InputWithValidation
                labelName="Name"
                stateName="name"
                error={touched.name && errors.name ? true : false}
              />
              {/* <Dropdown
                value={values.period}
                labelName="How often to pay"
                stateName="period"
                dropDownData={PERIOD}
                setFieldValue={setFieldValue}
                error={touched.period && errors.period ? true : false}
              /> */}

              <CustomSelect
                value={values.period}
                labelName="How often to pay"
                stateName="period"
                dropDownData={PERIOD}
                setValue={() => {
                  setFieldValue('payDay', '')
                  setFieldValue('periodEnd', '')
                  setFieldValue('payDay2', '')
                  setFieldValue('periodEnd2', '')
                }}
                setFieldValue={setFieldValue}
                error={touched.period && errors.period ? true : false}
                margin="10px 0 0 0"
              />

              <SharedDate
                value={values.periodEnd}
                labelName="1st pay period ends on"
                stateName="periodEnd"
                error={touched.periodEnd && errors.periodEnd ? true : false}
                setFieldValue={setFieldValue}
                onBlur={() => {
                  setFieldValue('payDay', '')
                }}
              />

              <SharedDate
                value={values.payDay}
                labelName="1st Payday is on"
                stateName="payDay"
                setFieldValue={setFieldValue}
                min={dayjs(values.periodEnd).add(1, 'day').format('YYYY-MM-DD')}
                error={touched.payDay && errors.payDay ? true : false}
              />

              {/* <CustomSelect
                value={values.payDay}
                labelName="1st Payday is on"
                stateName="payDay"
                dropDownData={payDayOneDropdown}
                setFieldValue={setFieldValue}
                setValue={() => {}}
                error={touched.payDay && errors.payDay ? true : false}
                margin="10px 0 0 0"
              /> */}

              {values.period === 'Twice per month' && (
                <>
                  <SharedDate
                    value={values.periodEnd2}
                    labelName="2nd pay period ends on"
                    stateName="periodEnd2"
                    error={touched.periodEnd2 && errors.periodEnd2 ? true : false}
                    setFieldValue={setFieldValue}
                    onBlur={() => {
                      setFieldValue('payDay2', '')
                    }}
                  />
                  <SharedDate
                    value={values.payDay2}
                    labelName="2nd Payday is on"
                    stateName="payDay2"
                    setFieldValue={setFieldValue}
                    min={dayjs(values.periodEnd2).add(1, 'day').format('YYYY-MM-DD')}
                  />

                  {/* <CustomSelect
                    value={values.payDay2}
                    dropDownData={payDayTwoDropdown}
                    labelName="2nd Payday is on"
                    stateName="payDay2"
                    setValue={() => {}}
                    setFieldValue={setFieldValue}
                    margin="10px 0 0 0"
                  /> */}
                </>
              )}
              {formValues.period && (
                <AddPayScheduleTableContainer>
                  <TableDetail marginTop="30px">The next 4 pay periods (based on the dates you entered)</TableDetail>
                  <Table
                    columns={columns}
                    data={data}
                    loading={loading}
                    pageCount={pageCount}
                    fetchData={fetchData}
                    noPagination={true}
                    noSearch={true}
                    noLink={true}
                  />
                </AddPayScheduleTableContainer>
              )}
              <SharedStyled.ButtonContainer marginTop="20px">
                <Button type="submit" width="max-content" isLoading={loading}>
                  Edit Pay Schedule
                </Button>
              </SharedStyled.ButtonContainer>
            </Form>
          )
        }}
      </Formik>
    </Styled.EditPayScheduleContainer>
  )
}
export default EditPaySchedule
