import * as Yup from 'yup'
import * as SharedStyled from '../../../../styles/styled'
import * as Styled from './style'
import dayjs from 'dayjs'
import 'dayjs/locale/en'
import { Form, Formik } from 'formik'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate, useParams } from 'react-router-dom'

import { createPaySchedule } from '../../../../logic/apis/paySchedule'
import { SharedDate } from '../../../../shared/date/SharedDate'
import { Dropdown } from '../../../../shared/dropDown/Dropdown'
import { PERIOD, PERIOD_OBJ, StorageKey } from '../../../../shared/helpers/constants'
import { onlyMmDdYyyy, onlyText } from '../../../../shared/helpers/regex'
import {
  daysInMonth,
  formatDateymd,
  getDataFromLocalStorage,
  getDaysInMonthFromDate,
  getFormattedDate,
  isSuccess,
  notify,
  startOfDate,
} from '../../../../shared/helpers/util'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import { Table } from '../../../../shared/table/Table'
import Button from '../../../../shared/components/button/Button'
import CustomSelect from '../../../../shared/customSelect/CustomSelect'
import { validateDateRange } from '../editPaySchedule/EditPaySchedule'
/**
 * InitialValues is an interface declared here so that it can be used as a type for the useState hook
 */

interface InitialValues {
  name: string
  period: string
  periodEnd: string
  payDay: string
  periodEnd2: string
  payDay2: string
}
export function generatePayPeriodsOne(args: any, iterations = 2) {
  const { periodEnd, payDay, periodEnd2, payDay2 } = args
  const result = []

  // Helper function to format dates as MM/DD/YYYY in UTC
  const formatUTCDate = (date: any) =>
    date.toLocaleDateString('en-US', { year: 'numeric', month: '2-digit', day: '2-digit', timeZone: 'UTC' })

  // Initialize the start date as the first day of the month in UTC
  let startDate = new Date(Date.UTC(new Date(periodEnd).getUTCFullYear(), new Date(periodEnd).getUTCMonth(), 1))

  for (let i = 0; i < iterations; i++) {
    // First period
    const periodEndDate1 = new Date(
      Date.UTC(startDate.getUTCFullYear(), startDate.getUTCMonth(), new Date(periodEnd).getUTCDate())
    )
    const payDate1 = new Date(
      Date.UTC(startDate.getUTCFullYear(), startDate.getUTCMonth(), new Date(payDay)?.getDate())
    )

    result.push({
      payPeriod: `${formatUTCDate(startDate)} - ${formatUTCDate(periodEndDate1)}`,
      payDay: formatUTCDate(payDate1),
    })

    // Second period
    const periodStartDate2 = new Date(
      Date.UTC(periodEndDate1.getUTCFullYear(), periodEndDate1.getUTCMonth(), periodEndDate1.getUTCDate() + 1)
    )
    const lastDayOfMonth = new Date(
      Date.UTC(periodStartDate2.getUTCFullYear(), periodStartDate2.getUTCMonth() + 1, 0)
    ).getUTCDate()
    const periodEndDate2 = new Date(
      Date.UTC(
        startDate.getUTCFullYear(),
        startDate.getUTCMonth(),
        Math.min(lastDayOfMonth, new Date(periodEnd2).getUTCDate())
      )
    )

    // Ensure the second period ends on the last day of the month if applicable
    if (periodEndDate2.getUTCDate() < lastDayOfMonth) {
      periodEndDate2.setUTCDate(lastDayOfMonth)
    }

    const payDate2 = new Date(
      Date.UTC(startDate.getUTCFullYear(), startDate.getUTCMonth(), new Date(payDay2)?.getDate())
    )
    // If payDate2 is earlier in the month than the period end, move it to the next month
    if (payDate2.getUTCDate() <= periodEndDate2.getUTCDate()) {
      payDate2.setUTCMonth(payDate2.getUTCMonth() + 1)
    }

    result.push({
      payPeriod: `${formatUTCDate(periodStartDate2)} - ${formatUTCDate(periodEndDate2)}`,
      payDay: formatUTCDate(payDate2),
    })

    // Move to the next month
    startDate.setUTCMonth(startDate.getUTCMonth() + 1)
    startDate.setUTCDate(1) // Reset to the first day of the new month
  }

  return result
}

export function generatePayPeriodsTwo(args: any, iterations = 2) {
  const { periodEnd, payDay, periodEnd2, payDay2 } = args
  const result = []

  const formatUTCDate = (date: any) =>
    date.toLocaleDateString('en-US', { year: 'numeric', month: '2-digit', day: '2-digit', timeZone: 'UTC' })

  let periodStartDate = new Date(Date.UTC(new Date(periodEnd).getUTCFullYear(), new Date(periodEnd).getUTCMonth(), 16))

  for (let i = 0; i < iterations; i++) {
    const lastDayOfMonth = new Date(
      Date.UTC(periodStartDate.getUTCFullYear(), periodStartDate.getUTCMonth() + 1, 0)
    ).getUTCDate()

    // First pay period (16th to end of the month)
    const periodEndDate1 = new Date(
      Date.UTC(periodStartDate.getUTCFullYear(), periodStartDate.getUTCMonth(), lastDayOfMonth)
    )

    const payDate1Day = Math.min(new Date(payDay)?.getDate(), lastDayOfMonth)

    const payDate1 = new Date(Date.UTC(periodEndDate1.getUTCFullYear(), periodEndDate1.getUTCMonth() + 1, payDate1Day))

    result.push({
      payPeriod: `${formatUTCDate(periodStartDate)} - ${formatUTCDate(periodEndDate1)}`,
      payDay: formatUTCDate(payDate1),
    })

    const nextMonthStartDate = new Date(Date.UTC(periodEndDate1.getUTCFullYear(), periodEndDate1.getUTCMonth() + 1, 1))
    const nextMonthLastDay = new Date(
      Date.UTC(nextMonthStartDate.getUTCFullYear(), nextMonthStartDate.getUTCMonth() + 1, 0)
    ).getUTCDate()

    const endDateForNextMonth = new Date(
      Date.UTC(
        nextMonthStartDate.getUTCFullYear(),
        nextMonthStartDate.getUTCMonth(),
        Math.min(new Date(periodEnd2).getUTCDate(), nextMonthLastDay)
      )
    )

    const payDate2Day = Math.min(new Date(payDay2)?.getDate(), nextMonthLastDay)
    const payDate2 = new Date(
      Date.UTC(endDateForNextMonth.getUTCFullYear(), endDateForNextMonth.getUTCMonth(), payDate2Day)
    )

    result.push({
      payPeriod: `${formatUTCDate(nextMonthStartDate)} - ${formatUTCDate(endDateForNextMonth)}`,
      payDay: formatUTCDate(payDate2),
    })

    periodStartDate = new Date(Date.UTC(endDateForNextMonth.getUTCFullYear(), endDateForNextMonth.getUTCMonth(), 16))
  }

  return result
}

const AddPaySchedule = () => {
  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */
  const [initialValues, setInitialValues] = useState<InitialValues>({
    name: '',
    period: '',
    periodEnd: '',
    payDay: '',
    periodEnd2: '',
    payDay2: '',
  })

  const [formValues, setFormValues] = useState<any>({})

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company

  /**
   * AddPayScheduleSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */
  const AddPayScheduleSchema = Yup.object().shape({
    name: Yup.string().min(2, 'Too Short!').max(50, 'Too Long!').required('Required'),
    period: Yup.string().min(2, 'Too Short!').max(50, 'Too Long!').required('Required'),
    periodEnd: Yup.string().min(2, 'Too Short!').max(50, 'Too Long!').required('Required'),
    payDay: Yup.string().required('Required'),
    periodEnd2: Yup.string()
      .min(2, 'Too Short!')
      .max(50, 'Too Long!')
      .test('periodEnd2', 'Date should be after first pay period', function (value: any) {
        const { periodEnd } = this.parent
        return formValues?.period === 'Twice per month' ? new Date(value) > new Date(periodEnd) : true
      }),
    payDay2: Yup.string().max(50, 'Too Long!'),
  })

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async (submittedValues: InitialValues, { resetForm }: any) => {
    setLoading(true)

    try {
      // if (Object.keys(currentCompany).length > 0) {
      let currentDate: any = new Date()

      if (Number(PERIOD_OBJ[submittedValues.period]) === 3) {
        if (validateDateRange(submittedValues?.periodEnd2, submittedValues?.payDay2!)) {
          notify(
            'You’ve entered a pay date that is over 20 days after the pay period ends, please choose a date closer',
            'error'
          )
          return
        }
      }

      if (validateDateRange(submittedValues?.periodEnd, submittedValues?.payDay)) {
        notify(
          'You’ve entered a pay date that is over 20 days after the pay period ends, please choose a date closer',
          'error'
        )
        return
      }

      let dataObj = {
        name: submittedValues.name,
        period: Number(PERIOD_OBJ[submittedValues.period]),
        payPeriodEndsOn: formatDateymd(getFormattedDate(submittedValues.periodEnd)),
        paydayOn: Number(new Date(submittedValues.payDay).getDate()),
        payPeriodEndsOn2:
          submittedValues.periodEnd2 && submittedValues?.period === 'Twice per month'
            ? formatDateymd(getFormattedDate(submittedValues.periodEnd2))
            : undefined,
        paydayOn2: submittedValues.payDay2 ? Number(new Date(submittedValues.payDay2).getDate()) : undefined,
        createdBy: currentMember._id,
      }

      let response = await createPaySchedule(dataObj)

      if (isSuccess(response)) {
        notify('Created payschedule successfully', 'success')
        resetForm()
        setLoading(false)
        navigate(`/settings/pay-schedule`)
      } else {
        notify(response?.data?.message, 'error')
        setLoading(false)
      }
      // }
    } catch (error) {
      console.error('AddPaySchedule handleSubmit', error)
      setLoading(false)
    } finally {
      setLoading(false)
    }
  }

  const [loading, setLoading] = useState<boolean>(false)
  const [pageCount, setPageCount] = useState<number>(10)
  const [data, setData] = useState<any>([])
  const [nextPayPeriods, setNextPayPeriods] = useState<any>([])
  const fetchIdRef = useRef(0)

  // const [payDayOneDropdown, setPayDayOneDropdown] = useState<number[]>([])
  // const [payDayTwoDropdown, setPayDayTwoDropdown] = useState<number[]>([])

  const navigate = useNavigate()

  const fetchData = useCallback(
    async ({ pageSize, pageIndex }: any) => {
      try {
        // Give this fetch an ID
        const fetchId = ++fetchIdRef.current

        // Set the loading state
        // setLoading(true)

        // We'll even set a delay to simulate a server here
        setTimeout(() => {
          // Only update the data if this is the latest fetch
          if (fetchId === fetchIdRef.current) {
            const startRow = pageSize * pageIndex
            const endRow = startRow + pageSize
            setData(nextPayPeriods.slice(startRow, endRow))

            // Your server could send back total page count.
            // For now we'll just fake it, too
            setPageCount(Math.ceil(nextPayPeriods.length / pageSize))
            // setLoading(false)
          }
        }, 1000)
      } catch (error) {
        console.error('PaySchedule fetchData error', error)
      }
    },
    [nextPayPeriods]
  )

  const columns: any = useMemo(
    () => [
      {
        Header: 'Pay Period',
        accessor: 'payPeriod', // accessor is the "key" in the data
      },
      {
        Header: 'Pay Day',
        accessor: 'payDay',
      },
    ],
    []
  )

  const getNextPayPeriodsAndPayDays = async () => {
    try {
      if (Object.keys(formValues).length > 0 && formValues.period !== '') {
        let periodEndDate1 = new Date(formValues.periodEnd + 'T23:59:59.999')
        let increment: any
        let payPeriods: any = []
        if (formValues.period === 'Every week' || formValues.period === 'Every other week') {
          formValues.period === 'Every week' ? (increment = 7) : (increment = 14)

          let startDateInitial = new Date(periodEndDate1)
          startDateInitial.setDate(startDateInitial.getDate() - (increment - 1))
          startDateInitial.setHours(0, 0, 0, 0)
          let data = await Promise.all(
            Array(4)
              .fill(0)
              .map((period: number, index: number) => {
                let startDate = new Date(startDateInitial)
                let endDate = new Date(periodEndDate1)

                let payDate = new Date(formValues.payDay + 'T00:00')
                let payPeriodObj: any = {}
                let payPeriodStart = dayjs(new Date(startDate.setDate(startDate.getDate() + increment * index))).format(
                  'MM/DD/YYYY'
                )
                let payPeriodEnd = dayjs(new Date(endDate.setDate(endDate.getDate() + increment * index))).format(
                  'MM/DD/YYYY'
                )
                let payDayDate = dayjs(new Date(payDate.setDate(payDate.getDate() + increment * index))).format(
                  'MM/DD/YYYY'
                )
                payPeriodObj.payPeriod = `${payPeriodStart} - ${payPeriodEnd}`
                payPeriodObj.payDay = `${payDayDate}`
                return payPeriodObj
                // setNextPayPeriods([...nextPayPeriods, payPeriodObj])
              })
          )
          setNextPayPeriods(data)
        } else if (formValues.period === 'Once per month') {
          let thisMonth = periodEndDate1.getMonth()
          let start = new Date(periodEndDate1)
          start.setDate(start.getDate() + 1)
          let data = await Promise.all(
            Array(4)
              .fill(0)
              .map((period: number, index: number) => {
                let startDate = start.getDate()
                let endDate

                if (daysInMonth(periodEndDate1.getFullYear(), thisMonth) === periodEndDate1.getDate()) {
                  endDate = 31
                } else {
                  endDate = periodEndDate1.getDate()
                }

                let payDate = new Date(formValues.payDay)?.getDate()
                let payPeriodObj: any = {}
                let thisMonthDays = daysInMonth(periodEndDate1.getFullYear(), thisMonth + index)

                let startMonth = thisMonth - 1
                let payMonth = thisMonth

                if (thisMonthDays < endDate) {
                  endDate = thisMonthDays
                }
                if (thisMonthDays === endDate) {
                  startMonth++
                }
                if (thisMonthDays < payDate) {
                  payDate = thisMonthDays
                }
                if (payDate <= endDate && payMonth === thisMonth) {
                  payMonth++
                }

                let payPeriodStart = dayjs(
                  new Date(periodEndDate1.getFullYear(), startMonth + index, startDate, 0, 0, 0, 0)
                ).format('MM/DD/YYYY')
                let payPeriodEnd = dayjs(
                  new Date(periodEndDate1.getFullYear(), thisMonth + index, endDate, 23, 59, 59, 999)
                ).format('MM/DD/YYYY')

                payPeriodObj.payPeriod = `${payPeriodStart} - ${payPeriodEnd}`
                payPeriodObj.payDay = dayjs(
                  new Date(periodEndDate1.getFullYear(), payMonth + index, payDate, 0, 0, 0, 0)
                ).format('MM/DD/YYYY')

                return payPeriodObj
              })
          )

          setNextPayPeriods(data)
        } else if (formValues.period === 'Twice per month') {
          const isSecondHalf = periodEndDate1?.getDate() > 15

          const range = isSecondHalf ? generatePayPeriodsTwo(formValues) : generatePayPeriodsOne(formValues)

          setNextPayPeriods(range)
        }
      }
    } catch (error) {
      console.error('getNextPayPeriodsAndPayDays error', error)
    }
  }

  useEffect(() => {
    getNextPayPeriodsAndPayDays()
  }, [formValues])

  const getPayDayTwoVal = (days: number = 31) => {
    const array = []
    for (var i = 0; i < days; i++) {
      array.push(i + 1)
    }
    return array
  }

  return (
    <Styled.AddPayScheduleContainer>
      <SharedStyled.SectionTitle>Add Pay Schedule</SharedStyled.SectionTitle>
      <Formik
        initialValues={initialValues}
        onSubmit={handleSubmit}
        validationSchema={AddPayScheduleSchema}
        validateOnChange={true}
        validateOnBlur={false}
        enableReinitialize={true}
      >
        {({ values, errors, touched, setFieldValue, handleSubmit }: any) => {
          setFormValues(values)

          // useEffect(() => {
          //   const days = getDaysInMonthFromDate(values?.periodEnd) || 31
          //   const date = new Date(values?.periodEnd)?.getDate()
          //   if (values.period === 'Twice per month') {
          //     const array = []
          //     for (var i = 0; i < 31; i++) {
          //       array.push(i + 1)
          //     }

          //     setPayDayOneDropdown(array)
          //   } else {
          //     const startDay = days === date ? 0 : date
          //     const array = []
          //     for (var i = startDay; i < days; i++) {
          //       array.push(i + 1)
          //     }
          //     setPayDayOneDropdown(array)
          //   }

          //   setPayDayTwoDropdown(getPayDayTwoVal())
          // }, [values])

          return (
            <Form className="form">
              <InputWithValidation
                labelName="Name"
                stateName="name"
                error={touched.name && errors.name ? true : false}
              />
              {/* <Dropdown
                value={values.period}
                labelName="How often to pay"
                stateName="period"
                dropDownData={PERIOD}
                setFieldValue={setFieldValue}
                error={touched.period && errors.period ? true : false}
              /> */}

              <CustomSelect
                value={values.period}
                labelName="How often to pay"
                stateName="period"
                dropDownData={PERIOD}
                setValue={() => {
                  setFieldValue('payDay', '')
                  setFieldValue('periodEnd', '')
                  setFieldValue('payDay2', '')
                  setFieldValue('periodEnd2', '')
                }}
                setFieldValue={setFieldValue}
                error={touched.period && errors.period ? true : false}
                margin="10px 0 0 0"
              />

              <SharedDate
                value={values.periodEnd}
                labelName="1st pay period ends on"
                stateName="periodEnd"
                error={touched.periodEnd && errors.periodEnd ? true : false}
                setFieldValue={setFieldValue}
                onBlur={() => {
                  setFieldValue('payDay', '')
                }}
              />

              <SharedDate
                value={values.payDay}
                labelName="1st Payday is on"
                stateName="payDay"
                setFieldValue={setFieldValue}
                min={dayjs(values.periodEnd).add(1, 'day').format('YYYY-MM-DD')}
                error={touched.payDay && errors.payDay ? true : false}
              />

              {values.period === 'Twice per month' && (
                <>
                  <SharedDate
                    value={values.periodEnd2}
                    labelName="2nd pay period ends on"
                    stateName="periodEnd2"
                    error={touched.periodEnd2 && errors.periodEnd2 ? true : false}
                    setFieldValue={setFieldValue}
                    onBlur={() => {
                      setFieldValue('payDay2', '')
                    }}
                  />
                  <SharedDate
                    value={values.payDay2}
                    labelName="2nd Payday is on"
                    stateName="payDay2"
                    setFieldValue={setFieldValue}
                    min={dayjs(values.periodEnd2).add(1, 'day').format('YYYY-MM-DD')}
                  />

                  {/* <CustomSelect
                    value={values.payDay2}
                    dropDownData={payDayTwoDropdown}
                    labelName="2nd Payday is on"
                    stateName="payDay2"
                    setValue={() => {}}
                    setFieldValue={setFieldValue}
                    margin="10px 0 0 0"
                  /> */}
                </>
              )}

              {!!formValues.period && (
                <Styled.AddPayScheduleTableContainer>
                  <Styled.TableDetail marginTop="30px">
                    The next 4 pay periods (based on the dates you entered)
                  </Styled.TableDetail>
                  <Table
                    columns={columns}
                    data={data}
                    loading={loading}
                    pageCount={pageCount}
                    fetchData={fetchData}
                    noPagination={true}
                    noSearch={true}
                    noLink={true}
                  />
                </Styled.AddPayScheduleTableContainer>
              )}
              <SharedStyled.ButtonContainer marginTop="20px">
                <Button type="submit" width="max-content" onClick={handleSubmit} isLoading={loading}>
                  Add Pay Schedule
                </Button>
              </SharedStyled.ButtonContainer>
            </Form>
          )
        }}
      </Formik>
    </Styled.AddPayScheduleContainer>
  )
}

export default AddPaySchedule
