import { Form, Formik } from 'formik'
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate, useParams } from 'react-router-dom'
import * as Yup from 'yup'
import dayjs from 'dayjs'

import UnitSvg from '../../../assets/newIcons/unitModal.svg'
import { CrossIcon } from '../../../assets/icons/CrossIcon'
import { createPaySchedule } from '../../../logic/apis/paySchedule'
import { SharedDate } from '../../../shared/date/SharedDate'
import { Dropdown } from '../../../shared/dropDown/Dropdown'
import { PERIOD_OBJ, PERIOD, StorageKey } from '../../../shared/helpers/constants'
import {
  startOfDate,
  formatDateymd,
  getFormattedDate,
  notify,
  daysIn<PERSON>onth,
  getDataFromLocalStorage,
  isSuccess,
} from '../../../shared/helpers/util'
import { InputWithValidation } from '../../../shared/inputWithValidation/InputWithValidation'
import { Table } from '../../../shared/table/Table'
import { ModalHeaderInfo } from '../../units/components/newUnitModal/style'
import {
  CrossContainer,
  ModalContainer,
  ModalHeader,
  ModalHeaderContainer,
} from '../../taxJurisdiction/taxJurisdictionModal/style'
import { Button, ButtonContainer, FlexCol, FlexRow, Loader, SettingModalContentContainer } from '../../../styles/styled'
import { AddPayScheduleTableContainer, TableDetail } from './addPaySchedule/style'

interface InitialValues {
  name: string
  period: string
  periodEnd: string
  payDay: string
  periodEnd2: string
  payDay2: string
}

interface IMaterialsModal {
  onClose: () => void
  inputData: any
  onComplete: () => void
}

const PayScheduleModal: React.FC<IMaterialsModal> = (props) => {
  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */

  const { onClose, inputData, onComplete } = props
  const isEdit = inputData?.name

  const currentDate: any = new Date()
  const [initialValues, setInitialValues] = useState<InitialValues>({
    name: '',
    period: '',

    periodEnd: isEdit ? formatDateymd(currentDate) + '' : '',
    payDay: isEdit ? formatDateymd(currentDate) + '' : '',
    periodEnd2: isEdit ? formatDateymd(currentDate) + '' : '',
    payDay2: isEdit ? formatDateymd(currentDate) + '' : '',
  })

  const [formValues, setFormValues] = useState<any>({})

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company

  /**
   * AddPayScheduleSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */
  const AddPayScheduleSchema = Yup.object().shape({
    name: Yup.string().min(2, 'Too Short!').max(50, 'Too Long!').required('Required'),
    period: Yup.string().min(2, 'Too Short!').max(50, 'Too Long!').required('Required'),
    periodEnd: Yup.string().min(2, 'Too Short!').max(50, 'Too Long!').required('Required'),
    payDay: Yup.string(),
    periodEnd2: Yup.string().min(2, 'Too Short!').max(50, 'Too Long!'),
    payDay2: Yup.string(),
  })

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async (submittedValues: InitialValues, { resetForm }: any) => {
    setLoading(true)

    try {
      // if (Object.keys(currentCompany).length > 0) {
      let currentDate: any = new Date()
      let dataObj = {
        name: submittedValues.name,
        period: Number(PERIOD_OBJ[submittedValues.period]),
        payPeriodEndsOn: startOfDate(formatDateymd(getFormattedDate(submittedValues.periodEnd))),
        paydayOn: startOfDate(formatDateymd(getFormattedDate(submittedValues.payDay))),
        payPeriodEndsOn2: submittedValues.periodEnd2
          ? startOfDate(formatDateymd(getFormattedDate(submittedValues.periodEnd2)))
          : undefined,
        paydayOn2: submittedValues.payDay2
          ? startOfDate(formatDateymd(getFormattedDate(submittedValues.payDay2)))
          : undefined,
        createdBy: currentMember._id,
      }
      let response = await createPaySchedule(dataObj)
      if (isSuccess(response)) {
        notify('Created payschedule successfully', 'success')
        resetForm()
        setLoading(false)
        // navigate(`/settings/pay-schedule`)
        onComplete()
      } else {
        notify(response?.data?.message, 'error')
        setLoading(false)
      }
      // }
    } catch (error) {
      console.error('AddPaySchedule handleSubmit', error)
      setLoading(false)
    }
  }

  const [loading, setLoading] = useState<boolean>(false)
  const [pageCount, setPageCount] = useState<number>(10)
  const [data, setData] = useState<any>([])
  const [nextPayPeriods, setNextPayPeriods] = useState<any>([])
  const fetchIdRef = useRef(0)

  const navigate = useNavigate()

  const fetchData = useCallback(
    async ({ pageSize, pageIndex }: any) => {
      try {
        // Give this fetch an ID
        const fetchId = ++fetchIdRef.current

        // Set the loading state
        // setLoading(true)

        // We'll even set a delay to simulate a server here
        setTimeout(() => {
          // Only update the data if this is the latest fetch
          if (fetchId === fetchIdRef.current) {
            const startRow = pageSize * pageIndex
            const endRow = startRow + pageSize
            setData(nextPayPeriods.slice(startRow, endRow))

            // Your server could send back total page count.
            // For now we'll just fake it, too
            setPageCount(Math.ceil(nextPayPeriods.length / pageSize))
            // setLoading(false)
          }
        }, 1000)
      } catch (error) {
        console.error('PaySchedule fetchData error', error)
      }
    },
    [nextPayPeriods]
  )

  const columns: any = useMemo(
    () => [
      {
        Header: 'Pay Period',
        accessor: 'payPeriod', // accessor is the "key" in the data
      },
      {
        Header: 'Pay Day',
        accessor: 'payDay',
      },
    ],
    []
  )

  const getNextPayPeriodsAndPayDays = async () => {
    try {
      if (Object.keys(formValues).length > 0 && formValues.period !== '') {
        let periodEndDate1 = new Date(formValues.periodEnd + 'T23:59:59.999')
        let increment: any
        let payPeriods: any = []
        if (formValues.period === 'Every week' || formValues.period === 'Every other week') {
          formValues.period === 'Every week' ? (increment = 7) : (increment = 14)

          let startDateInitial = new Date(periodEndDate1)
          startDateInitial.setDate(startDateInitial.getDate() - (increment - 1))
          startDateInitial.setHours(0, 0, 0, 0)
          let data = await Promise.all(
            Array(4)
              .fill(0)
              .map((period: number, index: number) => {
                let startDate = new Date(startDateInitial)
                let endDate = new Date(periodEndDate1)
                let payDate = new Date(formValues.payDay + 'T00:00')
                let payPeriodObj: any = {}
                let payPeriodStart = dayjs(new Date(startDate.setDate(startDate.getDate() + increment * index))).format(
                  'MM/DD/YYYY'
                )
                let payPeriodEnd = dayjs(new Date(endDate.setDate(endDate.getDate() + increment * index))).format(
                  'MM/DD/YYYY'
                )
                let payDayDate = dayjs(new Date(payDate.setDate(payDate.getDate() + increment * index))).format(
                  'MM/DD/YYYY'
                )
                payPeriodObj.payPeriod = `${payPeriodStart} - ${payPeriodEnd}`
                payPeriodObj.payDay = `${payDayDate}`
                return payPeriodObj
                // setNextPayPeriods([...nextPayPeriods, payPeriodObj])
              })
          )
          setNextPayPeriods(data)
        } else if (formValues.period === 'Once per month') {
          let thisMonth = periodEndDate1.getMonth()
          let start = new Date(periodEndDate1)
          start.setDate(start.getDate() + 1)
          let data = await Promise.all(
            Array(4)
              .fill(0)
              .map((period: number, index: number) => {
                let startDate = start.getDate()
                let endDate

                if (daysInMonth(periodEndDate1.getFullYear(), thisMonth) === periodEndDate1.getDate()) {
                  endDate = 31
                } else {
                  endDate = periodEndDate1.getDate()
                }

                let payDate = new Date(formValues.payDay).getDate()
                let payPeriodObj: any = {}
                let thisMonthDays = daysInMonth(periodEndDate1.getFullYear(), thisMonth + index)

                let startMonth = thisMonth - 1
                let payMonth = thisMonth

                if (thisMonthDays < endDate) {
                  endDate = thisMonthDays
                }
                if (thisMonthDays === endDate) {
                  startMonth++
                }
                if (thisMonthDays < payDate) {
                  payDate = thisMonthDays
                }
                if (payDate <= endDate && payMonth === thisMonth) {
                  payMonth++
                }

                let payPeriodStart = dayjs(
                  new Date(periodEndDate1.getFullYear(), startMonth + index, startDate, 0, 0, 0, 0)
                ).format('MM/DD/YYYY')
                let payPeriodEnd = dayjs(
                  new Date(periodEndDate1.getFullYear(), thisMonth + index, endDate, 23, 59, 59, 999)
                ).format('MM/DD/YYYY')

                payPeriodObj.payPeriod = `${payPeriodStart} - ${payPeriodEnd}`
                payPeriodObj.payDay = dayjs(
                  new Date(periodEndDate1.getFullYear(), payMonth + index, payDate, 0, 0, 0, 0)
                ).format('MM/DD/YYYY')

                return payPeriodObj
              })
          )

          setNextPayPeriods(data)
        } else if (formValues.period === 'Twice per month') {
          let payDay1 = new Date(formValues.payDay).getDate()
          let periodEndDate2 = new Date(formValues.periodEnd2 + 'T23:59:59.999')
          let payDay2 = new Date(formValues.payDay2).getDate()

          let payDate1 = payDay1
          let payDate2 = payDay2
          let endDate1 = periodEndDate1.getDate()
          let endDate2 = periodEndDate2.getDate()
          let startDate1 = new Date(periodEndDate2)
          startDate1.setDate(startDate1.getDate() + 1)
          let startDate11 = startDate1.getDate()
          let startDate2 = new Date(periodEndDate1)
          startDate2.setDate(startDate2.getDate() + 1)
          let startDate22 = startDate2.getDate()
          await Promise.all(
            Array(2)
              .fill(0)
              .map((period: number, index: number) => {
                let thisMonth = periodEndDate1.getMonth()
                let thisYear = periodEndDate1.getFullYear()
                let thisMonthDays = daysInMonth(thisYear, thisMonth + index)
                let nextMonthDays = daysInMonth(thisYear, thisMonth + 1 + index)

                let s1 = startDate11
                let s2 = startDate22
                let e1 = endDate1
                let e2 = endDate2
                let p1 = payDate1
                let p2 = payDate2
                let start1Month = thisMonth
                let end1Month = thisMonth + 1
                let pay1Month = thisMonth + 2

                let payPeriodObj: any = {}
                let payPeriodStart = dayjs(new Date(thisYear, start1Month + index, s1, 0, 0, 0, 0)).format('MM/DD/YYYY')
                if (thisMonthDays < e1) {
                  e1 = thisMonthDays
                }
                if (e1 > s1) {
                  end1Month--
                  pay1Month--
                }
                let payPeriodEnd = dayjs(new Date(thisYear, end1Month + index, e1, 23, 59, 59, 999)).format(
                  'MM/DD/YYYY'
                )

                if (thisMonthDays < p1) {
                  p1 = thisMonthDays
                }

                if (payDate1 > endDate1) {
                  pay1Month--
                }

                payPeriodObj.payPeriod = `${payPeriodStart} - ${payPeriodEnd}`
                payPeriodObj.payDay = dayjs(new Date(thisYear, pay1Month + index, p1, 0, 0, 0, 0)).format('MM/DD/YYYY')
                // setNextPayPeriods([...nextPayPeriods, payPeriodObj])
                payPeriods.push(payPeriodObj)

                let payPeriodObj2: any = {}

                if (nextMonthDays < s2) {
                  s2 = nextMonthDays
                }

                start1Month++
                if (s2 < e1) {
                  end1Month++
                }

                let payPeriodStart2 = dayjs(new Date(thisYear, start1Month + index, s2, 0, 0, 0, 0)).format(
                  'MM/DD/YYYY'
                )
                if (nextMonthDays < e2) {
                  e2 = nextMonthDays
                }
                let payPeriodEnd2 = dayjs(new Date(thisYear, end1Month + index, e2, 23, 59, 59, 999)).format(
                  'MM/DD/YYYY'
                )
                if (nextMonthDays < p2) {
                  p2 = nextMonthDays
                }
                if (payDate2 < endDate2) {
                  pay1Month++
                }
                payPeriodObj2.payPeriod = `${payPeriodStart2} - ${payPeriodEnd2}`
                payPeriodObj2.payDay = dayjs(new Date(thisYear, pay1Month + index, p2, 0, 0, 0, 0)).format('MM/DD/YYYY')
                // setNextPayPeriods([...nextPayPeriods, payPeriodObj2])

                payPeriods.push(payPeriodObj2)
              })
          )
          setNextPayPeriods(payPeriods)
        }
      }
    } catch (error) {
      console.error('getNextPayPeriodsAndPayDays error', error)
    }
  }

  useEffect(() => {
    getNextPayPeriodsAndPayDays()
  }, [formValues])

  return (
    <ModalContainer>
      <ModalHeaderContainer>
        <FlexRow>
          <img src={UnitSvg} alt="modal icon" />
          <FlexCol>
            <ModalHeader>{inputData?.name ? 'Edit' : 'Add New'} Pay Schedule</ModalHeader>
          </FlexCol>
        </FlexRow>
        <CrossContainer
          onClick={() => {
            onClose()
          }}
        >
          <CrossIcon />
        </CrossContainer>
      </ModalHeaderContainer>
      <SettingModalContentContainer>
        <Formik
          initialValues={initialValues}
          onSubmit={handleSubmit}
          validationSchema={AddPayScheduleSchema}
          validateOnChange={true}
          validateOnBlur={false}
          enableReinitialize={true}
        >
          {({ values, errors, touched, setFieldValue, handleSubmit }: any) => {
            setFormValues(values)
            return (
              <Form className="form paySchedule">
                <InputWithValidation
                  labelName="Name"
                  stateName="name"
                  error={touched.name && errors.name ? true : false}
                />
                <Dropdown
                  value={values.period}
                  labelName="How often to pay"
                  stateName="period"
                  dropDownData={PERIOD}
                  setFieldValue={setFieldValue}
                  error={touched.period && errors.period ? true : false}
                />
                <SharedDate
                  value={values.periodEnd}
                  labelName="1st pay period ends on"
                  stateName="periodEnd"
                  error={touched.periodEnd && errors.periodEnd ? true : false}
                  setFieldValue={setFieldValue}
                />
                <SharedDate
                  value={values.payDay}
                  labelName="1st Payday is on"
                  stateName="payDay"
                  firstHalf={values.period === 'Twice per month' ? true : false}
                  error={touched.payDay && errors.payDay ? true : false}
                  setFieldValue={setFieldValue}
                />
                {values.period === 'Twice per month' && (
                  <>
                    <SharedDate
                      value={values.periodEnd2}
                      labelName="2nd pay period ends on"
                      stateName="periodEnd2"
                      setFieldValue={setFieldValue}
                    />
                    <SharedDate
                      value={values.payDay2}
                      labelName="2nd Payday is on"
                      stateName="payDay2"
                      min={values.payDay1}
                      secondHalf={values.period === 'Twice per month' ? true : false}
                      setFieldValue={setFieldValue}
                    />
                  </>
                )}
                {formValues.period && (
                  <AddPayScheduleTableContainer>
                    <TableDetail marginTop="30px">The next 4 pay periods (based on the dates you entered)</TableDetail>
                    <Table
                      columns={columns}
                      data={data}
                      loading={loading}
                      pageCount={pageCount}
                      fetchData={fetchData}
                      noPagination={true}
                      noSearch={true}
                      noLink={true}
                    />
                  </AddPayScheduleTableContainer>
                )}
                <ButtonContainer marginTop="20px">
                  <Button type="submit" onClick={handleSubmit}>
                    {loading ? (
                      <>
                        Adding..
                        <Loader />
                      </>
                    ) : (
                      'Add Pay Schedule'
                    )}
                  </Button>
                </ButtonContainer>
              </Form>
            )
          }}
        </Formik>
      </SettingModalContentContainer>
    </ModalContainer>
  )
}

export default PayScheduleModal
