import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate, useParams } from 'react-router-dom'

import { getMemberByUserId } from '../../logic/apis/company'
import { getPaySchedule } from '../../logic/apis/paySchedule'
import { PERIOD_OBJ1, StorageKey } from '../../shared/helpers/constants'
import { getDataFromLocalStorage, isSuccess, notify } from '../../shared/helpers/util'
import { Table } from '../../shared/table/Table'
import * as SharedStyled from '../../styles/styled'
import { colors } from '../../styles/theme'
import { NoPaySchedule } from './components/noPaySchedule/NoPaySchedule'
import * as Styled from './style'
import { ButtonCont, SettingsCont } from '../units/style'
import Button from '../../shared/components/button/Button'
import TabBar from '../../shared/components/tabBar/TabBar'
import PayScheduleModal from './components/PayScheduleModal'
import { CustomModal } from '../../shared/customModal/CustomModal'
import { addPaySchedulePath } from '../../logic/paths'

const PaySchedule = () => {
  const [showPayScheduleModal, setShowPayScheduleModal] = useState(false)

  const [loading, setLoading] = useState<boolean>(false)
  const [shimmerLoading, setShimmerLoading] = useState<boolean>(true)
  const [noData, setNoData] = useState<boolean>(false)
  const [payScheduleData, setPayScheduleData] = useState<any>([])
  // const [pageCount, setPageCount] = useState<number>(10)
  const [data, setData] = useState<any>([])
  const fetchIdRef = useRef(0)
  const [selectedRow, setSelectedRow] = useState({})

  const navigate = useNavigate()

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany } = globalSelector.company

  const fetchData = useCallback(
    async ({ pageSize, pageIndex }: any) => {
      try {
        // This will get called when the table needs new data

        let receivedData: any = []

        if (payScheduleData.length > 0) {
          payScheduleData.forEach((pay: any) => {
            receivedData.push({
              name: pay.name,
              howOftenToPay: PERIOD_OBJ1[pay.period],
              payScheduleId: pay._id,
            })
          })
        }

        // Give this fetch an ID
        const fetchId = ++fetchIdRef.current

        // Set the loading state

        // We'll even set a delay to simulate a server here
        // setTimeout(() => {
        // Only update the data if this is the latest fetch
        if (fetchId === fetchIdRef.current) {
          const startRow = pageSize * pageIndex
          const endRow = startRow + pageSize
          setData(receivedData.slice(startRow, endRow))

          // Your server could send back total page count.
          // For now we'll just fake it, too
          // setPageCount(Math.ceil(receivedData.length / pageSize))
        }
        // }, 1000)
      } catch (error) {
        console.error('PaySchedule fetchData error', error)
      } finally {
        setLoading(false)
      }
    },
    [payScheduleData]
  )

  const columns: any = useMemo(
    () => [
      {
        Header: 'Name',
        accessor: 'name', // accessor is the "key" in the data
      },
      {
        Header: 'How often to pay',
        accessor: 'howOftenToPay',
      },
    ],
    []
  )

  const getPayScheduleDetails = async () => {
    setLoading(true)
    try {
      // if (Object.keys(currentCompany).length > 0) {
      const response = await getPaySchedule({ deleted: false })
      if (isSuccess(response)) {
        let payScheduleData1 = response?.data?.data?.paySchedule
        if (payScheduleData1.length > 0) {
          setNoData(false)
          setPayScheduleData(payScheduleData1)
        } else {
          setNoData(true)
        }
      } else {
        notify(response?.data?.message, 'error')
      }
      // }
    } catch (error) {
      console.error('getPayScheduleDetails error', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    getPayScheduleDetails()
  }, [])

  return (
    <>
      <>
        {!noData ? (
          <>
            <SettingsCont gap="24px" className="half">
              <SharedStyled.FlexRow justifyContent="space-between" flexWrap="wrap">
                <SharedStyled.SectionTitle>Pay Schedule</SharedStyled.SectionTitle>
                <ButtonCont>
                  <Button
                    onClick={() => {
                      navigate(addPaySchedulePath)
                      // setShowPayScheduleModal(true)
                    }}
                  >
                    Add Pay Schedule
                  </Button>
                </ButtonCont>
              </SharedStyled.FlexRow>

              <SharedStyled.FlexRow alignItems="flex-start">
                <SharedStyled.FlexCol gap="24px">
                  <TabBar
                    tabs={[
                      {
                        title: 'Active',
                        render: () => (
                          <Table
                            columns={columns}
                            data={data}
                            loading={loading}
                            // pageCount={pageCount}
                            fetchData={fetchData}
                            paySchedule={true}
                            noSearch
                            noPagination
                            // onRowClick={(d) => {
                            //   setSelectedRow(d)
                            //   setShowPayScheduleModal(true)
                            // }}
                          />
                        ),
                      },
                      // {
                      //   title: 'Inactive',
                      //   render: () => <DeletePaySchedule />,
                      // },
                    ]}
                    filterComponent={<></>}
                  />
                </SharedStyled.FlexCol>
              </SharedStyled.FlexRow>

              {/* <CustomModal show={showPayScheduleModal}>
                <PayScheduleModal
                  inputData={selectedRow}
                  onClose={() => {
                    setShowPayScheduleModal(false)
                  }}
                  onComplete={() => {
                    setShowPayScheduleModal(false)
                    getPayScheduleDetails()
                  }}
                />
              </CustomModal> */}
            </SettingsCont>
          </>
        ) : (
          <NoPaySchedule />
        )}
      </>
    </>
  )
}

export default PaySchedule
