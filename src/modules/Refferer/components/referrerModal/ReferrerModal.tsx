import { Formik } from 'formik'
import React, { useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useParams } from 'react-router-dom'

import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import CloseSvg from '../../../../assets/newIcons/closeIcon.svg'
import UnitSvg from '../../../../assets/newIcons/unitModal.svg'
import { createUnit, deleteUnit, updateUnit } from '../../../../logic/apis/projects'
import { getDataFromLocalStorage, isSuccess, notify } from '../../../../shared/helpers/util'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import * as SharedStyled from '../../../../styles/styled'
import { IFullUnit } from '../../Refferer'
import * as Styled from './style'
import Button from '../../../../shared/components/button/Button'
import { createReferrer, deleteReferrer } from '../../../../logic/apis/company'
import { StorageKey } from '../../../../shared/helpers/constants'
import { createContact } from '../../../../logic/apis/contact'
import { Types } from '../../../contact/constant'

interface IUnit {
  name: string
}

interface INewUnitsModal {
  onClose: () => void
  isEditing?: boolean
  inputData?: Partial<IFullUnit> | null
  onComplete: ({ id, name }: { id?: string; name?: string }) => void
}

const ReferrerModal: React.FC<INewUnitsModal> = (props) => {
  const { onClose, inputData, isEditing, onComplete } = props
  const [loading, setLoading] = useState(false)
  const initValues = {
    name: inputData ? inputData.name : '',
  }

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company
  const inputRef = useRef<HTMLInputElement>(null)

  const onSubmit = async (val: typeof initValues) => {
    try {
      setLoading(true)
      const fullName = val.name.trim()
      const [firstName, lastName] = fullName.split(' ')
      const res =
        // inputData
        //   ? await updateUnit({ ...inputData, unitId: inputData._id, name: val.name!, symbol: val.symbol! })
        //   :
        // await createReferrer({ name: val.name })
        await createContact({
          firstName: firstName,
          fullName: fullName,
          lastName: lastName ?? undefined,
          type: Types.Referrer,
          createdBy: currentMember._id,
        })

      if (isSuccess(res)) {
        notify(`${!isEditing ? 'Created new' : 'Updated '} referrer!`, 'success')
        onComplete({ id: res?.data?.data?.id, name: fullName })
        onClose()
      } else throw new Error(res?.data?.message)
    } catch (err: any) {
      notify(err?.message ?? 'Failed to create new referrer!', 'error')
    } finally {
      setLoading(false)
    }
  }

  const onDelete = async () => {
    try {
      const res = await deleteReferrer({
        id: inputData?._id ?? '',
      })
      if (isSuccess(res)) {
        notify(`Deleted referrer!`, 'success')
        onComplete()
        onClose()
      } else throw new Error(res?.data?.message)
    } catch (err: any) {
      notify(err?.message ?? 'Failed to delete referrer!', 'error')
    }
  }

  return (
    <Styled.SettingModalContainer>
      <Styled.SettingModalHeaderContainer>
        <SharedStyled.FlexRow>
          <img src={UnitSvg} alt="modal icon" />
          <SharedStyled.FlexCol>
            <Styled.ModalHeader>{isEditing ? 'Edit' : 'Add'} Referrer</Styled.ModalHeader>
          </SharedStyled.FlexCol>
        </SharedStyled.FlexRow>
        <Styled.CrossContainer
          onClick={() => {
            onClose()
          }}
        >
          <img src={CloseSvg} alt="close icon" />
        </Styled.CrossContainer>
      </Styled.SettingModalHeaderContainer>
      <SharedStyled.SettingModalContentContainer>
        <Formik
          initialValues={initValues}
          enableReinitialize={true}
          onSubmit={onSubmit}
          validateOnChange={true}
          validateOnBlur={false}
        >
          {({ values, errors, touched, resetForm, setFieldValue, handleSubmit }) => {
            return (
              <SharedStyled.FlexBox width="100%" flexDirection="column" gap="10px">
                <InputWithValidation
                  labelName="Name"
                  stateName="name"
                  value={values.name}
                  error={touched.name && errors.name ? true : false}
                  passRef={inputRef}
                />

                <SharedStyled.FlexBox gap="12px" marginTop="26px">
                  {!isEditing && (
                    <Button
                      type="button"
                      isLoading={loading}
                      onClick={() => handleSubmit()}
                      disabled={!values.name || values.name.length < 2}
                    >
                      Save Referrer
                    </Button>
                  )}
                  {inputData ? (
                    <Button className="delete" type="button" onClick={() => onDelete()}>
                      Delete
                    </Button>
                  ) : null}
                </SharedStyled.FlexBox>
              </SharedStyled.FlexBox>
            )
          }}
        </Formik>
      </SharedStyled.SettingModalContentContainer>
    </Styled.SettingModalContainer>
  )
}

export default ReferrerModal
