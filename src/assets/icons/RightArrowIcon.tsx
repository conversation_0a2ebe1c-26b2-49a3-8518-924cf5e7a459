import React from 'react'

interface RightArrowIconProps {
  size?: number | string
  color?: string
  rotate?: number // degrees, e.g. 0, 90, 180, 270
  className?: string
  style?: React.CSSProperties
}

// Wrapper around newIcons/right-arrow.svg with rotation support via props
const RightArrowIcon: React.FC<RightArrowIconProps> = ({
  size = 20,
  color = 'currentColor',
  rotate = 0,
  className,
  style,
}) => {
  const px = typeof size === 'number' ? `${size}px` : size
  return (
    <span
      className={className}
      style={{ display: 'inline-flex', transform: `rotate(${rotate}deg)`, lineHeight: 0, ...style }}
      aria-hidden="true"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        width={px}
        height={px}
        fill={color}
      >
        <path d="M5.536 21.886a1.004 1.004 0 0 0 1.033-.064l13-9a1 1 0 0 0 0-1.644l-13-9A1 1 0 0 0 5 3v18a1 1 0 0 0 .536.886z" />
      </svg>
    </span>
  )
}

export default RightArrowIcon

