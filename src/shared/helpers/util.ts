import * as queryString from 'querystring'
import { toast } from 'react-toastify'
import { ARROWMARK, MONTHS, StageGroupEnum, TimeCardStatusEnum, WEEK_DAYS } from './constants'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/en'
import { I_Opportunity } from '../../modules/opportunity/Opportunity'
import { getMemberByUserId } from '../../logic/apis/company'
import { onlyTwoNumbersAfterDecimal } from './regex'
import duration from 'dayjs/plugin/duration'
import utc from 'dayjs/plugin/utc'
dayjs.extend(utc)
dayjs.extend(duration)

export type Uuid = string
export type Hash = string
export type Money = number
export type UrlPath<T> = string & { _?: T } // Basically a string.  The second clause is to peg the generic type
export type StaticUrlPath = UrlPath<{}>
export type Image = any
export type StringMap = { [key: string]: string }

export enum AgentType {
  individual = 1,
  entity = 2,
}

export function roundFloat(value: number | undefined): number {
  return value ? parseFloat(value.toFixed(2)) : 0
}

export function toCommaFloat(value: number | undefined): string {
  if (value) {
    const rounded = parseFloat(value.toFixed(2))
    return toCommaNumber(rounded)
  } else {
    return '0'
  }
}

export function getQRUrl(secret: string, email: string): string {
  return `otpauth://totp/EQUA:${email}?secret=${secret}&issuer=EQUA`
}

export const getFirstTwoLetters = (name: string): string => {
  if (!name) return 'Me'
  return name
    .split(' ')
    .slice(0, 2)
    .map((word) => word[0])
    .join('')
}

export const toCommaNumber = (value: number | string) => {
  const parts = value.toString().split('.')
  const first = parts[0].replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
  return [first].concat(parts.slice(1)).join('.')
}

export function optionalCommaNumber(value?: number | string): string | undefined {
  return typeof value === 'number' || typeof value === 'string' ? toCommaNumber(value) : undefined
}

export function optionalCommaAmount(value?: number | string): string | undefined {
  if (typeof value === 'number') {
    const rounded = value.toFixed(2)
    return toCommaNumber(rounded)
  } else if (typeof value === 'string') return toCommaNumber(value)
  else return '0'
}

export function getShareClasses(): string[] {
  return [...Array(5).keys()].map((i) => String.fromCharCode(i + 65))
}

export function intStringToNumber(value: string | number): number {
  return typeof value === 'number' ? value : !!value ? parseInt(value.replace(/[$,]/g, '')) : 0
}

// TODO: Deprecated in favor of `floatStringToNumberOrUndefined`
export function floatStringToNumber(value: string | number): number | null {
  return typeof value === 'number' ? value : value ? parseFloat(value.replace(/[$,]/g, '')) : null
}

export function parseOptionalFloat(value: string | number): number | undefined {
  return typeof value === 'number' ? value : value ? parseFloat(value.replace(/[$,]/g, '')) : undefined
}

export function removeMask(value?: string): string | undefined {
  return value?.replace(/[$,]/g, '')
}

export function wrapFunction(callback: any, response?: () => any) {
  return () => {
    callback()
    if (response) return response
  }
}

export function formatBytes(bytes: number, decimals: number = 0) {
  if (bytes == 0) return '0 Bytes'
  const k = 1024
  const dm = decimals <= 0 ? 0 : decimals || 2
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
}

export const isStepComplete = (meta: any) => {
  return !meta.error || meta.error.length === 0 || false
}

export const currentStep = (steps: any, errors: any, meta?: any) => {
  if (Object.keys(errors).length === 0) return true
  if (meta && meta.invalid) return false
  for (const step of steps) {
    if (errors[step] && (!Array.isArray(errors[step]) || (Array.isArray(errors[step]) && errors[step].length > 0))) {
      return false
    }
  }
  return true
}

export const formatFieldPath =
  (formPath: string | undefined) =>
  (fieldName: string): string =>
    formPath ? `${formPath}.${fieldName}` : fieldName

export function formatDate(date: any) {
  const d = new Date(date)
  let month = '' + (d.getMonth() + 1)
  let day = '' + d.getDate()
  const year = d.getFullYear()

  if (month.length < 2) month = '0' + month
  if (day.length < 2) day = '0' + day

  return [month, day, year].join('-')
}

export const isCorrectDateFormat = (date: string): boolean => {
  if (
    date.match(
      /^((0?[13578]|10|12)(-|\/)(([1-9])|(0[1-9])|([12])([0-9]?)|(3[01]?))(-|\/)((19)([2-9])(\d{1})|(20)([01])(\d{1})|([8901])(\d{1}))|(0?[2469]|11)(-|\/)(([1-9])|(0[1-9])|([12])([0-9]?)|(3[0]?))(-|\/)((19)([2-9])(\d{1})|(20)([01])(\d{1})|([8901])(\d{1})))$/
    )
  ) {
    return true
  }

  return false
}

export function getQueryParams(): any {
  // queryString.parse is supposed to ignore question marks but in some tests question marks were appearing in the
  // parsed key
  return queryString.parse(location.search.replace('?', ''))
}

export function formatQueryString(query: any): string {
  return Object.entries(query)
    .map(([key, value]) => `${key}=${value}`)
    .join('&')
}

export function genericSum<T>(getter: (value: T) => number): (items: T[]) => number {
  // prettier-ignore
  return items => items
        .map(getter)
        .reduce((a, b) => a + b, 0)
}

export function safeDivide(a: number, b: number): number {
  return b === 0 ? 0 : a / b
}

export function percentage(value: number, total: number): number {
  return safeDivide(value * 100, total)
}

export function percentageString(value: number, total: number, decimalPlaces: number = 2): string {
  const result = percentage(value, total).toFixed(decimalPlaces)

  // Don't display as decimal if the decimal value is zero
  const truncated = result.replace(/\.0+$/, '')
  return truncated + '%'
}

export function filterBy<Data>(filterObject: Data[], filterQuery: { [key: string]: any }): Data[] {
  return filterObject.reduce((aggregateArray: Data[], option: any) => {
    const filtered = Object.keys(filterQuery).every((key: string) =>
      filterQuery[key] ? option[key] === filterQuery[key] : true
    )
    if (filtered) {
      aggregateArray.push(option)
    }
    return aggregateArray
  }, [])
}

export function usdString(value: number, decimalPlaces: number = 4): string {
  const isNegative = value < 0
  const negativePrefix = isNegative ? '- ' : ''
  const result = negativePrefix + '$' + toCommaNumber(Math.abs(value).toFixed(decimalPlaces))
  return decimalPlaces === 2 ? result : result.replace(/\.?0?0?$/, '')
}

export function dollarString(value: number): string {
  const isNegative = value < 0
  const negativePrefix = isNegative ? '- ' : ''
  return negativePrefix + '$' + Math.abs(value).toFixed(2)
}

export function dollarStringFromCents(value: number): string {
  return dollarString(value / 100)
}

export function normalizeDate(value: string | Date | number | undefined | null): Date | undefined {
  if (!value) return undefined
  return typeof value == 'string' ? new Date(value.split('T')[0].replace(/-/g, '/')) : new Date(value)
}

export const defaultPageSize = 25

export const getFieldValues = (fields: string[]) => (object: any) => {
  let result: string[] = []
  for (const field of fields) {
    const path = field.split('.')
    let value = object
    for (const token of path) {
      value = value[token]
      if (!value) break
    }
    result = result.concat(value)
  }
  return result
}

export function resolveObjectByString(path: any, obj = self, separator = '.') {
  const properties = Array.isArray(path) ? path : path.split(separator)
  return properties.reduce((prev: any, curr: any) => prev && prev[curr], obj)
}

export type Transform<T> = (input: T) => T

export function currencyStringToNumber(currencyString?: string | number) {
  if (typeof currencyString === 'number') return currencyString
  return currencyString ? Number(currencyString.replace(/[^0-9\.-]+/g, '')) : null
}

export function getPathArray(pathname: string) {
  return pathname.split('/').filter((i) => i)
}

export const getViewAddress = (addresses: any, type: any) => {
  const current = addresses?.filter((add: any) => add.groups.some((s: any) => s.id === type))[0]
  const withType = addresses?.filter((add: any) => add.groups.length > 0)[0]
  const hasAddress = addresses?.length > 0
  if (current) return current
  else if (withType) return withType
  else if (hasAddress) return addresses[0]
  else return undefined
}

export function daysSince(first: number, second: number = Date.now()): number {
  const oneDay = 24 * 60 * 60 * 1000 // hours * minutes * seconds * milliseconds
  return Math.round(Math.abs((first - second) / oneDay))
}

export let idCounter = 1

export function nextInternalId(): number {
  return idCounter++
}

export let userStateCount = 1

export function nextStateCount(): number {
  return userStateCount++
}

export function resetStateCount(): number {
  return (userStateCount = 1)
}

export interface ChildProps {
  children?: React.ReactNode
}

export function formatImageUrl(image: Hash): string {
  return `/api/v1/file/${image}/content`
}

export function yearsRange(size: number, startAt: number) {
  return [...Array(size).keys()].map((i) => {
    return { value: i + startAt, label: i > 0 ? `${i + startAt} Years` : `${i + startAt} Year` }
  })
}

export function getQueryParamFloat(queryParams: any, key: string): number | undefined {
  const value = queryParams[key]
  return value ? parseFloat(value) : undefined
}

export function getQueryParamInt(queryParams: any, key: string): number | undefined {
  const value = queryParams[key]
  return value ? parseInt(value) : undefined
}

export function matchesPath(path: string, value: string, exact: boolean): boolean {
  const generalized = path.replace(/:\w+/g, '[^/]*?')
  const pattern = `^${generalized}${exact ? '$' : ''}`
  const regex = new RegExp(pattern)
  return !!value.match(regex)
}

export const notify = (msg: string, type: string) => {
  if (type === 'info') {
    return toast.info(msg, {
      position: 'top-right',
      autoClose: 5000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
      theme: 'light',
    })
  } else if (type === 'success') {
    return toast.success(msg, {
      position: 'top-right',
      autoClose: 5000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
      theme: 'light',
    })
  } else if (type === 'warning') {
    return toast.warn(msg, {
      position: 'top-right',
      autoClose: 5000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
      theme: 'light',
    })
  } else if (type === 'error') {
    if (
      msg?.toLowerCase()?.trim() === 'forbidden resource' ||
      msg?.toLowerCase()?.trim() === 'insufficient permissions'
    ) {
      return
    }
    return toast.error(msg, {
      position: 'top-right',
      autoClose: 5000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
      theme: 'light',
    })
  }
}

export const getStatus = (statusNumber: number) => {
  if (statusNumber === 1) {
    return 'Pending'
  } else if (statusNumber === 2) {
    return 'Accepted'
  } else if (statusNumber === 3) {
    return 'Rejected'
  } else if (statusNumber === 4) {
    return 'Revoked'
  } else if (statusNumber === 5) {
    return 'Resent'
  }
}

export const daysInMonth = (y: any, m: any) => {
  return new Date(y, m + 1, 0).getDate()
}

const setMinAndReturnMinDate = (minDate: string) => {
  let min = dayjs(minDate).format('YYYY-MM-DD')
  return min
}

export const getFormattedDate = (value: string, format?: string): any => {
  try {
    let dateValue = new Date(value)
    let date = dateValue.getUTCDate()
    let month = dateValue.getUTCMonth() + 1
    let year = dateValue.getUTCFullYear()
    // let date = dayjs(new Date(value)).format('MM-DD-YYYY')

    if (format === 'YYYY-MM-DD') {
      let monthString = ''
      if (month.toString().length === 1) {
        monthString = '0' + month
      }
      return `${year}-${monthString}-${date}`
    } else {
      return new Date(`${month}-${date}-${year}`)
    }
  } catch (error: any) {
    console.error('getFormattedDate error', error)
    return error
  }
}

export const getFormattedDateForHistory = (value: string): string => {
  try {
    let dateValue = new Date(value)
    let date = dateValue.getUTCDate()
    let month = dateValue.getUTCMonth() + 1
    let year = dateValue.getUTCFullYear()
    // let date = dayjs(new Date(value)).format('MM-DD-YYYY')

    return `${month}/${date}/${year}`
  } catch (error: any) {
    console.error('getFormattedDate error', error)
    return error
  }
}

export const getFormattedDateForTimeCard = (value?: string): string => {
  try {
    let dateValue: Date
    if (value) {
      dateValue = new Date(value)
    } else {
      dateValue = new Date()
    }
    let day = WEEK_DAYS[dateValue.getUTCDay()]
    let date = dateValue.getUTCDate()
    let month = MONTHS[dateValue.getUTCMonth()]
    let year = dateValue.getUTCFullYear()
    return `${day}, ${month} ${date}, ${year}`
  } catch (error: any) {
    console.error('getFormattedDateForTimeCard error', error)
    return error
  }
}

export const getHoursAndMinutes = (value: string): string => {
  try {
    let dateValue: Date = new Date(value)

    if (isNaN(dateValue.getTime())) {
      // Invalid date
      return ''
    }

    let hour = dateValue.getHours().toString().padStart(2, '0')
    let minutes = dateValue.getMinutes().toString().padStart(2, '0')
    // let seconds = dateValue.getSeconds().toString().padStart(2, '0')

    // return `${hour}:${minutes}:${seconds}`
    return `${hour}:${minutes}`
  } catch (error: any) {
    console.error('getHoursAndMinutes error', error)
    return ''
  }
}

// export const getHoursAndMinutes = (value: string) => {
//   try {
//     let dateValue: Date

//     dateValue = new Date(value)

//     let hour = dateValue.getHours().toString()
//     let minutes = dateValue.getMinutes().toString()
//     if (minutes.toString().length === 1) {
//       minutes = '0' + minutes
//     }
//     if (hour.toString().length === 1) {
//       hour = '0' + hour
//     }

//     // let day = WEEK_DAYS[dateValue.getUTCDay()]
//     // let date = dateValue.getUTCDate()
//     // let month = MONTHS[dateValue.getUTCMonth()]
//     // let year = dateValue.getUTCFullYear()
//     // return `${day}:${month}`
//     return `${hour}:${minutes}`
//   } catch (error: any) {
//     console.error('getFormattedDateForTimeCard error', error)
//     return error
//   }
// }

export const checkMinCondition = (min: string, value: string) => {
  try {
    let minDateValue = new Date(min).getTime()
    let value1 = dayjs(new Date(value)).format('YYYY-MM-DD')
    let currentDateInputValue = new Date(value1).getTime()
    return currentDateInputValue <= minDateValue
  } catch (error) {
    console.error('checkMinCondition error', error)
  }
}

export const getDigitsFromPhone = (str: string) => {
  return str?.replace(/\D/g, '')
}

export const isValidPhoneNumber = (str: string) => {
  // Strip all non-numeric characters from the input
  const strippedInput = str.replace(/\D/g, '')
  return strippedInput.length <= 10
}

export const formatPhoneNumber = (input: string, prevInput: string) => {
  if (input?.length < prevInput?.length) return input
  // Strip all non-numeric characters from the input
  const strippedInput = input?.replace(/\D/g, '')

  // Use regex to split the input into groups of three digits and a group of up to four digits

  const match = strippedInput?.match(/^(\d{0,3})(\d{0,3})(\d{0,4})$/)

  // Reformat the input with parentheses around the first three digits and a hyphen between the second and third groups
  let formattedInput = ''
  if (match) {
    if (strippedInput.length >= 6) formattedInput = `(${match[1]}) ${match[2]}-${match[3]}`
    else if (strippedInput.length >= 3) formattedInput = `(${match[1]}) ${match[2]}${match[3]}`
    else formattedInput = `${match[1]}${match[2]}${match[3]}`
  } else {
    formattedInput = strippedInput
  }
  return formattedInput
}

export const formatDateymd = (time: number | string | Date) => {
  const currentDate = new Date(time)

  const year = currentDate.getFullYear()
  const month = ('0' + (currentDate.getMonth() + 1)).slice(-2)
  const day = ('0' + currentDate.getDate()).slice(-2)

  return `${year}-${month}-${day}`
}

export const isSuccess = (res: any) => {
  return (
    (res?.status >= 200 && res?.status < 300) ||
    (res?.statusCode >= 200 && res?.statusCode < 300) ||
    (res?.data?.statusCode >= 200 && res?.data?.statusCode < 300)
  )
}

// extracts specific keys from object[] into an string[] /{[key:string]:any}
export const getKeysFromObjects = <T extends Object>(obj: T[], key: keyof T) => {
  let keysArr = obj.reduce((prev: string[], cur: any) => {
    return [...prev, cur[key]]
  }, [])
  return keysArr
}

// export function convertStrToKey(inputString: string): string {
//   // split the string into an array of words
//   const words = inputString.split(/\s+/)

//   // convert the first word to lowercase
//   words[0] = words[0].toLowerCase()

//   // capitalize the first letter of each subsequent word
//   for (let i = 1; i < words.length; i++) {
//     words[i] = words[i].charAt(0).toUpperCase() + words[i].substring(1)
//   }

//   // join the words back together and return the result
//   return words.join('').toLowerCase()
// }

// export function convertKeyToStr(camelCaseString: string): string {
//   // split the camel case string into an array of words
//   const words = camelCaseString.replace(/([a-z])([A-Z])/g, '$1 $2').split(/\s+/)

//   // join the words back together with spaces and return the result
//   return words.join(' ')
// }

// export function convertStrToKey(str: string): string {
//   return str
//     .replace(/\s+/g, '-') // replace spaces with hyphens
//     .toLowerCase() // convert to lowercase
// }
export function convertStrToKey(str: string): string {
  return str
    ?.replace(/\s+/g, '-') // replace spaces with hyphens
    ?.replace(/\./g, '_') // replace periods with underscores
    ?.toLowerCase() // convert to lowercase
}
export function convertKeyToStr(key: string): string {
  return key
    ?.split('-')
    ?.map((word, index) => {
      return word?.charAt(0)?.toUpperCase() + word?.slice(1)
    })
    ?.join(' ')
}
export function isoToMMDD(dateStr: string): string {
  const date = new Date(dateStr)
  const month = (date.getUTCMonth() + 1).toString().padStart(2, '0')
  const day = date.getUTCDate().toString().padStart(2, '0')
  const year = date.getUTCFullYear()
  return `${month}/${day}/${year}`
}

export function isoToMMDDWithDate(date: Date): string {
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  const year = date.getFullYear()
  return `${month}/${day}/${year}`
}

export function deleteElementAtIndex<T>(arr: T[], index: number): T[] {
  if (arr.length === 1 && index === 0) {
    return []
  } else {
    const newArr = [...arr]
    newArr.splice(index, 1)
    return newArr
  }
}

export function convertTimeTo12HourFormat(timeString: string): string {
  const [hours, minutes] = timeString.split(':')
  let amOrPm = 'am'
  let formattedHours = parseInt(hours, 10)
  if (formattedHours >= 12) {
    amOrPm = 'pm'
    formattedHours -= 12
  }
  if (formattedHours === 0) {
    formattedHours = 12
  }
  return `${formattedHours.toString().padStart(2, '0')}:${minutes} ${amOrPm}`
}

export function combineDateTime(date: Date, time: Date): Date {
  const year = date.getFullYear()
  const month = date.getMonth()
  const day = date.getDate()
  const hours = time.getHours()
  const minutes = time.getMinutes()
  const seconds = time.getSeconds()
  const combinedDateTime = new Date(year, month, day, hours, minutes, seconds)
  return combinedDateTime
}

export function filterOpportunity(arr: I_Opportunity[], filterString: string): I_Opportunity[] {
  const filteredArray = arr.filter((obj) => {
    const keys = [
      'PO',
      'firstName',
      'lastName',
      'street',
      'city',
      'state',
      'zip',
      'client',
      'addrCityState',
      'salesPerson',
    ]
    for (const key of keys) {
      if (obj[key] && obj[key].toString().toLowerCase().startsWith(filterString.toLowerCase())) {
        return true
      }
    }
    return false
  })
  return filteredArray
}

export function parseTimeString(timeString: string): Date {
  const today = new Date()
  const [hours, minutes] = timeString.split(':').map((str) => parseInt(str))
  return new Date(today.getFullYear(), today.getMonth(), today.getDate(), hours, minutes)
}

interface Week {
  week: number
  startDate: string
  endDate: string
}

// VERSION 1
// export function getWeeksInRange(startDate: Date, endDate: Date): Week[] {
//   const weeks: Week[] = []
//   let weekNumber = 1
//   let currentDate = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate())

//   while (currentDate <= endDate) {
//     const startDateOfWeek = new Date(currentDate)
//     startDateOfWeek.setDate(startDateOfWeek.getDate() - startDateOfWeek.getDay())

//     const endDateOfWeek = new Date(startDateOfWeek)
//     endDateOfWeek.setDate(endDateOfWeek.getDate() + 6)

//     const formattedStartDate = `${
//       startDateOfWeek.getMonth() + 1
//     }/${startDateOfWeek.getDate()}/${startDateOfWeek.getFullYear()}`
//     const formattedEndDate = `${endDateOfWeek.getMonth() + 1}/${endDateOfWeek.getDate()}/${endDateOfWeek.getFullYear()}`

//     weeks.push({ week: weekNumber, startDate: formattedStartDate, endDate: formattedEndDate })

//     const nextWeekStartYear = startDateOfWeek.getFullYear()
//     const nextWeekStartDate = new Date(nextWeekStartYear, startDateOfWeek.getMonth(), startDateOfWeek.getDate() + 7)
//     if (nextWeekStartYear !== nextWeekStartDate.getFullYear()) {
//       weekNumber = 1
//     } else {
//       weekNumber++
//     }

//     currentDate.setDate(currentDate.getDate() + 7)
//   }

//   return weeks
// }

// VERSION 2
export function getWeeksInRange(startDate: Date, endDate: Date): Week[] {
  const weeks: Week[] = []
  let currentDate = new Date(startDate)

  while (currentDate <= endDate) {
    const startDateOfWeek = new Date(currentDate)
    const endDateOfWeek = new Date(currentDate)

    // Move to the beginning of the current week (Sunday)
    startDateOfWeek.setDate(startDateOfWeek.getDate() - startDateOfWeek.getDay())

    // Move to the end of the current week (Saturday)
    endDateOfWeek.setDate(startDateOfWeek.getDate() + 6)

    const isoWeekNumber = getISOWeek(endDateOfWeek)
    const formattedStartDate = formatDateNew(startDateOfWeek)
    const formattedEndDate = formatDateNew(endDateOfWeek)

    weeks.push({ week: isoWeekNumber, startDate: formattedStartDate, endDate: formattedEndDate })

    // Move to the start of the next week
    currentDate.setDate(currentDate.getDate() + 7)

    // Adjust the end date for the last week
    if (isoWeekNumber === getISOWeek(endDate) && currentDate > endDate) {
      endDateOfWeek.setDate(endDate.getDate())
    }
  }

  return weeks
}

function getISOWeek(date: Date): number {
  const d: any = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()))
  const dayNum = d.getUTCDay() || 7
  d.setUTCDate(d.getUTCDate() + 4 - dayNum)
  const yearStart: any = new Date(Date.UTC(d.getUTCFullYear(), 0, 1))
  return Math.ceil(((d - yearStart) / 86400000 + 1) / 7)
}

function formatDateNew(date: Date): string {
  return `${date.getMonth() + 1}/${date.getDate()}/${date.getFullYear()}`
}

//VERSION 3

export const formattedDateForRange = (isWeek: boolean) => {
  //find start and end report dates
  var startDate = getWeekStart(0, new Date())

  var endDate = new Date(startDate)
  endDate.setDate(endDate.getDate() + 7)
  endDate.setHours(0, 0, 0, -1)
  var weeksToGoBack = 30 //define how far to look back
  var weeks = []
  for (var i = 0; i < weeksToGoBack; i++) {
    const ds = new Date(startDate)
    const de = new Date(endDate)
    const start = dayjsFormat(new Date(ds.setDate(ds.getDate() - i * 7)), 'MM/DD/YYYY')
    const end = dayjsFormat(new Date(de.setDate(de.getDate() - i * 7)), 'MM/DD/YYYY')

    if (isWeek) {
      const week = getWeekNum(new Date(start))
      const formatedWeekDate = `W${week.num}: ${start} to ${end}`
      weeks.push(formatedWeekDate)
    } else {
      const formatedWeekDate = `${start} to ${end}`
      weeks.push(formatedWeekDate)
    }
  }

  return weeks
}

// export const reportPeriods = (startDate: any) => {
//   const periods = 4
//   const week = getWeekNum(new Date(startDate))
//   const array = []
//   for (let i = 0; i < periods; i++) {
//     const wkNum = week.num - i
//     array.push(`W${wkNum}`)
//   }
//   console.log({ array })
//   return array
// }

const getWeekNum = (date: any) => {
  date.setHours(0, 0, 0, 0)
  // Set date to wednesday of current week
  date.setDate(date.getDate() + 3 - date.getDay())
  // Find first week - January 4 is always in week 1.
  let week1 = new Date(date.getFullYear(), 0, 4)
  // Set date to wednesday of first week
  week1.setDate(week1.getDate() + 3 - week1.getDay())
  // Count # of Wednesdays in between to get week number
  const num = 1 + Math.round((date.getTime() - week1.getTime()) / 604800000)
  const year = date.getFullYear()
  return { num, year }
}
const getWeekStart = (weekStartDay: any, date: any) => {
  const d = new Date(date)
  d.setHours(0, 0, 0, 0) // set time to start of day
  const dayOfWeek = d.getDay() // get day of week
  const start = new Date(d)
  if (dayOfWeek !== weekStartDay) {
    // if today is not week start
    var diff = dayOfWeek - weekStartDay // find difference between today and weekstart
    if (diff < 0) {
      // if difference is negative, add 7 to it to find how many days away
      diff = diff + 7
    }
    start.setDate(start.getDate() - diff) // set date to start of week
  }
  return start
}

// expected input format "W1: 1/1/23 to 1/2/23"
export function extractDatesFromString(str: string): [Date, Date] {
  // Extract the start and end dates from the string using a regular expression
  const dateRegExp = /(\d{1,2}\/\d{1,2}\/\d{4})/g
  const [startDateStr, endDateStr] = str.match(dateRegExp) as string[]

  // Parse the date strings into Date objects
  const startDate = new Date(startDateStr)
  const endDate = new Date(endDateStr)

  // Return the dates as a tuple
  return [startDate, endDate]
}

// export function getCurrentWeekNumberAndYear(date?: string): string {
//   const now = date ? new Date(date) : new Date()
//   const onejan = new Date(now.getFullYear(), 0, 1)
//   const weekNumber = Math.ceil(((now.getTime() - onejan.getTime()) / 86400000 + onejan.getDay() + 1) / 7)
//   const year = now.getFullYear()
//   return `W${weekNumber.toString().padStart(2, '0')} - ${year.toString()}`
// }

export function getCurrentWeekNumberAndYear(startDate?: string, endDate?: string): string {
  const now: Date = new Date()
  const currentYear: number = now.getFullYear()

  if (!startDate || !endDate) {
    const onejan: Date = new Date(currentYear, 0, 1)
    const dayOfYear: number = Math.floor((now.getTime() - onejan.getTime()) / 86400000)
    const weekNumber: number = Math.floor((dayOfYear + onejan.getDay() + 6) / 7)
    return `W${weekNumber.toString().padStart(2, '0')}-${currentYear.toString()}`
  }

  const end: Date = new Date(endDate)

  // Check if the end date is valid
  if (isNaN(end.getTime())) {
    return 'Invalid end date format. Please provide the end date in the format "MM/DD/YYYY"'
  }

  // Calculate the day difference between the end date and the start of the year
  const onejan: Date = new Date(currentYear, 0, 1)
  const dayOfYearEnd: number = Math.floor((end.getTime() - onejan.getTime()) / 86400000)

  // Calculate the week number using ISO 8601 standard for the end date
  const endWeekNumber: number = Math.floor((dayOfYearEnd + onejan.getDay() + 6) / 7)

  return `W${endWeekNumber.toString().padStart(2, '0')}-${currentYear.toString()}`
}

export const joinString = (str: string) => {
  return str.split(' ').join('')
}

// export function getWeeksFromDate(date: Date, n: number): string[] {
//   let weeks: string[] = []

//   // Calculate the week number of the given date
//   let weekNum = 'W' + getWeekNumber(date)

//   // Loop n times and subtract 1 from week number each time
//   for (let i = 0; i < n; i++) {
//     weeks.push(weekNum)

//     // Subtract 1 from week number
//     let prevWeekNum = subtractWeekNumber(weekNum)

//     // If week number goes past 0, calculate based on previous year's last week number
//     if (prevWeekNum === 'W0') {
//       let prevYear = date.getFullYear() - 1
//       let lastWeekNum = getLastWeekNumber(prevYear)
//       prevWeekNum = 'W' + lastWeekNum
//     }

//     weekNum = prevWeekNum
//   }

//   return weeks
// }

export function getWeeksFromDate(date: Date, n: number): string[] {
  const weeks: string[] = []

  // Calculate the initial week number and date
  let currentDate = new Date(date)

  for (let i = 0; i < n; i++) {
    // Get the week number and the week ending date (last day of the week)
    const weekNumber = getWeekNumber(currentDate)
    const weekEndingDate = getWeekEndingDate(currentDate)

    // Format the week number and ending date as "W45: 11/9"
    weeks.push(`W${weekNumber}: ${dayjsFormat(weekEndingDate, 'M/D')}`)

    // Move to the previous week
    currentDate = new Date(currentDate)
    currentDate.setDate(currentDate.getDate() - 7) // Subtract 7 days
  }

  return weeks
}

// Helper function to get the week ending date (Saturday)
function getWeekEndingDate(date: Date): Date {
  const day = date.getDay() // Get the day of the week (0 = Sunday, 6 = Saturday)
  const diff = 6 - day // Calculate days to reach Saturday
  const weekEndingDate = new Date(date)
  weekEndingDate.setDate(date.getDate() + diff)

  return weekEndingDate
}

// Helper function to get week number from date
export function getWeekNumber(date: Date): number {
  let d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()))
  d.setUTCDate(d.getUTCDate() + 4 - (d.getUTCDay() || 7))
  let yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1))
  let weekNo = Math.ceil(((d.getTime() - yearStart.getTime()) / 86400000 + 1) / 7)
  return weekNo
}

// Helper function to subtract 1 from week number
export function subtractWeekNumber(weekNum: string): string {
  let num = parseInt(weekNum.substr(1))
  return 'W' + (num - 1)
}

// Helper function to get last week number of a year
export function getLastWeekNumber(year: number): number {
  let d = new Date(year, 11, 31)
  let weekNum = getWeekNumber(d)
  if (weekNum === 1) {
    // If last day of year is in first week of next year, subtract 1 from week number
    weekNum = 52
  }
  return weekNum
}

export const isValidDate = (dateString: string) => {
  const date = new Date(dateString)
  return !isNaN(date.getTime())
}

export const getNameFromId = (id: string, options: any) => {
  const selectedOption = options?.find((option: any) => option?.id === id)
  return selectedOption ? selectedOption.name : ''
}

export const getNameFrom_Id = (id: string, options: any) => {
  const selectedOption = options.find((option: any) => option._id === id)
  return selectedOption ? selectedOption.name : ''
}

export const getIdFromName = (name: string, options: any[]) => {
  const selectedOption = options?.find((option: any) => option.name === name)
  return selectedOption ? selectedOption?._id || selectedOption?.id || '' : ''
}

export const getSymbolFromId = (id: string, options: any) => {
  const selectedOption = options.find((option: any) => option._id === id)
  return selectedOption ? selectedOption.symbol : ''
}
export const getActivityTypeByStepName = (name: string, options: any) => {
  const selectedOption = options.find((option: any) => option.name === name)
  return selectedOption ? selectedOption.activityType : ''
}
export const getStageNameFromId = (id: string, options: any) => {
  const selectedOption = options?.find((option: any) => option._id === id)
  return selectedOption ? selectedOption.name : ''
}

export const getStageIdFromName = (name: string, options: any) => {
  const selectedOption = options.find((option: any) => option.name === name)
  return selectedOption ? selectedOption._id : ''
}

export const getBooleanFromName = (name: string, options: any) => {
  const selectedOption = options.find((option: any) => option.name.trim() === name)
  return selectedOption ? selectedOption.isBusiness : null
}

export const getMemberId = async (setMemberId: React.Dispatch<React.SetStateAction<string>>) => {
  try {
    const res = await getMemberByUserId()
    if (isSuccess(res)) {
      const { member } = res?.data?.data
      setMemberId(member?._id)
    }
  } catch (error) {
    console.error(error, 'Member ID Error')
  }
}

export const getUnitSymbolFromId = (id: string, options: any) => {
  const selectedOption = options?.find((option: any) => option?._id === id)
  return selectedOption ? `${selectedOption.symbol} (${selectedOption.name})` : ''
}

export const getDecFromId = (id: string, options: any) => {
  const selectedOption = options.find((option: any) => option._id === id)
  return selectedOption ? selectedOption.desc : ''
}

export const getStageIdFromId = (id: string, options: any) => {
  const selectedOption = options.find((option: any) => option._id === id)
  return selectedOption ? `${selectedOption.stage}` : ''
}

export const getPercentCompleteFromId = (id: string, options: any) => {
  const selectedOption = options.find((option: any) => option._id === id)
  return selectedOption ? selectedOption.percent_done : 0
}

export const getUnitIdFromSymbol = (symbol: string, options: any) => {
  const selectedOption = options.find((option: any) => option.symbol === symbol)
  return selectedOption ? selectedOption._id : ''
}

interface Item {
  pitch?: number
  layers?: number
  amount?: number
  id?: string
  name: string
  unit?: string
}

interface ItemWithStatus extends Item {
  isRemoved?: boolean
}

function getUniqueAndRemovedItems(a: Item[], b: Item[]): Partial<ItemWithStatus>[] {
  // Create a map from array `a` for quick lookup by `id` or `name`
  const aMap = new Map<string, Item>(a.map((item) => [item.id ?? item.name, item]))
  // Create a map from array `b` for quick lookup by `id` or `name`
  const bMap = new Map<string, Item>(b.map((item) => [item.id ?? item.name, item]))

  const result: Partial<ItemWithStatus>[] = []

  // Check for unique or updated items in `b`
  for (const itemB of b) {
    const key = itemB.id ?? itemB.name
    const itemA = aMap.get(key)

    if (!itemA) {
      // Include the full item if it's not in `a` (new item)
      result.push(itemB)
    } else {
      // Check for differences if it exists in both
      const differences: Partial<ItemWithStatus> = { name: itemB.name }

      let hasDifference = false
      for (const prop in itemB) {
        if (prop !== 'id' && itemB[prop as keyof Item] !== itemA[prop as keyof Item]) {
          // @ts-ignore
          differences[prop as keyof Item] = `${itemA[prop as keyof Item]} -> ${itemB[prop as keyof Item]}`
          hasDifference = true
        }
      }

      // Include only if there are differences
      if (hasDifference) {
        result.push(differences)
      }
    }
  }

  // Check for removed items in `a`
  for (const itemA of a) {
    const key = itemA.id ?? itemA.name
    if (!bMap.has(key)) {
      // Include the full item with `isRemoved: true` if it's not in `b`
      result.push({
        name: itemA?.name,
        amount: itemA?.amount,
        pitch: itemA?.pitch,
        layers: itemA?.layers,
        isRemoved: true,
      })
    }
  }

  return result
}

export const getChangedValues = (values: any, initialValues: any) => {
  return Object.entries(values).reduce((acc: Record<string, unknown>, [key, value]) => {
    const hasChanged = initialValues[key] !== value

    if (hasChanged) {
      if (key === 'pieceWork') {
        acc[key] = getUniqueAndRemovedItems(initialValues?.pieceWork, values?.pieceWork)
      } else if (key === 'extraTime') {
        acc[key] = getUniqueAndRemovedItems(initialValues?.extraTime, values?.extraTime)
      } else {
        acc[key] = typeof initialValues[key] === 'string' ? `${initialValues[key]} ${ARROWMARK} ${value}` : value
      }
    }

    return acc
  }, {})
}

export const camelCaseToSentenceCase = (input: string) => {
  if (!input) return input

  // Insert a space before each uppercase letter and convert the string to lowercase
  const sentence = input
    .replace(/([A-Z])/g, ' $1') // Add space before uppercase letters
    .toLowerCase() // Convert to lowercase

  // Capitalize the first letter
  return sentence.charAt(0).toUpperCase() + sentence.slice(1)
}

export const getSalesPersonIdFromName = (name: string, options: any) => {
  const selectedOption = options.find((option: any) => option.name === name)
  return selectedOption ? selectedOption._id : ''
}

export const getSalesPersonNameFromId = (id: string, options: any) => {
  const selectedOption = options?.find((option: any) => option._id === id)
  return selectedOption ? selectedOption.name : ''
}

export const getParentIdFromName = (name: string, options: any) => {
  const selectedOption = options.find((option: any) => option.name === name)
  return selectedOption ? selectedOption._id : ''
}

export const getSubcontractorIdFromName = (name: string, options: any) => {
  const selectedOption = options.find((option: any) => option.name === name)
  return selectedOption ? selectedOption._id : ''
}
export const getSubcontractorNameFromId = (id: string, options: any) => {
  const selectedOption = options.find((option: any) => option._id === id)
  return selectedOption ? selectedOption.name : ''
}

export const getPoIdFromName = (name: string, options: any) => {
  const selectedOption = options?.find((option: any) => option.po === name)
  return selectedOption ? selectedOption.oppId : ''
}

export const getParentNameFromId = (id: string, options: any) => {
  if (!id) {
    return ''
  }

  const selectedOption = options.find((option: any) => option._id === id)
  return selectedOption ? selectedOption.name : ''
}

export const getTaxJurisdictionNameFromId = (id: string, options: any) => {
  const selectedOption = options.find((option: any) => option._id === id)
  return selectedOption ? selectedOption.identifier : ''
}

export const getTaxJurisdictionIdFromName = (name: string, options: any) => {
  const selectedOption = options?.find((option: any) => option.identifier === name)
  return selectedOption ? selectedOption._id : ''
}

export const getValueByKeyAndMatch = (key: string, value: any, matchKey: string, options: any[]) => {
  const selectedOption = options?.find((option: any) => option[matchKey] === value)
  return selectedOption ? selectedOption[key] || '' : ''
}

export const findObjectKeyValue = (obj: any, key: string) => {
  if (obj.hasOwnProperty(key)) {
    return obj[key]
  } else {
    return ''
  }
}

export const createCustomDate = (date: any, time: any) => {
  const baseDateObj = new Date(date)

  // Parse the user input time
  const [hours, minutes] = time.split(':')

  // Create a new date with the same year, month, and day as the base date
  const customDate = new Date(baseDateObj.getFullYear(), baseDateObj.getMonth(), baseDateObj.getDate(), hours, minutes)

  return customDate
}
export const getEditDate = (date: any) => {
  // Append the desired time to the date string
  const customDateString = date + 'T10:36:00.000Z'

  return customDateString
}

export const getTaxJurisdictionRateFromId = (id: string, options: any) => {
  const selectedOption = options.find((option: any) => option._id === id)
  return selectedOption ? selectedOption.rate : ''
}

export enum PeriodEnum {
  EveryWeek = 1,
  EveryOtherWeek = 2,
  TwicePerMonth = 3,
  OncePerMonth = 4,
}

export const getPayPeriods = (period?: any, end1?: any, payday1?: any, end2?: any, payday2?: any) => {
  var today: any = new Date()
  var payPeriods: any = []
  // set end date to isodate and end of local day
  var end1: any = new Date(end1 + 'T23:59:59.999')
  if (period === PeriodEnum.EveryWeek || period === PeriodEnum.EveryOtherWeek) {
    if (period == PeriodEnum.EveryWeek) {
      var increment: any = 7
    } else if (period == PeriodEnum.EveryOtherWeek) {
      var increment: any = 14
    }
    var start1 = new Date(end1)
    start1.setDate(start1.getDate() - (increment - 1))
    start1.setHours(0, 0, 0, 0)
    var reportEnd = 0
    var i = 0
    while (reportEnd < today) {
      var startDate: any = new Date(start1)
      var endDate: any = new Date(end1)
      var payDate: any = new Date(payday1 + 'T00:00')
      var obj: any = {}
      obj.periodStart = new Date(startDate.setDate(startDate.getDate() + increment * i))
      obj.periodEnd = new Date(endDate.setDate(endDate.getDate() + increment * i))
      obj.periodPayday = new Date(payDate.setDate(payDate.getDate() + increment * i))
      payPeriods.push(obj)
      reportEnd = obj.periodEnd
      i++
    }
  } else if (period === PeriodEnum.OncePerMonth) {
    var start1 = new Date(end1)
    start1.setDate(start1.getDate() + 1)
    var reportEnd = 0
    var i = 0
    while (reportEnd < today) {
      var startMonth: any = end1.getMonth()
      var subMonth: any = startMonth - 1
      var startDate: any = start1.getDate()
      // if pay period ends on last day of current month, set endDate to 31 to choose each last day
      if (daysInMonth(end1.getFullYear(), startMonth) === end1.getDate()) {
        var endDate: any = 31
      } else {
        var endDate = end1.getDate()
      }
      var payDate: any = Number(payday1)
      var payMonth = startMonth
      if (payDate <= endDate && payMonth === startMonth) {
        payMonth++
      }
      var obj: any = {}
      var thisMonthDays: any = daysInMonth(end1.getFullYear(), startMonth + i)
      //on this loop, if there are less days in the month than the endDate, set to days in month
      if (thisMonthDays < endDate) {
        endDate = thisMonthDays
      }
      //if
      if (thisMonthDays === endDate) {
        subMonth++
      }
      // if there are less days in month than the payDate, set payDate to days in month
      if (thisMonthDays < payDate) {
        payDate = thisMonthDays
      }
      obj.periodStart = new Date(end1.getFullYear(), subMonth + i, startDate, 0, 0, 0, 0)
      obj.periodEnd = new Date(end1.getFullYear(), startMonth + i, endDate, 23, 59, 59, 999)
      obj.periodPayday = new Date(end1.getFullYear(), payMonth + i, payDate, 0, 0, 0, 0)
      payPeriods.push(obj)
      reportEnd = obj.periodEnd
      i++
    }
  } else if (period === PeriodEnum.TwicePerMonth) {
    //FIRST GET DATES OF THE MONTH
    payday1 = Number(payday1)
    end2 = new Date(end2 + 'T23:59:59.999')
    payday2 = Number(payday2)
    var payDate1 = payday1
    var payDate2 = payday2
    let endDate1
    if (daysInMonth(end1.getFullYear(), end1.getMonth()) === end1.getDate()) {
      endDate1 = 31
    } else {
      endDate1 = end1.getDate()
    }
    let endDate2 = end2.getDate()
    if (daysInMonth(end2.getFullYear(), end2.getMonth()) === end2.getDate()) {
      endDate2 = 31
    } else {
      endDate2 = end2.getDate()
    }

    var startDate1: any = new Date(end2)
    startDate1.setDate(startDate1.getDate() + 1)
    startDate1 = startDate1.getDate()
    var startDate2: any = new Date(end1)
    startDate2.setDate(startDate2.getDate() + 1)
    startDate2 = startDate2.getDate()
    var startMonth = end1.getMonth() // Jan
    var startYear = end1.getFullYear()
    var reportEnd = 0
    var i = 0
    while (reportEnd < today) {
      var thisMonthDays: any = daysInMonth(startYear, startMonth + i)
      var nextMonthDays = daysInMonth(startYear, startMonth + 1 + i)
      //Create variables for the loop to prevent changing the presets
      var s1 = startDate1
      var s2 = startDate2
      var e1 = endDate1
      var e2 = endDate2
      var p1 = payDate1
      var p2 = payDate2
      var s1Month = startMonth
      var e1Month = startMonth + 1
      var p1Month = startMonth + 2
      var obj: any = {}
      obj.periodStart = new Date(startYear, s1Month + i, s1, 0, 0, 0, 0)
      if (thisMonthDays < e1) {
        e1 = thisMonthDays
      }
      if (e1 > s1) {
        e1Month--
        p1Month--
      }
      obj.periodEnd = new Date(startYear, e1Month + i, e1, 23, 59, 59, 999)
      if (thisMonthDays < p1) {
        p1 = thisMonthDays
      }
      //if paydate is greater than the endDate, subtract a month
      if (payDate1 > endDate1) {
        p1Month--
      }
      obj.periodPayday = new Date(startYear, p1Month + i, p1, 0, 0, 0, 0)
      payPeriods.push(obj)
      // if (startDate1 < startDate2) {
      //     startMonth --;
      // }
      var obj2: any = {}
      if (nextMonthDays < s2) {
        s2 = nextMonthDays
      }
      s1Month++
      if (s2 < e1) {
        e1Month++
      }
      obj2.periodStart = new Date(startYear, s1Month + i, s2, 0, 0, 0, 0)
      if (nextMonthDays < e2) {
        e2 = nextMonthDays
      }
      obj2.periodEnd = new Date(startYear, e1Month + i, e2, 23, 59, 59, 999)
      if (nextMonthDays < p2) {
        p2 = nextMonthDays
      }
      if (payDate2 < endDate2) {
        p1Month++
      }
      obj2.periodPayday = new Date(startYear, p1Month + i, p2, 0, 0, 0, 0)
      payPeriods.push(obj2)
      reportEnd = obj2.periodEnd
      i++
    }
  }
  return payPeriods.reverse()
}

export function checkAndReturnValue(number: any) {
  if (Number.isFinite(number)) {
    return number
  } else {
    return 0
  }
}

export function startOfYesterday() {
  const d = new Date()
  const start = new Date(d.setHours(0, 0, 0, 0))
  start.setDate(start.getDate() - 1)
  return start.toISOString()
}

export function endOfToday() {
  const d = new Date()
  const end = new Date(d.setHours(23, 59, 59, 999))
  return end.toISOString()
}

// export function startOfDate(date: any) {
//   const d = new Date(`"${date}"`)
//   console.log(d, 'jnkjsaad', `"${date}"`)
//   const start = new Date(d.setHours(0, 0, 0, 0))
//   return start.toISOString()
// }

// export function endOfDate(date: any) {
//   const d = new Date(`"${date}"`)
//   const end = new Date(d.setHours(23, 59, 59, 999))
//   return end.toISOString()
// }

export function startOfDate(date: any) {
  const d = new Date(date)
  const offset = d.getTimezoneOffset()
  const finalDate = new Date(d.getTime() + offset * 60000)
  const start = new Date(finalDate.setHours(0, 0, 0, 0))
  return start.toISOString()
}

export function endOfDate(date: any) {
  const d = new Date(date)
  const offset = d.getTimezoneOffset()
  const finalDate = new Date(d.getTime() + offset * 60000)
  const end = new Date(finalDate.setHours(23, 59, 59, 999))
  return end.toISOString()
}
// ============= New =============

export const nextAction = (card: any) => {
  const opp = card
  if (!opp?.nextAction?.due) return ''

  const dueDate = dayjs(opp?.nextAction?.due)
  const today = dayjs()

  const diff = dueDate.diff(today, 'minute')
  const daysDiff = diff / (60 * 24) // Convert minutes to float days

  if (daysDiff >= 30) {
    return 'pastelPurple' // 30+ days away
  } else if (daysDiff >= 7 && daysDiff < 30) {
    return 'pastelPurple' // 1 week to 30 days ahead
  } else if (daysDiff >= 1 && daysDiff < 7) {
    return 'pastelBlue' // Less than 1 week ahead
  } else if (daysDiff >= 0 && daysDiff < 1) {
    return 'pastelGreen' // Due within next 24 hours
  } else if (daysDiff >= -1 && daysDiff < 0) {
    return 'pastelYellow' // Overdue by up to 1 day
  } else if (daysDiff >= -7 && daysDiff < -1) {
    return 'pastelOrange' // Overdue by 1 to 7 days
  } else if (daysDiff < -7) {
    return 'softPink' // Overdue by more than 1 week
  }

  return ''
}
export const getDataFromLocalStorage = (key: string) =>
  localStorage.getItem(key) ? JSON.parse(localStorage.getItem(key) ?? '') : ''
export const splitFullName = (fullName: string) => {
  const nameArray = fullName?.trim()?.split(/\s+/) // Split the full name by spaces
  let firstName = nameArray[0] // First word is considered the first name
  let lastName = nameArray?.slice(1)?.join(' ') // Rest of the words are considered the last name

  // If there's only one word, consider it the first name and set last name to an empty string
  if (nameArray.length === 1) {
    lastName = ''
  }

  return { firstName, lastName }
}

export const dayjsFormat = (date: any, format: string) => (date ? dayjs(date).format(format) : '')

export const isWithinHour = (createdAt: string) => {
  // Check if the difference between now and createdAt is within 1 hour
  return dayjs().diff(dayjs(createdAt), 'hour') < 1
}

export const dedupeArray = (array: any) => {
  if (!array) return []
  return array.filter(function (item: any, index: number) {
    return array.indexOf(item) >= index
  })
}

export const extractLocalTime = (dateString: string): string | null => {
  const parsedDate = dayjs(dateString)

  if (parsedDate.isValid()) {
    // Format the local time as 'HH:mm:ss'
    const formattedTime: string = parsedDate.format('HH:mm')

    return formattedTime
  } else {
    // Handle invalid input date
    return null
  }
}

export const formatNumber = (value: any) => {
  if (isNaN(value) || value === undefined) {
    return '--'
  }
  return parseFloat(value).toFixed(2)
}

export const formatCurrency = (val: string | number) => Number(Number(val).toFixed(2)).toLocaleString()

// export const toPascalCase = (inputString: string) => {
//   // Split the input string into words
//   const words = inputString?.split(/\s+/) || ''

//   // Capitalize the first letter of each word and join them back
//   const pascalCaseString = words
//     .map((word: any) => {
//       // Capitalize the first letter of the word and concatenate with the rest of the word
//       return word.charAt(0).toUpperCase() + word.slice(1)
//     })
//     .join('')

//   return pascalCaseString
// }
export const calculateDiscountPercentage = (discountAmount: number, totalAmount: number): string => {
  if (totalAmount === 0) return '--'
  const percentage = (discountAmount / totalAmount) * 100
  return `${percentage.toFixed(1)}%`
}

export const toPascalCase = (inputString: string) => {
  if (!inputString) return ''

  // Split the input string into words including spaces
  const words = inputString?.split(/(\s+)/) || ''

  // Capitalize the first letter of each word and join them back
  const pascalCaseString = words
    .map((word: string) => {
      // If the word is a space, return it as is
      if (/\s/.test(word)) {
        return word
      }
      // Capitalize the first letter of the word and concatenate with the rest of the word
      return word.charAt(0).toUpperCase() + word.slice(1)
    })
    .join('')

  return pascalCaseString
}

export const formatNumberToCommaS = (number: number | string | null | undefined): string => {
  if (number === null || number === undefined) {
    return '--' // Handle null or undefined values if needed
  }

  // Convert to number if it's a string
  const numericValue = typeof number === 'string' ? parseFloat(number) : number

  if (isNaN(numericValue)) {
    return '--' // Handle non-numeric values
  }

  // Format the number with commas and two decimal places
  const formattedNumber = numericValue.toLocaleString(undefined, {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })

  return formattedNumber
}

export const getWeekRange = (weeksToAdd: number): { start: string; end: string } => {
  let currentWeekStart = dayjs().startOf('week')
  currentWeekStart = currentWeekStart.add(weeksToAdd, 'week')
  const startOfWeek = currentWeekStart.startOf('week')
  const endOfWeek = startOfWeek.add(6, 'day').endOf('day')
  return {
    start: startOfWeek.format('MM/DD/YYYY'),
    end: endOfWeek.format('MM/DD/YYYY'),
  }
}

export const convertFilters = (
  input: Record<string, any[]>
): {
  filter: { filter: any[]; logic: 'AND' | 'OR' }
} => {
  const mergedFilters = Object.values(input).flat()
  return {
    filter: {
      filter: mergedFilters,
      logic: 'AND',
    },
  }
}

export const simplifyBackendError = (error: any) => {
  if (typeof error === 'string') {
    // If the error is a string, return it as is
    return error
  } else if (Array.isArray(error)) {
    // If the error is an array, join its elements into a string
    return error.join(', ')
  } else {
    // For other cases, return a default error message
    return 'An error occurred'
  }
}

export const round1 = (num?: any) => (num ? Math.round(num * 10) / 10 : 0)

export const truncateParagraph = (paragraph: string, numCharacters: number) => {
  // Ensure numCharacters is within the valid range
  try {
    if (numCharacters >= paragraph.length) {
      return paragraph // Return the whole paragraph if numCharacters exceeds the total characters
    }

    // Extract the substring up to the specified character limit
    const truncatedParagraph = paragraph.slice(0, numCharacters)

    // Append an ellipsis to the truncated string
    return truncatedParagraph + '...'
  } catch (error) {
    return '---'
  }
}

export const truncateString = (name: string, maxLength: number) => {
  return maxLength >= name?.length ? name : `${name?.slice(0, maxLength)}...`
}

export const allValuesTrue = (arr: boolean[]): boolean => {
  return arr.every((value) => value === true)
}

export const filterUndefinedAndNull = (obj: any, addNullCheck?: boolean, addEmptyStringCheck?: boolean) => {
  const filteredObj = {} as { [key: string]: any }

  for (const key in obj) {
    const nullCheck = addNullCheck ? obj[key] !== null : true
    const emptyStringCheck = addEmptyStringCheck ? obj[key] !== '' : true
    if (obj[key] !== undefined && nullCheck && emptyStringCheck) {
      filteredObj[key] = obj[key]
    }
  }

  return filteredObj
}

export const hasValues = (obj: any) => {
  return obj !== null && obj !== undefined && Object.keys(obj).length > 0
}

export const moveCrewLeadFirst = (arr: any) => {
  // Find the index of the element with crewLead: true
  const index = arr.findIndex((item: any) => item?.crewLead === true)

  if (index !== -1) {
    const crewLeadElement = arr.splice(index, 1)[0]
    arr.unshift(crewLeadElement)
  }

  return arr
}

export function sixMonthsAgo(date: Date) {
  const sixMonthsAgoDate = new Date(date)
  sixMonthsAgoDate.setMonth(sixMonthsAgoDate.getMonth() - 6)
  sixMonthsAgoDate.setUTCHours(0, 0, 0, 0)
  return sixMonthsAgoDate
}

export const sortByFixedIndex = (index: number, response: any) => {
  const sorted = response.slice().sort((a: any, b: any) => {
    if (a.orderNumber === index) return -1
    if (b.orderNumber === index) return 1
    return 0
  })

  return sorted
}

export const getTimeCardColor = (bgColor: string) => {
  switch (bgColor) {
    case `${TimeCardStatusEnum.Active}`:
      return '#fcebeb'
    case `${TimeCardStatusEnum.Unapproved}`:
      return '#fcfceb'
    case `${TimeCardStatusEnum.Lead_Approved}`:
      return '#ebecfc'
    case `${TimeCardStatusEnum.Approved}`:
      return '#ebfceb'
    default:
      return 'transparent'
  }
}

export const extractPoInfo = (value: string, type: string) => {
  const [po, num] = value?.split('-')

  if (type === 'po') {
    return po
  } else if (type === 'num') {
    // Add leading zero if num is a single digit
    return num?.length === 1 ? `0${num}` : num
  }

  return ''
}

export const roundToNearestTenth = (number: number | string) => {
  if (!Number(number)) return 0
  return Math.round(Number(number) * 10) / 10
}

export const transformToNearestTenth = (num: number): number => {
  if (!num) return 0

  // Round to 2 decimal places
  const rounded = Math.round(num * 100) / 100

  // Extract the decimal part
  const decimalPart = rounded * 10 - Math.floor(rounded * 10)

  // Adjust to end in .x0 or .x5
  if (decimalPart < 0.25) {
    return Math.floor(rounded * 10) / 10 // Ends in .x0
  } else if (decimalPart < 0.75) {
    return Math.floor(rounded * 10 + 0.5) / 10 // Ends in .x5
  } else {
    return Math.ceil(rounded * 10) / 10 // Ends in .x0
  }
}

export const handleWheel = (e: any) => {
  e?.target?.blur()
}

export const filterTaskArray = (obj: any, data: any) => {
  const array: string[] = []
  obj?.forEach((item: any) => {
    Object?.keys(item)?.forEach((key) => {
      if (key?.includes('Id') && item[key] !== '') {
        array?.push(item[key])
      }
    })
  })

  const updated = data.filter((v: any) => !array.includes(v))
  return updated
}

export const filterMatchingIds = (noneObject: any, taskArray: string[]) => {
  const matchingIds: string[] = []
  taskArray?.forEach((id) => {
    if (noneObject?.some((item: any) => item?._id === id)) {
      matchingIds?.push(id)
    }
  })
  return matchingIds
}

export const formatDecimalHours = (hrs: number) => {
  const hourPart = Math.floor(hrs)
  const minutePart = Math.round((hrs - hourPart) * 60)
  return `${hourPart}h ${minutePart}m`
}

export const isLessThanSevenDays = (date: string) => {
  const currentDate = dayjs()
  const value = currentDate.diff(dayjs(date).format('YYYY-MM-DD'), 'day')
  return value < 8
}

export const transformObjectValues = (data: any, multiply: boolean) => {
  const transformedData: any = {}

  for (const key in data) {
    if (multiply) {
      transformedData[key] = Number((data[key] * 100)?.toFixed(2))
    } else {
      transformedData[key] = data[key] / 100
    }
  }

  return transformedData
}

export const isTextContainsWord = ({ text, word }: { text: string; word: string }) => {
  const regex = new RegExp(`\\b${word}\\b`)
  return regex.test(text)
}
export const generateUUID = () => {
  const cryptoObj = window.crypto
  if (!cryptoObj) {
    console.error('crypto API not supported')
    return null
  }

  const buffer = new Uint16Array(8)
  cryptoObj.getRandomValues(buffer)

  function pad4(num: any) {
    let str = num.toString(16)
    return '0000'.slice(str.length) + str
  }

  return (
    pad4(buffer[0]) +
    pad4(buffer[1]) +
    '-' +
    pad4(buffer[2]) +
    '-' +
    pad4(buffer[3]) +
    '-' +
    pad4(buffer[4]) +
    '-' +
    pad4(buffer[5]) +
    pad4(buffer[6]) +
    pad4(buffer[7])
  )
}

// export const filterAndReplaceDoubleQuotes = (inputArray: (string | number)[]): (string | number)[] => {
//   if (!inputArray) return [] // Handle null or undefined input

//   return inputArray?.map((item) => {
//     if (typeof item === 'string') {
//       return item?.replace(/"/g, '') // Remove double quotes from strings
//     } else {
//       return item // Return numbers as is
//     }
//   })
// }

export const updateArrays = (obj: any, key: string, toNumber: boolean, count: number) => {
  // if (count || count === 0) {
  const updatedArray = obj?.customData[key]
    ?.map((item: any) => item?.replace(/"/g, ''))
    ?.slice(0, count)
    ?.map((v: any) => (toNumber ? Number(v) : String(v)))
  obj.customData[key] = updatedArray
  // } else {
  //   const updatedArray = obj?.customData[key]
  //     ?.map((item: any) => {
  //       if (typeof item === 'string' && item.includes('"')) {
  //         return item.replace(/"/g, '')
  //       }
  //       return item
  //     })
  //     ?.map((v: any) => (toNumber ? Number(v) : String(v)))
  //   obj.customData[key] = updatedArray
  // }
}

export function sortByOrder(a: { order?: number }, b: { order?: number }): number {
  if (a.order !== undefined && b.order !== undefined) {
    return a.order - b.order
  } else if (a.order !== undefined) {
    return -1
  } else if (b.order !== undefined) {
    return 1
  } else {
    return 0
  }

  // const aOrder = a.order !== undefined ? a.order : Number.NEGATIVE_INFINITY;
  // const bOrder = b.order !== undefined ? b.order : Number.NEGATIVE_INFINITY;

  // return aOrder - bOrder;
}

// export const uuidToColor = (uuid: string) => {
//   // Validate the UUID
//   const uuidRegex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/
//   if (!uuidRegex.test(uuid)) {
//     return 'Invalid UUID'
//   }

//   // Remove the hyphens from the UUID
//   const hexStr = uuid.replace(/-/g, '')

//   // Extract different segments to form RGB values
//   const red = parseInt(hexStr.substring(0, 2), 16)
//   const green = parseInt(hexStr.substring(8, 10), 16)
//   const blue = parseInt(hexStr.substring(16, 18), 16)

//   // Ensure the values are in hex format and pad if necessary
//   const redHex = red.toString(16).padStart(2, '0')
//   const greenHex = green.toString(16).padStart(2, '0')
//   const blueHex = blue.toString(16).padStart(2, '0')

//   // Return the color code
//   return `#${redHex}${greenHex}${blueHex}`
// }
export const isTruthyOrZero = (value: any) => {
  return value === 0 || Boolean(value)
}

const googleMapsColors = [
  '#EA4335', // Red
  '#4285F4', // Blue
  '#34A853', // Green
  '#FBBC05', // Yellow
  '#FF9900', // Orange
  '#9B59B6', // Purple
  '#00AEEF', // Cyan
  '#DB4437', // Pink
]

// Function to calculate color difference using RGB components
const colorDifference = (c1: string, c2: string): number => {
  const r1 = parseInt(c1.substring(1, 3), 16)
  const g1 = parseInt(c1.substring(3, 5), 16)
  const b1 = parseInt(c1.substring(5, 7), 16)

  const r2 = parseInt(c2.substring(1, 3), 16)
  const g2 = parseInt(c2.substring(3, 5), 16)
  const b2 = parseInt(c2.substring(5, 7), 16)

  return Math.sqrt(Math.pow(r2 - r1, 2) + Math.pow(g2 - g1, 2) + Math.pow(b2 - b1, 2))
}
export const uuidToColor = (uuid: string): string => {
  // Validate the UUID
  const uuidRegex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/
  if (!uuidRegex.test(uuid)) {
    return 'Invalid UUID'
  }

  // Remove the hyphens from the UUID
  const hexStr = uuid.replace(/-/g, '')

  // Extract different segments to form RGB values
  let r = parseInt(hexStr.substring(0, 2), 16)
  let g = parseInt(hexStr.substring(8, 10), 16)
  let b = parseInt(hexStr.substring(16, 18), 16)

  const res = adjustColor(r, g, b)

  return `rgb(${res[0]}, ${res[1]}, ${res[2]})`
}

const adjustColor = (r: number, g: number, b: number): [number, number, number] => {
  // Calculate brightness (perceived brightness formula)
  let brightness = 0.299 * r + 0.587 * g + 0.114 * b

  // Force a darker color
  if (brightness > 150) {
    const factor = 150 / brightness // Scale down to keep proportions
    r = Math.floor(r * factor)
    g = Math.floor(g * factor)
    b = Math.floor(b * factor)
  }

  // Ensure no component is above 150 (to keep it dark)
  r = Math.min(r, 150)
  g = Math.min(g, 150)
  b = Math.min(b, 150)

  return [r, g, b]
}
export const isCustomTruthy = (value: any) => {
  return value === 0 || value === false || Boolean(value)
}

export const getObjectKeyByValue = (object: Record<string, unknown>, value: string) => {
  return Object.keys(object).find((key) => object[key] === value)
}

export const isValidURL = (url: string) => {
  try {
    const parsedUrl = new URL(url)
    return parsedUrl.protocol === 'http:' || parsedUrl.protocol === 'https:'
  } catch (e) {
    return false
  }
}

export const handlePlural = (value: string | number) => (+value === 1 ? '' : 's')

export function convertUnixTimestamp(timestamp: any) {
  // Convert the Unix timestamp to milliseconds
  const timestampInMillis = timestamp * 1000

  // Create a new Date object using the timestamp
  const date = new Date(timestampInMillis)

  // Get the components of the date
  const year = date.getFullYear()
  const month = date.toLocaleString('default', { month: 'short' }) // Get short month name
  const day = date.getDate()
  const hours = date.getHours()
  const minutes = date.getMinutes()

  // Format the date as 'day Month Year'
  const formattedDate = `${day} ${month} ${year}`

  // Format the time as 'hh:mm'
  const formattedTime = `${hours < 10 ? '0' : ''}${hours}:${minutes < 10 ? '0' : ''}${minutes}`

  // Return the formatted date and time as an object
  return {
    date: formattedDate,
    time: formattedTime,
  }
}

export const formatCurrencyToHandleDollar = (amount: number, decimalPlaces: number = 2): string => {
  // Format the number to the desired decimal places
  const formattedAmount = Math.abs(amount).toFixed(decimalPlaces)

  // Check if the number is positive or negative
  if (amount >= 0) {
    return `$${formattedAmount}`
  } else {
    return `-$${formattedAmount}`
  }
}

export function getEnumValue(stage: string): StageGroupEnum | undefined {
  // Check if the provided string matches any of the enum values
  if (Object.values(StageGroupEnum).includes(stage as StageGroupEnum)) {
    return stage as StageGroupEnum
  }
  // Return undefined if no match is found
  return undefined
}

export const formatDollarAmountWithIncrement = (value: number) =>
  Math.sign(value) === 1 ? `+$${formatNumberToCommaS(value)}` : `-$${formatNumberToCommaS(Math.abs(value))}`
export const formatDollarAmount = (value: number) =>
  Math.sign(value) >= 0 ? `$${formatNumberToCommaS(value)}` : `-$${formatNumberToCommaS(Math.abs(value))}`

export const resetDateToUTC = (date: string | Date) => {
  // Get UTC date from date to avoid any DST/timezone conversions
  return dayjsFormat(date, 'YYYY-MM-DD') + 'T00:00:00Z'
}

export const getDaysInMonthFromDate = (inputDate: any) => {
  const date = new Date(inputDate)
  const year = date.getFullYear()
  const month = date.getMonth()
  return new Date(year, month + 1, 0).getDate()
}

export const isDateDiffGreaterThan = (date1: string, date2: string, diffValue: number) => {
  const parsedDate1 = dayjs(date1)
  const parsedDate2 = dayjs(date2)

  const diffInDays = Math.abs(parsedDate1.diff(parsedDate2, 'day'))

  return diffInDays > diffValue
}

export const renderClientName = (isBusiness: boolean = false, fullName: string, businessName?: string) => {
  return (isBusiness ? businessName : fullName) || ''
}

export const getInitials = (name: string) => {
  if (!name) return ''

  const parts = name.trim().split(' ')
  const firstInitial = parts[0] ? parts[0][0].toUpperCase() : ''
  const lastInitial = parts[1] ? parts[1][0].toUpperCase() : ''

  return firstInitial + lastInitial
}

export const extractPermissionByName = (data: { permissions: any[] }, resourceName: string) => {
  for (const permission of data?.permissions) {
    const resource = permission.resources.find((res: { name: string }) => res.name === resourceName)
    if (resource) {
      return resource
    }
  }
  return null
}

export const getTrueKeys = (obj: Record<string, boolean>) => {
  return Object.keys(obj).filter((key) => obj[key] === true)
}

export const reverseToTrue = (keysArray: string[]) => {
  return keysArray.reduce((obj: Record<string, boolean>, key) => {
    obj[key] = true
    return obj
  }, {})
}

export function handleInputWithTwoDecimals(
  value: string,
  setFieldValue: (field: string, value: any) => void,
  fieldName: string
): void {
  if (value === '') {
    setFieldValue(fieldName, '')
    return
  }
  if (value && onlyTwoNumbersAfterDecimal.test(value)) {
    setFieldValue(fieldName, Number(value))
  }
}

export const formatCurrencyNumber = (amount: any, locale = 'en') => {
  return new Intl.NumberFormat(locale, {
    notation: 'compact',
    compactDisplay: 'short',
    maximumFractionDigits: 1,
  }).format(amount)
}

export const getFormattedRatio = (a: number, b: number) => {
  if (!a || !b) return '--'

  const decimalValue = (a / b).toFixed(2)
  const decimal = `${decimalValue}:1`

  return decimal
}

export const getPastelColorFromId = (str: string) => {
  if (!str) return '#CCCCCC'

  // Create a hash from the string
  let hash = 0
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash)
  }

  // Generate vibrant colors with good saturation
  // Use HSL color model for better control over brightness and saturation
  const h = Math.abs(hash) % 360 // Hue: 0-359 degrees on the color wheel
  const s = 65 + (Math.abs(hash >> 8) % 25) // Saturation: 65-90%
  const l = 65 + (Math.abs(hash >> 16) % 15) // Lightness: 65-80%

  return `hsl(${h}, ${s}%, ${l}%)`
}

export const countObjectDifferences = (obj1: Record<string, any>, obj2: Record<string, any>): number => {
  let diffCount = 0

  const allKeys = new Set([...Object.keys(obj1), ...Object.keys(obj2)])

  for (const key of allKeys) {
    const val1 = obj1[key]
    const val2 = obj2[key]

    // Compare using deep equality for objects/arrays
    const areEqual = JSON.stringify(val1) === JSON.stringify(val2)

    if (!areEqual) {
      diffCount++
    }
  }

  return diffCount
}

export const exportToCSV = (data: Record<string, any>[], filename: string): void => {
  if (!data || !data.length) {
    notify('No data to export', 'warning')
    return
  }

  // Get headers from the first object
  const headers = Object.keys(data[0])

  // Create CSV rows
  const csvRows = [
    // Header row
    headers.join(','),
    // Data rows
    ...data.map((row) =>
      headers
        .map((header) => {
          // Handle values that need quotes (contain commas, quotes, or newlines)
          const cell = row[header] !== null && row[header] !== undefined ? String(row[header]) : ''
          if (cell.includes(',') || cell.includes('"') || cell.includes('\n')) {
            return `"${cell.replace(/"/g, '""')}"`
          }
          return cell
        })
        .join(',')
    ),
  ]

  // Combine rows into a single string
  const csvString = csvRows.join('\n')

  // Create a blob and download link
  const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')

  // Set up and trigger download
  link.setAttribute('href', url)
  link.setAttribute('download', filename)
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

export function formatAddress(contact: { street?: string; city?: string; state?: string; zip?: string }): string {
  const parts = [contact.street, contact.city, contact.state, contact.zip]
    .filter(Boolean)
    .join(', ')
    .replace(/, ([^,]*)$/, ' $1')

  return parts.trim()
}
export const getOrdinal = (n: number) => {
  const s = ['th', 'st', 'nd', 'rd']
  const v = n % 100
  return n + (s[(v - 20) % 10] || s[v] || s[0])
}

export const getDueText = (dueDateStr: string) => {
  const now = dayjs()
  const due = dayjs(dueDateStr)

  const isPast = due.isBefore(now)
  const diffMs = Math.abs(due.diff(now))
  const dur = dayjs.duration(diffMs)

  const days = Math.floor(dur.asDays())
  const hours = dur.hours()
  const minutes = dur.minutes()

  const parts = [days ? `${days}d` : '', hours ? `${hours}h` : '', minutes ? `${minutes}m` : ''].filter(Boolean)

  return isPast ? `Past due ${parts?.join(', ')}` : `Due in ${parts?.join(' ')}`
}

export const getLastName = (fullName: string): string => {
  if (!fullName) return ''
  const parts = fullName.trim().split(/\s+/)
  return parts.length > 1 ? parts[parts.length - 1] : parts?.[0] || ''
}
