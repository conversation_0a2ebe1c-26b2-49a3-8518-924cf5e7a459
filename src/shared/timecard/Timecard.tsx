import { Fragment, memo } from 'react'
import { FlexCol, FlexRow } from '../../styles/styled'
import { TimeCardStatusEnum } from '../helpers/constants'
import { dayjsFormat, formatNumberToCommaS, getHoursAndMinutes, isTruthyOrZero } from '../helpers/util'
import * as Styled from './style'
import { SLoader } from '../components/loader/Loader'
import { hideDollarValue } from '../../modules/timeCard/timecardUtils'

interface WorkDone {
  name: string
  amount: number
  unit: string
  pitch?: number
  layers?: number
}

interface Work {
  earned?: number
  work?: {
    workDone?: WorkDone[]
    extraWorkTime?: string
  }
  removeLeadBonus?: boolean
}

interface TimeCard {
  active?: boolean
  status?: TimeCardStatusEnum | string
  projectPO?: string
  hrs?: number
  taskName?: string
  timeIn?: string
  timeOut?: string
  task?: string
  managerNotes?: string
  notes?: string
  work?: Work
  _id?: string
  ptoUsed?: boolean
}

interface Member {
  _id: string
  name: string
  positions: string[]
  date?: string
}
interface ITimecardProps {
  timeCard: TimeCard
  member?: Member
  renderApprove?: () => React.ReactNode
  onTimeCardClick?: (data: any) => void
  isReport?: boolean
  hideDollar?: boolean
  isLoading?: boolean
}

const Timecard = (props: ITimecardProps) => {
  const { timeCard, member, renderApprove, isReport, hideDollar, onTimeCardClick, isLoading } = props
  return (
    <Styled.TimeCardApproveNoApproveDiv
      onClick={() => (isLoading ? null : onTimeCardClick?.({ timeCard, name: member?.name, date: member?.date }))}
      className={timeCard?.active ? 'active' : ''}
      bgColor={timeCard?.status}
    >
      {timeCard?.task === 'Day_Off' ? (
        <>
          <FlexRow justifyContent="space-between" alignItems="flex-start">
            <FlexCol gap="4px">
              <div className="project date">Day Off</div>
              {timeCard?.ptoUsed ? <Styled.KeyValueSpan>PTO Used</Styled.KeyValueSpan> : null}
            </FlexCol>
            {isReport ? <div className="project date">{dayjsFormat(timeCard?.timeIn, 'M/D/YY')} </div> : null}
          </FlexRow>
        </>
      ) : (
        <>
          <Styled.TimeCardApproveNoApproveLeftDiv className={isReport ? 'report' : ''}>
            <FlexRow justifyContent="space-between">
              <Styled.KeyValueSpan>
                <span className="project">
                  {isLoading ? <SLoader style={{ display: 'inline-block' }} width={170} /> : timeCard?.projectPO}{' '}
                </span>
                {!isNaN(Number(timeCard?.hrs)) && !isLoading ? `${Number(timeCard?.hrs)?.toFixed(1)}h` : null}
              </Styled.KeyValueSpan>
              {isReport ? <div className="project date">{dayjsFormat(timeCard?.timeIn, 'M/D/YY')} </div> : null}
            </FlexRow>
            <Styled.KeyValueSpan>{isLoading ? <SLoader width={46} /> : timeCard?.taskName}</Styled.KeyValueSpan>
            <Styled.KeyValueSpan className="timeInOut">
              {isLoading ? (
                <SLoader width={154} height={30} />
              ) : (
                <>
                  {`${timeCard?.timeIn ? getHoursAndMinutes(timeCard?.timeIn) : ''} - ${
                    timeCard?.timeOut ? getHoursAndMinutes(timeCard?.timeOut) : ''
                  }`}
                </>
              )}
            </Styled.KeyValueSpan>

            {isLoading ? (
              <SLoader width={170} height={50} />
            ) : (
              <>
                <Styled.WorkDoneTimeCardContainer>
                  {timeCard?.work?.work?.workDone?.map((w: any, idx: number) => (
                    <Fragment key={idx}>
                      {w?.amount ? (
                        <Styled.WorkDoneTimeCardHeader className="work" key={idx}>
                          {w?.name}: {w?.amount} {w?.unit}
                          {isTruthyOrZero(w?.pitch) ? ` (${w?.pitch}/12${w?.layers ? `, ${w?.layers}L` : ''})` : null}
                        </Styled.WorkDoneTimeCardHeader>
                      ) : null}
                    </Fragment>
                  ))}
                </Styled.WorkDoneTimeCardContainer>

                {timeCard?.work?.work?.extraWorkTime ? (
                  <Styled.KeyValueSpan className="work">
                    Extra Hours: {timeCard?.work?.work?.extraWorkTime}
                  </Styled.KeyValueSpan>
                ) : null}
                {timeCard?.managerNotes ? (
                  <Styled.KeyValueSpan className="managerNotes">{timeCard?.managerNotes}</Styled.KeyValueSpan>
                ) : (
                  <div />
                )}
                {timeCard?.notes ? (
                  <Styled.KeyValueSpan className="note">{timeCard?.notes}</Styled.KeyValueSpan>
                ) : (
                  <div />
                )}

                <FlexRow justifyContent="space-between">
                  {timeCard?.work?.earned ? (
                    <Styled.KeyValueSpan className="dollar">
                      ${hideDollarValue(formatNumberToCommaS(Number(timeCard?.work?.earned)), hideDollar!)}
                    </Styled.KeyValueSpan>
                  ) : null}

                  {timeCard?.work?.removeLeadBonus && !renderApprove ? (
                    <span style={{ marginLeft: 'auto' }}>x</span>
                  ) : null}
                </FlexRow>
              </>
            )}
          </Styled.TimeCardApproveNoApproveLeftDiv>

          {isLoading ? (
            <SLoader width={30} height={30} skeletonStyle={{ marginLeft: 'auto', borderRadius: '50%' }} />
          ) : (
            renderApprove?.()
          )}
        </>
      )}
    </Styled.TimeCardApproveNoApproveDiv>
  )
}

export default memo(Timecard)
