import React, { useState, useEffect, useRef } from 'react'
import {
  DropDownOuterContainer,
  DropDownContainer,
  DropDownContentContainer,
  DropDownItem,
  DropDownLabel,
} from './style'
import useDebounce from '../hooks/useDebounce'
import { NormalInput } from '../normalInput/NormalInput'
import { isSuccess } from '../helpers/util'

interface SearchableDropdownProps {
  placeholder?: string
  searchFunction: (query: string) => Promise<any[]>
  onSelect: (item?: any) => void
  resultExtractor: (res: any) => any[]
  label?: string
  height?: string
  innerHeight?: string
  showAddOption?: boolean
  onAddClick?: any
  addNewText?: string
  displayKey: string | string[]
  selectedValue?: any
  className?: string
  hideDropdown?: boolean
  handleBlur?: (name?: string) => void
  refererOptions?: any[]
  showUnKnownOption?: boolean
  onUnKnownClick?: any
  onTextChange?: (text: string) => void
}

const SearchableDropdown: React.FC<SearchableDropdownProps> = ({
  placeholder = 'Type to search',
  searchFunction,
  onSelect,
  resultExtractor,
  label = 'Search',
  height,
  innerHeight,
  showAddOption,
  onAddClick,
  addNewText,
  displayKey,
  selectedValue,
  className,
  handleBlur,
  refererOptions,
  hideDropdown,
  showUnKnownOption,
  onUnKnownClick,
  onTextChange,
}) => {
  const [search, setSearch] = useState('')
  const [results, setResults] = useState<any[]>([])
  const [showDropdown, setShowDropdown] = useState(false)

  useEffect(() => {
    if (selectedValue) {
      setSearch(selectedValue)
    }
  }, [selectedValue])

  useEffect(() => {
    if (!search && refererOptions?.length) {
      setResults(refererOptions!)
    }

    if (onTextChange) {
      onTextChange(search)
    }
  }, [search])

  const debouncedSearch = useDebounce(search, 500)
  const dropdownRef = useRef<HTMLDivElement>(null)
  useEffect(() => {
    const runSearch = async () => {
      if (debouncedSearch.trim()) {
        try {
          const res = await searchFunction(debouncedSearch)
          if (isSuccess(res)) {
            const extracted = resultExtractor(res)
            setResults(extracted || [])

            if (refererOptions?.length || hideDropdown) {
              // setShowDropdown(true)
            } else {
              setShowDropdown(true)
            }
          }
        } catch (err) {
          console.error('Search error:', err)
        }
      } else {
        if (refererOptions?.length || hideDropdown) {
          //     setShowDropdown(false)
          // setResults([])
        } else {
          setShowDropdown(false)
          setResults([])
        }
      }
    }

    runSearch()
  }, [debouncedSearch])

  const handleSelect = (item: any) => {
    onSelect(item)
    setSearch('')
    setShowDropdown(false)
  }

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const getDisplayValue = (item: any, key: string | string[]): string => {
    if (Array.isArray(key)) {
      return key
        .map((k) => k.split('.').reduce((acc, subKey) => acc?.[subKey], item) || '')
        .join(' ')
        .trim()
    } else {
      return key.split('.').reduce((acc, subKey) => acc?.[subKey], item) || ''
    }
  }

  return (
    <DropDownOuterContainer className={className}>
      {/* <DropDownLabel>{label}</DropDownLabel> */}
      <DropDownContainer focus={showDropdown} height={innerHeight} ref={dropdownRef}>
        <NormalInput
          padding={'20px 40px 8px 16px'}
          labelName={label}
          stateName={''}
          twoInput={true}
          value={search}
          noMessage
          selectTextOnFocus
          onChange={(e) => setSearch(e.target.value)}
          onFocusCallback={() => {
            setShowDropdown(true)
          }}
          onTab={handleBlur}
        />
        <DropDownContentContainer height={height} $visibility={showDropdown}>
          {results?.length > 0 ? (
            results?.map((item, idx) => (
              <DropDownItem key={idx} onClick={() => handleSelect(item)}>
                {getDisplayValue(item, displayKey)}
              </DropDownItem>
            ))
          ) : (
            <DropDownItem noHover>No results</DropDownItem>
          )}
          {showUnKnownOption && (
            <DropDownItem
              fontWeight={700}
              onClick={() => {
                if (onUnKnownClick) {
                  onUnKnownClick()
                }
                setShowDropdown(false)
              }}
            >
              {'Unknown'}
            </DropDownItem>
          )}
          {showAddOption && (
            <DropDownItem
              fontWeight={700}
              onClick={() => {
                if (onAddClick) {
                  onAddClick()
                }
                setShowDropdown(false)
              }}
            >
              {addNewText ?? '+ Add New'}
            </DropDownItem>
          )}
        </DropDownContentContainer>
      </DropDownContainer>
    </DropDownOuterContainer>
  )
}

export default SearchableDropdown
